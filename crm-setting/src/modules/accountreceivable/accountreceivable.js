/**
 * @desc 应收管理
 */
define(function(require, exports, module) {
	var util = FS.crmUtil;
	var tpl = require('./template/tpl-html');//最外层
    var containerTpl  = require('./template/container-html');//应收管理
    var payContainer  = require('./template/pay-container-html');//应付管理
    var PayForm = require("./comps/payForm");
    var ReceiveForm = require("./comps/receiveForm");
    const RECEIVE_CUSTOM_KEYS = [
        // 期初日期设置
        {key: 'opening_balance_date_setting', component: require('./comps/OpeningBalanceDateSetting'), configKeys: ['opening_balance_date', 'opening_balance_force_check']},
        //预设核销方案
        {key: 'is_open_match', component: require('./comps/MatchStatus')},
        // 自动核销开关
        {key: 'is_open_auto_match', component: require('./comps/AutoMatchStatus'), configKeys: ['is_open_auto_match']},
        // 自动核销规则顺序
        {key: 'auto_match_rule', component: require('./comps/AutoMatchRuleOrder'), configKeys: ['auto_match_rule']},
        {key: 'create_ar_by_objects', component: require('./comps/CreateArByObjects'), configKeys: ['create_ar_by_objects', 'is_open_ar_quick_add']},
        // 自动应收
        {key: 'is_open_periodic_accounts_receivable', component: require('./comps/AutoCreateReveivable'), configKeys: ['is_open_periodic_accounts_receivable', 'periodic_accounts_receivable_type']},
        // 结算单多来源
        {key: 'settlement_detail_mapping_rule', component: require('./comps/SettlementDetailMappingRule'), configKeys: ['settlement_detail_mapping_rule']},
    ];

    var AccountReceivable = Backbone.View.extend({
    	initialize: function(opts) {
            this.setElement(opts.wrapper);
            this.el.innerHTML = tpl();
            this.$container = this.$('.container');
            this.PayForm = null;
            this.ReceiveForm = null;
            this.timer = null;
            this.SFAConfigValues = {};
    	},
        
        events: {
			'click .j-set-on': '_onSetAccountReceivable',
			'click .j-set-matchway': '_onSetMatchWay',
			'click .tab-item': '_onTabCh',
        },
        
        render: function() {
            var me = this;
            // me.getConfig();
            me.getbillControl();
        },
        //应付
        renderPayForm(config){
            let me = this;
            let el = me.$('.content-container')[0];
            let form = this.PayForm = new PayForm({
                el: el,
                propsData: config
            })
            if(config.count == 1) {
                form.$on('getStatus', params=>{
                    me.getPayConfig(params.count);
                })
            }
        },
        //应收
        renderReceiveForm(config){
            let me = this;
            let el = me.$('.receive-bill')[0];
            let form = this.ReceiveForm = new ReceiveForm({
                el: el,
                propsData: config
            })
            form.$on('getRules', params=>{
                this.getBillSwitch().then(openOBj=>{
                    form.updateRules(openOBj);
                })
            })
        },

        renderReceviceCustomSettingItem() {
            this.recevieCustomWidgets = {};
            const me = this;
            const getWrapper = (key) => (me.$el.find('.content-container .column-item.' + key)[0]);
            const afterCompChange = (key, value, flag) => {
                // 开启自动核销
                if (key === 'is_open_auto_match' && flag) {
                    const rKey = 'auto_match_rule';
                    const {component: rComponent} = RECEIVE_CUSTOM_KEYS.find(({key}) => key === rKey);
                    const rComp = renderItem(rKey, rComponent);
                    // 开启自动核销，设置默认值
                    if (value === '1') {
                        rComp?.setDefaultValue();
                    }
                }

                let comp = me.recevieCustomWidgets[key];
                if (!comp) {
                    let rKey = RECEIVE_CUSTOM_KEYS.find((item) => item.configKeys && item.configKeys.includes(key))?.key;
                    comp = me.recevieCustomWidgets[rKey];
                }
                comp?.changeComplete?.(key, value, flag);
            }
            const renderItem = (key, component) => {
                const wrapper = getWrapper(key);
                this.recevieCustomWidgets[key]?.destroy?.();
                if (wrapper && component) {
                    const comp = component(wrapper, key, this.SFAConfigValues);
                    comp?.$on('change', async (_key, _value) => {
                        let changeRst = true;
                        try {
                            await me.setSFAServiceConfig([{key: _key, value: _value}]);
                        } catch (error) {
                            changeRst = false;
                        }
                        afterCompChange(_key, _value, changeRst);
                    })
                    comp?.$on('update', async (_key) => {
                        await me.getSFAServiceConfig([_key]);
                        comp?.updateComplete?.(me.SFAConfigValues);
                    });
                    me.recevieCustomWidgets[key] = comp;
                    return comp;
                }
            }
            RECEIVE_CUSTOM_KEYS.forEach(({key, component}) => {
                renderItem(key, component);
            })
        },

        setSFAServiceConfig(params) {
            const me = this;
            return new Promise((resolve, reject) => {
                CRM.util.showLoading_tip();
                CRM.util.setConfigValues(params).then(
                    () => {
                        params.forEach(({key, value}) => {
                            me.SFAConfigValues[key] = value;
                        })
                        CRM.util.remind(1, $t("设置成功"));
                        CRM.util.hideLoading_tip();
                        resolve();
                    },
                    (msg) => {
                        CRM.util.remind(3, msg || $t("设置失败"));
                        CRM.util.hideLoading_tip();
                        reject();
                    }
                );
            })
        },

        getSFAServiceConfig(keys) {
            const me = this;
            if (!keys?.length) {
                keys = RECEIVE_CUSTOM_KEYS.reduce((acc, {configKeys = []}) => {
                    acc.push(...configKeys);
                    return acc;
                }, []);
            }
            return new Promise((resolve) => {
                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/biz_config/service/get_config_values',
                    data: {
                        isAllConfig: false,
                        keys,
                    },
                    success: function (res) {
                        if (res.Result.StatusCode == 0) {
                            res.Value.values.forEach(({key, value}) => {
                                me.SFAConfigValues[key] = value;
                            })
                        }
                        resolve(me.SFAConfigValues);
                    }
                })
            })
        },
        /**
         * @desc 查询应收管理相关开关配置
         */
        getConfig: function() {
			var me = this;
			var keys = ["accounts_receivable_status", "accounts_receivable_match_way"]
			return new Promise((resolve,reject)=>{
                util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/accounts_receivable/service/get_config',
                    data: { keys },
                    success: function (res) {
                        var result = res.Result;
                        if (result.StatusCode == 0) {
                            var list = res.Value.values || []
                            var accountReceivableStatus = null // 应收开启状态：0未开启, 1开启中, 2已经开启, 3开启失败
                            var matchWay = 1 // 核销方式
    
                            _.each(list, function(item) {
                                switch (item.key) {
                                    case 'accounts_receivable_status':
                                        accountReceivableStatus = item.value
                                        break;
                                    case 'accounts_receivable_match_way':
                                        matchWay = item.value
                                        break;
                                    default: break;
                                }
                            });
                            resolve({
                                account_receivable_status: accountReceivableStatus,
                                match_way: matchWay,
                            })
                        } else {
                            resolve({
                                account_receivable_status: null,
                                match_way: 1,
                            })
                            util.alert(result.FailureMessage || result.message || $t("设置失败请联系纷享客服"));
                        }
                    }
                }, {
                    errorAlertModel: 1
                });
            })
		},
        //账期控制-总
        getbillControl(){
            let me = this;
            this.showLoading();
            Promise.all([this.getConfig(),this.getBillConfig(),this.getBillSwitch(),this.getSFAServiceConfig()]).then(resList=>{
                console.log('getbillControl____',resList);
                let config = resList.length > 0 ? resList[0] : {};
                let objects = resList.length > 1 ? resList[1] : [];
                let openOBj = resList.length > 2 ? resList[2] : {};
                $('.mn-radio-box', me.$el).html(containerTpl({
                    ...config,
                    recevieCustomKeys: RECEIVE_CUSTOM_KEYS,
                }) );
                me.renderReceiveForm({
                    objects,
                    billOpen: openOBj.billOpen,
                    dataPrevList: openOBj.objects,
                })
                me.renderReceviceCustomSettingItem();
                me.hideLoading();
            })
        },
        /**
         * 应收管理-账期控制-获取已保存配置
         */
         getBillSwitch(){
            return new Promise((resolve,reject)=>{
                let me = this;
                util.FHHApi({
                    url: '/EM1HNCRM/FMCG/API/DMS/AccountsPeriodConfig/GetAccountsPeriodConfig',
                    data: {},
                    success: function (res) {
                        console.log('getPrevBillConfig___',res);
                        let result = res.Result;
                        if (result.StatusCode == 0) {
                            let open = res.Value.open || false;
                            let objects = res.Value.objects || [];
                            resolve({billOpen: open,objects});
                        } else {
                            resolve({
                                billOpen: false,
                                objects: [],
                            })
                            util.alert(result.FailureMessage || result.message || $t("查询失败"));
                        }
                    },
                    complete: function() {
                        me.hideLoading();
                    },
                }, {
                    errorAlertModel: 1
                });
            })
        },
        /**
         * 应收管理-账期控制-下拉选项
         */
        getBillConfig(){
            return new Promise((resolve,reject)=>{
                let me = this;
                util.FHHApi({
                    url: '/EM1HNCRM/FMCG/API/DMS/AccountsPeriodConfig/GetObjectInitializationInformation',
                    data: {},
                    success: function (res) {
                        console.log('getBillConfig___',res);
                        let result = res.Result;
                        if (result.StatusCode == 0) {
                            let list = res.Value.objects || [];
                            resolve(list);
                        } else {
                            util.alert(result.FailureMessage || result.message || $t("查询失败"));
                        }
                    },
                    complete: function() {
                        me.hideLoading();
                    },
                }, {
                    errorAlertModel: 1
                });
            })
        },
        /**
         * @desc 查询应付管理相关开关配置
         * count  1-初始化调用 2-开启开关后第一次调用 3-开启中定时调用
         */
        getPayConfig(count) {
            let me = this;
            let { timer } = this;
            if(count == 3 && timer == null) return;
            util.FHHApi({
                url: '/EM1HNCRM/FMCG/API/DMS/AccountsPayable/GetStatus',
                data: {},
                success: function (res) {
                    console.log('getPayConfig___',res);
                    let result = res.Result;
                    if (result.StatusCode == 0) {
                        let account_pay_status = res.Value.status; // 应收开启状态：0未开启, 1开启中, 2已经开启, 3开启失败
                        if(account_pay_status == 1) {
                            me.timer = setTimeout(()=>{
                                count != 3 && util.alert($t("正在开启中，请耐心等待"));
                                me.getPayConfig(3);
                            },5000);
                        }else if(account_pay_status == 2){
                            me.timer = null;
                            (count == 2 || count == 3 ) && CRM.util.remind(1, $t("开启成功"));
                        }else if(account_pay_status == 3){
                            me.timer = null;
                            CRM.util.remind(1, $t("crm.manage.accountreceivable.pay.open_fail_tip"));
                        }
                        $('.mn-radio-box', me.$el).html(payContainer({
                            account_pay_status: account_pay_status,
                            isOpen: account_pay_status == 2 ? true : false,
                        }) );
                        me.renderPayForm({
                            account_pay_status: account_pay_status,
                            isOpen: account_pay_status == 2 ? true : false,
                            count,
                        })
                        me.PayForm.updateStatus(account_pay_status);
                    } else {
                        util.alert(result.FailureMessage || result.message || $t("查询失败"));
                    }
                },
                complete: function() {
                    me.hideLoading();
                },
            }, {
                errorAlertModel: 1
            });
        },
		/**
		 * @desc 开启客户账户确认弹窗
		 */
        _onSetAccountReceivable: function(e) {
            var me = this;
			var confirm;
			confirm = util.confirm($t("应收管理一旦启用将无法停用确定要启用吗"), $t("提示"), function() {
				confirm.destroy();
				me.enableAccountReceivable();
			});
		},
        showLoading: function (msg) {
            FS.crmUtil.waiting(msg || '');
        },
        hideLoading: function () {
            FS.crmUtil.waiting(false);
        },
        /**
         * @desc 启用应收管理
         */
        enableAccountReceivable: function() {
            var me = this;
            var enableStatus;
            var message = $t("设置失败请联系纷享客服");

            util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/accounts_receivable/service/enable_accounts_receivable',
                data: {},
                success: function (res) {
                    var result = res.Result;
                    if (result.StatusCode === 0) {
                        enableStatus = res.Value.enableStatus;
                        if (enableStatus === 0 || enableStatus === 3) {
                            util.alert(res.Value.message || message);
                        } else {
							util.remind(1, $t("设置成功"));
							
                            // me.getConfig();
                            me.getbillControl();
							if (enableStatus === 1) { // 开启中
								util.alert($t("crm.系统检测客户太多系统预设在次日凌晨开启"));
							}

                            CRM.control.refreshAside(); // 刷新左侧导航
                        }
                    } else {
                        util.alert(result.FailureMessage || message);
                    }
                }
            }, {
                errorAlertModel: 1
            });
        },

		/**
         * @desc 设置核销方式
         */
		_onSetMatchWay: function(e) {
			var me = this;
			var $target = $(e.currentTarget);
			var value = $target.data('value');
			if ($target.hasClass('mn-selected')) return;

			util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/accounts_receivable/service/update_config',
                data: {
					key: 'accounts_receivable_match_way',
					value: parseInt(value) // (1, 应收单核销), (2, 应收单明细核销)
				},
                success: function (res) {
                    var result = res.Result;
                    if (result.StatusCode === 0) {
                        $('.matchway-radio-item', me.$el).removeClass('mn-selected');
						$target.addClass('mn-selected');
						util.remind(1, $t("设置成功"));
                    } else {
                        util.alert(result.FailureMessage || $t("设置失败请联系纷享客服"));
                    }
                }
            }, {
                errorAlertModel: 1
            });
		},

        /**
         * @desc tab切换
         */
        _onTabCh: function(e){
            console.log('_onTabCh___',e);
            let $target = $(e.currentTarget);
            $target.siblings().removeClass('active');
            $target.addClass('active');
            
            let prop = e && e.currentTarget && e.currentTarget.dataset && e.currentTarget.dataset.prop || null;
            if(prop == 'receive') {
                this.showLoading()
                // this.getConfig();
                this.getbillControl();
                this.destroy('pay');
            }else if(prop == 'pay') {
                this.showLoading()
                this.getPayConfig(1);
            }
        },
        destroyPayForm(){
            this.PayForm && this.PayForm.$off && this.PayForm.$off();
            this.PayForm && this.PayForm.$destroy && this.PayForm.$destroy();
            this.PayForm && this.PayForm.$el.remove && this.PayForm.$el.remove();
            this.PayForm = null;
        },
        destroy: function(prop) {
            console.log('destroyed------',this.PayForm)
            if(prop == 'pay') {
                this.destroyPayForm();
            }else {
                this.destroyPayForm();
                this.ReceiveForm && this.ReceiveForm.$off && this.ReceiveForm.$off();
                this.ReceiveForm && this.ReceiveForm.$destroy && this.ReceiveForm.$destroy();
                this.ReceiveForm && this.ReceiveForm.$el.remove && this.ReceiveForm.$el.remove();
                this.ReceiveForm = null;
                
                this.timer = null;
            }
            Object.values(this.recevieCustomWidgets || {}).forEach((comp) => comp?.destroy());
        },
		
    });

    module.exports = AccountReceivable;
});
