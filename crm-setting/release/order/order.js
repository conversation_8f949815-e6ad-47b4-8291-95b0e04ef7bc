define("crm-setting/order/account/account",["../common/table","../util","crm-modules/common/util","../api","./template/tpl-html","../module","./dt-config","../dialog/bankaccount","./table","./dialog"],function(e,t,n){e("../common/table");var s=e("../util"),c=e("crm-modules/common/util"),r=e("../api"),o=e("./template/tpl-html"),i=e("../module"),l=(e("./dt-config"),e("../dialog/bankaccount"),e("./table")),a=e("./dialog"),d="PaymentAccountObj",u=i.extend({name:"CRM-ORDER-BANKACCOUNT",events:{"click .j-add":"_onAdd","click .j-create":"_onAdd","click .j-switch-online":"_onlineSwitch","click .j-switch-offline":"_offlineSwitch"},initialize:function(e){u.__super__.initialize.call(this,e),this.$el.html(o()),this.isOnlyISV(),this.getVaildISV(),this.getOnlinePay(),this.initTable()},isOnlyISV:function(){var t=this;c.FHHApi({url:"/EM1APAY/PAYWallet/isMchOnlyISVForWeb",data:{merchantCode:"**************",wareId:"1005"},success:function(e){if(0===e.Result.StatusCode)return 0===e.Value.errorCode?(t.$$state.isSupport=1===e.Value.mchOnlyISV,!t.$$state.isSupport&&$(".account-online-qy",t.element).css("display","inline-block")&&$(".account-tip",t.element).css("display","block"),void(t.$$state.payType&&t.$$state.payType.length&&$(".account-tip",t.element).css("display","none"))):void s.error(e.Value.errorMessage);s.error(e.Result.FailureMessage)}},{errorAlertModel:1})},getVaildISV:function(e){var t=this;c.FHHApi({url:"/EM1APAY/PAYWallet/queryValidISVListForWeb",data:{merchantCode:"**************",wareId:"1005"},success:function(e){if(0===e.Result.StatusCode)return 0===e.Value.errorCode?void(t.$$state.payType=e.Value.validISVInfoList||[]).forEach(function(e){$(".account-tip",t.element).css("display","none"),1===e.type&&$(".status-wx",t.element).html($t("已接入"))&&$(".account-online-wx",t.element).addClass("s-opened"),2===e.type&&$(".status-zfb",t.element).html($t("已接入"))&&$(".account-online-zfb",t.element).addClass("s-opened")}):void s.error(e.Value.errorMessage);s.error(e.Result.FailureMessage)}},{errorAlertModel:1})},initTable:function(){var n=this;n.$$components.$table=new l({el:n.$el.find(".account-offline-list"),apiname:d,showTitle:!1,isFilter:!1,isRenderRecordType:!1,showFilerBtn:!1,showTerm:!1,showManage:!1,searchTerm:null}),n.$$components.$table.on("onitemclick",function(e,t){n.$$components.$dialog||(n.$$components.$dialog=new a,n.$$components.$dialog.on("createOrUpdate",function(e){n.$$components.$table.table.refresh()}),n.$$components.$dialog.on("hide",function(){n.$$components.$dialog.destroy(),n.$$components.$dialog=null})),n.$$components.$dialog.show(e,t)}),n.$$components.$table.on("onitemdelete",function(e){var t=c.confirm($t("确认要删除该数据吗？"),$t("提示"),function(){t.destroy(),r.deleteObject({describe_api_name:d,idList:[e._id]}).done(function(e){0!==e.Result.StatusCode?FS.util.error(e.Result.FailureMessage||$t("暂时无法删除数据!")):(FS.util.remind(1,$t("删除成功")),n.$$components.$table.table.refresh())})})}),n.$$components.$table.render()},getOnlinePay:function(){var t=this;r.getContactAndLogo().done(function(e){0===e.errorCode&&(2===(e=e.data).onlinePaySwitch?$(".online-switch",t.element).removeClass("on"):$(".online-switch",t.element).addClass("on"),2===e.offlinePaySwitch?$(".offline-switch",t.element).removeClass("on"):$(".offline-switch",t.element).addClass("on"))}).complete(function(){})},_onlineSwitch:function(e){var t,n,o="on",i="",l="",a=$(e.currentTarget);a.hasClass("on")||!this.$$state.isSupport||this.$$state.payType.length?(o=a.hasClass("on")?(i=$t("确定要关闭线上支付通道吗"),l=$t("关闭成功"),"close"):(i=$t("确认要打开线上支付通道吗"),l=$t("开启成功"),"on"),n=s.confirm(i,$t("提示"),function(){n.hide(),r.updateOnlinePaySwitch({paySwitch:o}).done(function(e){e&&0===e.errorCode?(a.toggleClass("on"),s.remind(l)):s.error($t("设置失败请稍后再试"))})})):(e={btnLabel:{confirm:$t("前往接入"),cancel:$t("关闭")},stopPropagation:!1},i='<div class="crm-s-order-account-online-dialog">'+$t("crm.暂不允许开启在线支付")+'<br/><a class="look-guide" href="https://www.fxiaoke.com/mob/guide/flearning/zhifu/qianbao/%E6%8E%A5%E5%85%A5%E7%AC%AC%E4%B8%89%E6%96%B9%E8%B4%A6%E6%88%B7.html" target="_blank">查看接入教程</a></div>',t=c.confirm(i,$t("提示"),function(){t.hide(),window.location.hash="#app/entwallet/wallet"},e))},_offlineSwitch:function(e){var t="on",n="",o="",i=$(e.currentTarget),t=i.hasClass("on")?(n=$t("确定要关闭线下支付通道吗"),o=$t("关闭成功"),"close"):(n=$t("确认要打开线下支付通道吗"),o=$t("开启成功"),"on"),l=s.confirm(n,$t("提示"),function(){l.hide(),r.updateOfflinePaySwitch({paySwitch:t}).done(function(e){e&&0===e.errorCode?(i.toggleClass("on"),s.remind(o)):s.error($t("设置失败请稍后再试"))})})},_onAdd:function(){var t=this;t.$$components.$dialog||(t.$$components.$dialog=new a,t.$$components.$dialog.on("createOrUpdate",function(e){t.$$components.$table.table.refresh()}),t.$$components.$dialog.on("hide",function(){t.$$components.$dialog.destroy(),t.$$components.$dialog=null})),t.$$components.$dialog.show("create",null)}});n.exports=u});
define("crm-setting/order/account/dialog",["crm-widget/dialog/dialog","../api","../util","crm-modules/common/util","./view"],function(t,e,i){var a=t("crm-widget/dialog/dialog"),n=t("../api"),l=t("../util"),o=(t("crm-modules/common/util"),t("./view")),d=a.extend({attrs:{content:'<div class="crm-s-order-paymentaccount-dialog j-form"></div>',title:$t("收款账号"),width:1e3,height:600,showBtns:!0,showScroll:!0},events:{"click .b-g-btn-cancel":"hide","click .b-g-btn":"_submit"},initialize:function(t){var e=Array.prototype.slice.call(arguments,1);e.unshift(t),d.superclass.initialize.apply(this,e)},_submit:function(){var t,e=this;l.loading($(".b-g-btn",e.element),!0)||(t=e.$view.collect(),e.data&&e.data._id&&(t=_.extend(e.data,t)),n.createOrUpdateObject({object_data:t}).done(function(t){0!==t.Result.StatusCode?FS.util.error(t.Result.FailureMessage||$t("暂时无法编辑数据！")):(FS.util.remind(1,$t("保存成功")),e.data?_.extend(e.data,t.Value.objectData):e.data=t.Value.objectData,e.trigger("createOrUpdate",e.data),e.hide())}).complete(function(){l.loading($(".b-g-btn",e.element),!1)}))},_initView:function(){var t=this;t.$view&&t.$view.destroy(),t.$view=new o({el:t.$(".j-form"),data:t.data,apiname:"PaymentAccountObj",include_detail_describe:!0,record_type:"default__c",show_type:"full",showNav:!1,showScroll:!1,isWM:!0,hideFields:["owner","data_own_department"]}),t.$view.render()},show:function(t,e){var i=this,a=d.superclass.show.call(this),n=$(".b-g-btn",i.element);return"view"===t?n.hide():n.show(),i.action=t,i.data=e,i._initView(),a},hide:function(){return d.superclass.hide.call(this)},destroy:function(){return this.$$data=null,d.superclass.destroy.call(this)}});i.exports=d});
define("crm-setting/order/account/dt-config",[],function(t,a,e){var n={url:"/FHH/EM1HSailAdmin/sail-admin/paymentAccount/list",requestType:"define",requestCodeKey:"errorCode",method:"post",height:"auto",showPage:!1,showMoreBtn:!1,showFilerBtn:!1,operate:{pos:"T",btns:[{text:$t("新增"),className:"j-add"}]},paramFormat:function(t){return t},columns:[{data:"Name",title:$t("账号名称"),width:280,dataType:1,isFilter:!0},{data:"Bank",title:$t("开户行"),dataType:1,isFilter:!0},{data:"Account",title:$t("银行账号"),dataType:1,isFilter:!0},{data:"Remark",title:$t("备注"),isFilter:!0},{data:null,title:$t("操作"),width:140,render:function(t,a,e,n){return'<div class="j-operate"><a href="javascript:;" class="j-edt" style="margin-left: 10px;">'+$t("编辑")+'</a><a href="javascript:;" class="j-del" style="margin-left: 10px;">'+$t("删除")+"</a></div>"}}],initComplete:function(t){$(".operate-btn-box",t).append('<h3 class="dt-title"><em>'+$t("线下收款账号")+"</em></h3>")},formatData:function(t){var t=(t||{}).data||[],a=[];return _.map(t,function(t){a.push({Id:t.id,Name:t.name||"--",Bank:t.bank||"--",Account:t.account||"--",Remark:t.remark||"--"})}),{totalCount:t.length,data:a}}};e.exports=n});
define("crm-setting/order/account/table",["../api","crm-modules/common/util","crm-modules/components/objecttable/objecttable"],function(t,e,n){t("../api"),t("crm-modules/common/util");var i=t("crm-modules/components/objecttable/objecttable"),t=i.extend({getOptions:function(){var t=i.prototype.getOptions.apply(this,arguments),e=(t.showManage=t.showFilerBtn=t.showTerm=!1,t.searchTerm=null,t.showTermBatch=!1,t.showMultiple=!1,t.columns),e=_.filter(e,function(t){return["org_ids","created_by","create_time","last_modified_by","last_modified_time"].indexOf(t.api_name)<0});return _.each(e,function(t){"org_range"===t.api_name&&(t.render=function(t,e,n){try{return"ALL"===JSON.parse(n.org_range).type?$t("全部"):$t("部分")}catch(t){return"--"}})}),e.push({data:null,width:100,title:$t("操作"),lastFixed:!0,render:function(t,e,n){return['<a class="item-btn" data-action="edit">'+$t("编辑")+"</a>",' <a class="item-btn" data-action="delete">'+$t("删除")+"</a>"].join("")}}),t.columns=e,t},trclickHandle:function(t,e,n){n=n.data("action");"view"===n||"edit"===n?this.trigger("onitemclick",n,t):"delete"===n&&this.trigger("onitemdelete",t)},_onSaveColumnWidth:function(t){}});n.exports=t});
define("crm-setting/order/account/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-s-order-account crm-g-form"> <div class="account-tip">' + ((__t = $t("企业钱包中的订货通账户即将停用请尽快接入第三方支付账户")) == null ? "" : __t) + '</div> <div class="fm-item accuont-online-switch"> <label class="fm-lb">' + ((__t = $t("线上支付")) == null ? "" : __t) + '<a style="vertical-align: -3px;" class="crm-doclink" target="_blank" title="' + ((__t = $t("crm.线上支付影响说明")) == null ? "" : __t) + '"></a></label> <div class="switch-sec online-switch on j-switch-online"></div> </div> <div class="account-online-list"> <div class="account-online-item account-online-qy"> <div class="item-hd"> <div class="logo"><i class="wallet"></i>' + ((__t = $t("企业钱包")) == null ? "" : __t) + '</div> <div class="status">' + ((__t = $t("已开通")) == null ? "" : __t) + '</div> </div> <div class="item-bd">' + ((__t = $t("crm.企业钱包介绍")) == null ? "" : __t) + '</div> <div class="item-ft"> <a class="readme" href="http://open.fxiaoke.com/support.html#artiId=98" target="_blank">' + ((__t = $t("了解更多")) == null ? "" : __t) + '</a> <a class="go b-g-btn-cancel" href="#app/entwallet/wallet">' + ((__t = $t("进入企业钱包")) == null ? "" : __t) + '</a> </div> </div> <div class="account-online-item account-online-wx"> <div class="item-hd"> <div class="logo"><i class="wallet weixin"></i>' + ((__t = $t("微信支付")) == null ? "" : __t) + '</div> <div class="status status-wx">' + ((__t = $t("未接入")) == null ? "" : __t) + '</div> </div> <div class="item-bd">' + ((__t = $t("微信支付为第三方支付账户接入可为企业提供支付收款服务。")) == null ? "" : __t) + '</div> <div class="item-ft no-opened"> <a class="readme" href="https://www.fxiaoke.com/mob/guide/flearning/zhifu/qianbao/%E6%8E%A5%E5%85%A5%E7%AC%AC%E4%B8%89%E6%96%B9%E8%B4%A6%E6%88%B7.html" target="_blank">' + ((__t = $t("接入向导")) == null ? "" : __t) + '</a> <a class="go b-g-btn" href="#app/entwallet/wallet">' + ((__t = $t("去接入微信")) == null ? "" : __t) + '</a> </div> <div class="item-ft has-opened"> <a class="readme" href="#app/entwallet/wallet" target="_blank">' + ((__t = $t("了解更多")) == null ? "" : __t) + '</a> </div> </div> <div class="account-online-item account-online-zfb"> <div class="item-hd"> <div class="logo"><i class="wallet zhifubao"></i>' + ((__t = $t("支付宝")) == null ? "" : __t) + '</div> <div class="status status-zfb">' + ((__t = $t("未接入")) == null ? "" : __t) + '</div> </div> <div class="item-bd">' + ((__t = $t("crm.支付宝可为企业提供支付服务")) == null ? "" : __t) + '</div> <div class="item-ft no-opened"> <a class="readme" href="https://www.fxiaoke.com/mob/guide/flearning/zhifu/qianbao/%E6%8E%A5%E5%85%A5%E7%AC%AC%E4%B8%89%E6%96%B9%E8%B4%A6%E6%88%B7.html" target="_blank">' + ((__t = $t("接入向导")) == null ? "" : __t) + '</a> <a class="go b-g-btn" href="#app/entwallet/wallet">' + ((__t = $t("去接入支付宝")) == null ? "" : __t) + '</a> </div> <div class="item-ft has-opened"> <a class="readme" href="#app/entwallet/wallet" target="_blank">' + ((__t = $t("了解更多")) == null ? "" : __t) + '</a> </div> </div> </div> <div class="fm-item clearfix"> <label class="fm-lb">' + ((__t = $t("线下支付")) == null ? "" : __t) + '<a style="vertical-align: -3px;" class="crm-doclink" target="_blank" title="' + ((__t = $t("crm.仔细确认你的账户信息")) == null ? "" : __t) + '"></a></label> <div class="switch-sec offline-switch on j-switch-offline"></div> </div> <div class="account-offline-addbtn" style="padding: 0 20px; text-align: right;"> <span class="b-g-btn btn-setting j-create">' + ((__t = $t("新建")) == null ? "" : __t) + '</span> </div> <div class="account-offline-list" style="padding: 0 20px;"></div> </div>';
        }
        return __p;
    };
});
define("crm-setting/order/account/view",["crm-modules/action/field/field","crm-modules/common/filtertabs/filtertabs"],function(e,t,i){var n=e("crm-modules/action/field/field"),o=e("crm-modules/common/filtertabs/filtertabs").components,e=n.components.use_range.extend({initialize:function(e){e.tabs={all:{type:"ALL",label:$t("全部"),index:0,isHide:!1,selected:!0,component:o.all,options:{}},filter:{type:"CONDITION",label:$t("符合指定条件"),index:1,isHide:!0,selected:!1,component:o.filter,options:{apiname:null}},selector:{type:"ORG",label:$t("组织架构"),index:2,isHide:!1,selected:!1,component:o.selector,options:{}},none:{type:"NONE",label:$t("无"),index:3,isHide:!0,selected:!1,component:o.none,options:{}}},n.components.use_range.prototype.initialize.apply(this,arguments)}}),e=n.View.extend({mycomponents:{org_range:e},initialize:function(){n.View.prototype.initialize.apply(this,arguments)}});i.exports=e});
define("crm-setting/order/agreement/agreement",["../util","../api","./template/tpl-html","../module","crm-modules/common/util","./table","./dialog","./view"],function(e,t,o){var n=e("../util"),i=e("../api"),r=e("./template/tpl-html"),a=e("../module"),s=e("crm-modules/common/util"),d=e("./table"),l=e("./dialog"),c="OrderAgreementObj",u=e("./view"),m=a.extend({name:"CRM-ORDER-AGREEMENT",events:{"click .j-switch-online":"_switchMode","click .j-save":"_saveSingleForm","click .j-effect":"_updateStatus","click .j-invalid":"_updateStatus","click .j-export":"_export","click .j-create":"_create"},initialize:function(e){m.__super__.initialize.call(this,e),this.$el.html(r({imageRoot:CRMSETTING.ASSETS_PATH}))},render:function(){this._getData();var e=$("#cmr-s-order-wrapper").height()-50-40-54-34;this.$(".order-agree-list-wrapper").height(e+"px")},renderModeSwitch:function(){var e=this;2==e.$$state.mode?(e.$(".order-agree-single").hide(),e.$(".order-agree-multi").show(),e.$(".j-switch-online").addClass("on"),e.$(".j-switch-label").text($t("多组织"))):(e.$(".order-agree-single").show(),e.$(".order-agree-multi").hide(),e.$(".j-switch-online").removeClass("on"),e.$(".j-switch-label").text($t("单组织")))},renderBtnWrapper:function(){var e,t=this,o=t.$el.find(".j-btns");2===t.$$state.mode?o.hide():(e=o.children(),t.$$state.data&&t.$$state.data._id?(e.eq(3).show(),!0===t.$$state.data.effective_status?(e.eq(1).hide(),e.eq(2).show()):(e.eq(1).show(),e.eq(2).hide())):(e.eq(1).hide(),e.eq(2).hide(),e.eq(3).hide()),o.show())},_switchMode:function(){var t=this,o=2==t.$$state.mode?1:2;i.setAgreementMode({value:o}).then(function(e){0===e.Result.StatusCode?(t.$$state.mode=o,t.renderModeSwitch(),t.renderBtnWrapper(),2==t.$$state.mode?(t.$$components.$view&&(t.$$components.$view.destroy(),t.$$components.$view=null),t._getMultiList()):(t.$$components.$table&&(t.$$components.$table.destroy(),t.$$components.$table=null),t._getSingleData())):FS.util.error(e.Result.FailureMessage||$t("暂时无法获取数据!"))})},_getData:function(){var t=this;i.getAgreementMode().done(function(e){0===e.Result.StatusCode?(t.$$state.mode=e.Value.value,t.renderModeSwitch(),2==t.$$state.mode?t._getMultiList():t._getSingleData()):FS.util.error(e.Result.FailureMessage||$t("暂时无法获取数据!"))}).complete(function(){})},_getSingleData:function(){var t=this;i.getObjectList({object_describe_api_name:c,search_template_type:"default",ignore_scene_record_type:!1,search_query_info:'{"limit":20,"offset":0,"filters":[],"orders":[{"fieldName":"last_modified_time","isAsc":false}]}'}).done(function(e){0!==e.Result.StatusCode?FS.util.error(e.Result.FailureMessage||$t("暂时无法获取数据!")):(0<(e=e.Value.dataList||[]).length&&(t.$$state.data=e[0]),t.$$components.$view=new u({el:t.$el.find(".order-agree-single-wrapper"),data:t.$$state.data,apiname:c,include_detail_describe:!0,record_type:"default__c",show_type:"full",showNav:!1,showScroll:!1,isWM:!0,hideFields:["owner","data_own_department","name","effective_time","effective_status","customer_range"]}),t.$$components.$view.render(),t.renderBtnWrapper())}).complete(function(){})},_getMultiList:function(){var o=this;o.$$components.$table=new d({el:o.$el.find(".order-agree-list-wrapper"),apiname:c,showTitle:!1,isFilter:!1,isRenderRecordType:!1,showFilerBtn:!1,showTerm:!1,showManage:!1,searchTerm:null}),o.$$components.$table.on("onitemclick",function(e,t){o.$$components.$dialog||(o.$$components.$dialog=new l,o.$$components.$dialog.on("createOrUpdate",function(e){o.$$components.$table.table.refresh()}),o.$$components.$dialog.on("hide",function(){o.$$components.$dialog.destroy(),o.$$components.$dialog=null})),o.$$components.$dialog.show(e,t)}),o.$$components.$table.on("onitemdelete",function(e){var t=s.confirm($t("crm.setting.order.delete_agreement_confirm"),$t("提示"),function(){t.destroy(),i.deleteObject({describe_api_name:c,idList:[e._id]}).done(function(e){0!==e.Result.StatusCode?FS.util.error(e.Result.FailureMessage||$t("暂时无法删除数据!")):(FS.util.remind(1,$t("删除成功!")),o.$$components.$table.table.refresh())})})}),o.$$components.$table.on("onitemexport",function(e){i.exportAgreementViewRecord({dataId:e._id}).done(function(e){0!==e.errorCode?FS.util.error($t("crm.setting.order.export_error_tip")+e.errorCode):(FS.util.remind(1,$t("导出成功")),e=e.Value.path,e=n.getExtendDomainFscLink("0",e+".xlsx","".concat($t("crm.setting.order.view_order_records"),".xlsx"),!0),window.open(e))})}),o.$$components.$table.render()},_export:function(){i.exportAgreementViewRecord({dataId:this.$$state.data._id}).done(function(e){0!==e.errorCode?FS.util.error($t("crm.setting.order.export_error_tip")+e.errorCode):(FS.util.remind(1,$t("导出成功")),e=e.Value.path,e=n.getExtendDomainFscLink("0",e+".xlsx","".concat($t("crm.setting.order.view_order_records"),".xlsx"),!0),window.open(e))})},_create:function(){var t=this;t.$$components.$dialog||(t.$$components.$dialog=new l,t.$$components.$dialog.on("createOrUpdate",function(e){t.$$components.$table.table.refresh()}),t.$$components.$dialog.on("hide",function(){t.$$components.$dialog.destroy(),t.$$components.$dialog=null})),t.$$components.$dialog.show("create",null)},_saveSingleForm:function(){var t=this,o=t.$$components.$view.collect();o.object_describe_api_name=c,t.$$state.data&&t.$$state.data._id&&(o=_.extend(t.$$state.data,o)),i.createOrUpdateAgreement({object_data:o}).done(function(e){0!==e.Result.StatusCode?320001401===e.Result.FailureCode?FS.util.error($t("不能编辑已生效的协议，请先停用!")):FS.util.error(e.Result.FailureMessage||$t("暂时无法获取数据!")):(FS.util.remind(1,$t("保存成功！")),t.$$state.data=_.extend(o,e.Value.objectData),t.renderBtnWrapper())})},_updateStatus:function(e){var t,o=this,n=$(e.target).data("type"),r=o.$$state.data;r&&(e=1==n?$t("协议生效后，客户登录订货通将弹出协议并要求确认，否则无法使用订货通，确定要生效吗？"):$t("停用将导致原来的协议失效，所有客户查看记录也会清除，且再次生效仍需要客户重新签订协议，建议慎重操作。确定要停用吗？"),t=s.confirm(e,$t("提示"),function(){t.destroy(),r.effective_status=1==n,i.createOrUpdateAgreement({object_data:r}).done(function(e){0!==e.Result.StatusCode?FS.util.error(e.Result.FailureMessage||$t("暂时无法编辑数据!")):(FS.util.remind(1,1==n?$t("启用成功"):$t("停用成功")),_.extend(r,e.Value.objectData),o.renderBtnWrapper())})}))}});o.exports=m});
define("crm-setting/order/agreement/dialog",["crm-widget/dialog/dialog","../api","../util","crm-modules/common/util","./view"],function(e,t,i){var a=e("crm-widget/dialog/dialog"),r=e("../api"),n=e("../util"),l=(e("crm-modules/common/util"),e("./view")),d=a.extend({attrs:{content:'<div class="crm-s-order-agreement-dialog"></div>',title:$t("订货协议"),width:1200,height:600,showBtns:!0,showScroll:!0},events:{"click .b-g-btn-cancel":"hide","click .b-g-btn":"_submit","click .mn-checkbox-item":"_changeCheckbox"},initialize:function(e){var t=Array.prototype.slice.call(arguments,1);t.unshift(e),d.superclass.initialize.apply(this,t)},_submit:function(){var e,t=this;n.loading($(".b-g-btn",t.element),!0)||(e=t.$view.collect(),t.data&&t.data._id&&(e=_.extend(t.data,e)),r.createOrUpdateAgreement({object_data:e}).done(function(e){0!==e.Result.StatusCode?320001401===e.Result.FailureCode?FS.util.error($t("不能编辑已生效的协议，请先停用!")):FS.util.error(e.Result.FailureMessage||$t("暂时无法获取数据!")):(FS.util.remind(1,$t("保存成功！")),t.data?_.extend(t.data,e.Value.objectData):t.data=e.Value.objectData,t.trigger("createOrUpdate",t.data),"create"===t.action&&t.hide())}).complete(function(){n.loading($(".b-g-btn",t.element),!1)}))},_initView:function(){var e=this;e.$view&&e.$view.destroy(),e.$view=new l({el:e.$(".crm-s-order-agreement-dialog"),data:e.data,apiname:"OrderAgreementObj",include_detail_describe:!0,record_type:"default__c",show_type:"full",showNav:!1,showScroll:!1,isWM:!0,hideFields:["owner","data_own_department","effective_time","effective_status"]}),e.$view.render()},show:function(e,t){var i=this,a=d.superclass.show.call(this),r=$(".b-g-btn",i.element);return"view"===e?r.hide():r.show(),i.action=e,i.data=t,i._initView(),a},hide:function(){return d.superclass.hide.call(this)},destroy:function(){return this.$$data=null,d.superclass.destroy.call(this)}});i.exports=d});
define("crm-setting/order/agreement/table",["../api","crm-modules/common/util","crm-modules/components/objecttable/objecttable"],function(t,e,a){var o=t("../api"),l=t("crm-modules/common/util"),n=t("crm-modules/components/objecttable/objecttable"),t=n.extend({getOptions:function(){var t=n.prototype.getOptions.apply(this,arguments),e=(t.showManage=t.showFilerBtn=t.showTerm=!1,t.searchTerm=null,t.showTermBatch=!1,[{data:"name",dataType:"text",title:$t("协议名称")},{data:"customer_range",dataType:"user_scope",title:$t("客户范围"),render:function(t,e,a){a=a.customer_range;if(!a)return $t("All");try{return"ALL"===JSON.parse(a).value?$t("All"):$t("部分")}catch(t){return $t("All")}}},{data:"effective_status",dataType:"true_or_false",title:$t("生效状态")},{data:"effective_time",dataType:"date_time",title:$t("生效日期")},{data:null,width:160,title:$t("操作"),lastFixed:!0,render:function(t,e,a){return(!0===a.effective_status?['<a class="item-btn" data-action="view">'+$t("查看")+"</a>",'<a class="item-btn" data-action="disable">'+$t("停用")+"</a>",'<a class="item-btn" data-action="export">'+$t("导出查看记录")+"</a>"]:['<a class="item-btn" data-action="edit">'+$t("编辑")+"</a>",'<a class="item-btn" data-action="enable">'+$t("启用")+"</a>",'<a class="item-btn" data-action="delete">'+$t("删除")+"</a>"]).join("")}}]);return t.columns=e,t},trclickHandle:function(t,e,a){var n,i=this,r=a.data("action");"enable"===r||"disable"===r?(a="enable"===r?$t("协议生效后，客户登录订货通将弹出协议并要求确认，否则无法使用订货通，确定要生效吗？"):$t("停用将导致原来的协议失效，所有客户查看记录也会清除，且再次生效仍需要客户重新签订协议，建议慎重操作。确定要停用吗？"),n=l.confirm(a,$t("提示"),function(){n.destroy(),t.effective_status="enable"==r,o.createOrUpdateAgreement({object_data:t}).done(function(t){0!==t.Result.StatusCode?FS.util.error(t.Result.FailureMessage||$t("暂时无法编辑数据!")):(FS.util.remind(1,"enable"==r?$t("启用成功"):$t("停用成功")),i.table.refresh())})})):"view"===r||"edit"===r?i.trigger("onitemclick",r,t):"export"===r?i.trigger("onitemexport",t):"delete"===r&&i.trigger("onitemdelete",t)},_onSaveColumnWidth:function(t){}});a.exports=t});
define("crm-setting/order/agreement/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-s-order-agree crm-g-form"> <div class="fm-item accuont-online-switch"> <label class="fm-lb">' + ((__t = $t("组织模式")) == null ? "" : __t) + '</label> <div class="switch-sec online-switch j-switch-online"></div> <span class="switch-label j-switch-label"></span> </div> <div class="order-agree-single" style="display: none;"> <div class="order-agree-single-wrapper"></div> <div class="fm-item j-btns" style="display: none;"> <span class="b-g-btn btn-setting j-save">' + ((__t = $t("保存")) == null ? "" : __t) + '</span> <span class="crm-btn btn-setting j-effect" data-type="1">' + ((__t = $t("启用")) == null ? "" : __t) + '</span> <span class="crm-btn btn-setting j-invalid" data-type="0">' + ((__t = $t("停用")) == null ? "" : __t) + '</span> <a href="javascript:;" class="j-export">' + ((__t = $t("导出查看记录")) == null ? "" : __t) + '</a> </div> </div> <div class="order-agree-multi" style="display: none;"> <div class="order-agree-addbtn"> <span class="b-g-btn btn-setting j-create">' + ((__t = $t("新建")) == null ? "" : __t) + '</span> </div> <div class="order-agree-list-wrapper"></div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/order/agreement/view",["../util","../richtext/richtext","crm-modules/action/field/field"],function(e,i,t){var r=e("../util"),n=e("../richtext/richtext"),l="OrderAgreementObj",a=e("crm-modules/action/field/field"),e=a.View.extend({mycomponents:{rich_text2:n},initialize:function(){a.View.prototype.initialize.apply(this,arguments),this.model.fetch=_.bind(this._fetchDescribe,this.model)},_fetchDescribe:function(){var t=this;t._fetchAjax=r.FHHApi({url:"/EM1HNCRM/API/v1/object/"+l+"/controller/DescribeLayout",data:{data_id:null,include_detail_describe:!1,include_layout:!0,apiname:l,layout_type:"add"},success:function(e){var i;0===e.Result.StatusCode?((i=[]).push({field_section:[{api_name:"base_field_section__c",column:2,form_fields:[{is_readonly:!1,is_required:!0,render_type:"text",field_name:"name"},{is_readonly:!1,is_required:!0,render_type:"rich_text2",field_name:"service_term"},{is_readonly:!1,is_required:!1,render_type:"file_attachment",field_name:"attachment"},{is_readonly:!1,is_required:!1,render_type:"use_scope",field_name:"customer_range"}]}],api_name:"base_field_section__c",column:2}),e.Value.layout.components=i,t.parseDescribe(e.Value)):(FS.util.error(e.Result.FailureMessage||$t("暂时无法获取数据!")),t.trigger("error",e))},complete:function(){t._fetchAjax=null}},{errorAlertModel:1})}});t.exports=e});
define("crm-setting/order/api",["./util","base-modules/utils"],function(e,t,n){window.FS;var a=e("./util"),o=e("base-modules/utils");n.exports={cache:{},isBooted:function(){return this.ajax("P","/employeeConfig/isBooted")},setBooted:function(){return this.ajax("P","/employeeConfig/confirmBooted")},getContactAndLogo:function(){return this.ajax("P","/config/getContactAndLogo")},updateContactOrLogo:function(e,t){return this.ajax("P",t?t+"/config/updateContactOrLogo":"/config/updateContactOrLogo",e)},getTiedProduct:function(){return this.ajax("P","/tiedProduct/batchGet")},updateTiedProduct:function(e){return this.ajax("P","/tiedProduct/batchCreateOrUpdate",e)},updateOrderWay:function(e){return this.ajax("P","/config/createOrUpdateAddCartFlag",e)},updateOrderScope:function(e){return this.ajax("P","/config/createOrUpdateOrderScope",e)},setSailConfig:function(e){return this.ajax("P","/config/configSet",e)},getSailConfig:function(e){return this.ajax("P","/config/configGet",e)},batchGetDescribeByApiName:function(e){return this.ajax("P","/describeLayout/findDescribeListByApiName",e)},getWorkflowInfo:function(){return this.ajax("P","/config/getWorkFlowInfo")},setWorkflowInfo:function(e){return this.ajax("P","/config/updateWorkFlowInfo",e)},addOrUpdateBankAccount:function(e){return this.ajax("P","/paymentAccount/createOrUpdate",e)},deleteBankAccount:function(e){return this.ajax("P","/paymentAccount/delete",e)},getCrmBasicConfig:function(e){return this.ajax("P","^/FHH/EM1HNCRM/API/v1/object/biz_config/service/get_config_value_by_key",e,{loading:!1})},getHomeApp:function(){return this.ajax("P","/homeLayout/isLinkAppsOpen")},getHomeSetting:function(e){return this.ajax("P","/homeLayout/get",e)},updateHomeSetting:function(e){return this.ajax("P","/homeLayout/createOrUpdate",e)},getRecommendCategories:function(e){return this.ajax("P","/product/getRecommendCategories",e)},updateRecommendCategories:function(e){return this.ajax("P","/product/createOrUpdateRecommendCategories",e)},updateOnlinePaySwitch:function(e){return this.ajax("P","/config/createOrUpdateOnlinePaySwitch",e)},updateOfflinePaySwitch:function(e){return this.ajax("P","/config/createOrUpdateOfflinePaySwitch",e)},getSignConfig:function(e){return this.ajax("P","^/statement-admin/elec/getConfig",e)},setSignConfigKeyword:function(e){return this.ajax("P","^/statement-admin/elec/createOrUpdateSignKeyword",e)},setSignConfigSigner:function(e){return this.ajax("P","^/statement-admin/elec/createOrUpdateSealConfig",e)},getSignFromSigner:function(e){return this.ajax("P","^/statement-admin/elec/getUserBindAccount",e)},deleteSign:function(e){return this.ajax("P","^/statement-admin/elec/deleteSealConfig",e)},getAgreementMode:function(e){return this.ajax("P","^/FHH/EM1HNCRM/API/v1/object/order_agreement/service/get_organization_mode",e)},setAgreementMode:function(e){return this.ajax("P","^/FHH/EM1HNCRM/API/v1/object/order_agreement/service/set_organization_mode",e)},createOrUpdateAgreement:function(e){var t=e.object_data||{};return t.name=t.name||$t("订货协议"),t.effective_time=t.effective_time||(new Date).getTime(),void 0===t.effective_status&&(t.effective_status=!1),this.createOrUpdateObject(e)},getObjectList:function(e){var t=e.object_describe_api_name;return this.ajax("P","^/FHH/EM1HNCRM/API/v1/object/"+t+"/controller/List",e)},createOrUpdateObject:function(e){var t=e.object_data||{},n=t.object_describe_api_name,n=t._id?"^/FHH/EM1HNCRM/API/v1/object/"+n+"/action/Edit":"^/FHH/EM1HNCRM/API/v1/object/"+n+"/action/Add";return e.object_data=t,this.ajax("P",n,e)},deleteObject:function(e){var t=e.describe_api_name;return this.ajax("P","^/FHH/EM1HNCRM/API/v1/object/"+t+"/action/BulkDelete",e)},exportAgreementViewRecord:function(e){return this.ajax("P","/orderAgreement/exportViewRecord",e)},ajax:function(e,t,n,i){(i=i||{}).url=t,i.type="G"===e?"GET":"POST",i.data=n,i.loading&&a.showLoading(!0);e=((e,t)=>{if((e=_.extend({type:"POST",contentType:"application/json; charset=utf-8"},e)).url)return/^\^/.test(e.url)?e.url=e.url.substr(1):e.url="/FHH/EM1HSailAdmin/sail-admin"+e.url,"post"==e.type.toLowerCase()&&/^application\/json/.test(e.contentType)&&(e.data=JSON.stringify(e.data)),t=_.extend({submitSelector:!1,autoPrependPath:!1},t),o.api(e,t);throw new Error("We need a property url for request")})(i);return e.done(function(e){/^\^/.test(t)||a.covertResponse(e)}).fail(function(e){}).complete(function(){i.loading&&a.showLoading(!1)}),e},defer:function(t){var n=$.Deferred();return _.defer(function(){var e=null;t&&(e=t()),n.resolve(e)}),n.promise()},getWarehouseList:function(e){return this.ajax("P","/warehouse/list",e)},getExchangeReturnStatus:function(){return this.ajax("P","/config/isExchangeReturnOpen")},getReceiveInfo:function(){return this.ajax("P","^/FHH/EM1HNCRM/API/v1/object/stock_module/service/get_automatic_receiving_conf")},getReceiveConfig:function(){return this.ajax("P","^/FHH/EM1HNCRM/API/v1/object/stock_config/service/get_config_values",{isAllConfig:!1,keys:["delivery_note_status"]})},updateStockView:function(e){return this.ajax("P","^/FHH/EM1HNCRM/API/v1/object/stock_module/service/update_dht_stock_view_type",e)},getStockData:function(){return this.ajax("P","^/FHH/EM1HNCRM/API/v1/object/stock/service/query_stock_config")}}});
define("crm-setting/order/author/authlist-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="checkbox-scroll"> ';
            _.each(list, function(item, index) {
                __p += ' <h3 class="checkbox-head" id="' + ((__t = item.descApiName) == null ? "" : __t) + '" name="' + ((__t = item.descApiName) == null ? "" : __t) + '"> ' + ((__t = item.descApiDisplayName) == null ? "" : __t) + ' </h3> <dl class="mn-checkbox-box checkbox-group b-g-clear"> <dd class="checkbox-list"> <div class="checkbox-item"> <span class="mn-checkbox-item j-check-all ' + ((__t = item.isEditable ? "" : "disabled-selected") == null ? "" : __t) + '"></span> <span class="check-lb">' + ((__t = $t("全选")) == null ? "" : __t) + "</span> </div> ";
                _.each(item.roleFunctionInfos, function(ite, ind) {
                    __p += ' <div class="checkbox-item"> <span class="mn-checkbox-item ' + ((__t = !ite.isEditable ? "disabled-selected" : "") == null ? "" : __t) + " " + ((__t = ite.enabled ? "mn-selected" : "") == null ? "" : __t) + '" data-functionnumber=' + ((__t = ite.functionNumber) == null ? "" : __t) + '></span> <span class="check-lb">' + ((__t = ite.displayName) == null ? "" : __t) + "</span> </div> ";
                });
                __p += " </dd> </dl> ";
            });
            __p += ' </div> <div class="checkbox-btn"> <div class="b-g-btn btn-to-save b-g-btn-disabled">' + ((__t = $t("保存")) == null ? "" : __t) + "</div> ";
            if (roletype === 1) {
                __p += '<div class="btn-to-reset b-g-btn-disabled">' + ((__t = $t("恢复默认")) == null ? "" : __t) + "</div>";
            }
            __p += " </div>";
        }
        return __p;
    };
});
define("crm-setting/order/author/author-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="rolemag-group rolemag-table rolemag-box SettingNav"> <div class="box-leftnav"> <div class="crm-loading "></div> </div> <div class="box-auth-setting box-centersection b-g-hide"> <div class="crm-loading "></div> </div> <div class="box-role-assign rolemag-t-box b-g-hide"> <div class="crm-loading "></div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/order/author/author",["crm-widget/selector/selector","crm-widget/table/table","crm-modules/common/util","./author-html","./rolelist-html","./authlist-html"],function(e,t,i){var l=e("crm-widget/selector/selector"),r=e("crm-widget/table/table"),n=e("crm-modules/common/util"),a=e("./author-html"),o=e("./rolelist-html"),s=e("./authlist-html"),e=Backbone.View.extend({events:{"click .SettingNav .default-rolelist li":"onSwitchTab"},initialize:function(e){this.$el.html(a({}))},render:function(){this.renderLeftNav(),this.doSwitchTab(0)},renderLeftNav:function(){var e={roles:[{groupName:$t("角色管理"),roleInfoList:[{roleCode:-1,roleType:-1,roleName:$t("角色权限设置"),description:""},{roleCode:-1,roleType:-1,roleName:$t("人员角色分配"),description:""}]}]},t=this.$(".box-leftnav");t.html(o(e)),t.find("li").first().addClass("active")},renderAuthList:function(){var e={list:[{descApiName:"..",descApiDisplayName:$t("crm.联系人"),isHaveFieldPrivilege:!1,isEditable:!0,roleFunctionInfos:[{isEditable:!0,enabled:!0,functionNumber:1e3,displayName:$t("查看列表")}]}],roletype:1,rolename:$t("CRM管理员")},t=this.$(".box-centersection");t.find(".crm-loading ").remove(),t.append(s(e))},renderRoleList:function(){var a=this;a.dt?a.dt.setParam({},!0):(a.dt=new r({$el:$(".rolemag-t-box",a.$el),url:"/EM1HCRMUdobj/userApi/crmUserList",requestType:"FHHApi",showFilerBtn:!1,showMultiple:!0,trHandle:!1,operate:{pos:"T",btns:[{text:$t("添加联系人"),className:"btn-to-add-employee j-all-crm-user"}]},search:{pos:"T",placeHolder:$t("crm.搜索客户名称"),type:"username",fileName:"username"},batchBtns:[{text:$t("移除全部角色"),className:"btn-to-del-crm-role"}],postData:{deptId:""},columns:[{data:"employeeId",title:$t("姓名"),width:150,render:function(e,t,i){return n.getEmployeeById(e)&&n.getEmployeeById(e).name||"--"}},{data:"employeeId",title:$t("crm.部门"),render:function(e,t,i){e=n.getEmployeeById(e)&&_.map(n.getEmployeeById(e).circleIds,function(e){return FS.contacts.getCircleById(e).name}).join($t("、"));return e?'<span title="'+e+'">'+e+"</span>":"--"}},{data:"employeeId",title:$t("职位"),render:function(e,t,i){return n.getEmployeeById(e)&&n.getEmployeeById(e).post||"--"}},{data:"roleList",title:$t("角色"),render:function(e,t,i){e=_.map(e,function(e){return e.roleName}).join($t("、"));return e?'<span title="'+e+'">'+e+"</span>":"--"}},{data:"",title:$t("操作"),render:function(e,t,i){return'<div class="edit-area"><a class="role-edit">'+$t("编辑")+'</a><a class="role-del">'+$t("移除")+'</a><a class="role-copy">'+$t("复制角色到员工")+"</a></div>"}}],formatData:function(e){var i=[];return _.each(e.map,function(e,t){i.push({employeeId:t,roleList:e})}),{totalCount:e.page&&e.page.totalCount||0,data:i}},initComplete:function(e){$(".SettingNav .batch-btns",a.$el).after(['<div class="dt-sc-box batch-c">','<span class="line"></span>','<span class="term-tit">'+$t("范围：")+" </span>",'<div class="rolemag-t-check-circle dt-ipt-wrap"></div>',"</div>"].join("")),a.initAssignCircle("crm")}}),a.dt.on("trclick",function(e,t,i){i.hasClass("role-edit")?a.editRolePowerHandle(e):i.hasClass("role-del")?a.delCrmRolePowerHandle(e):i.hasClass("role-copy")&&a.copyRolePowerHandle(e)}))},initAssignCircle:function(a){var r=this;r.assignCircleWidget&&r.assignCircleWidget.destroy(),r.assignCircleWidget=new l({$wrap:$(".SettingNav .rolemag-t-check-circle",r.$el),zIndex:1e3,group:!0,singleCked:!0,label:$t("crm.选择部门")}),r.assignCircleWidget.on("change",function(e){var e=e.group||[],i=[],t="crm"===a?r.dt:r.assignDt;_.each(e,function(e,t){i.push(+e)}),t.setParam({deptId:i.join(",")},!0,!0)})},onSwitchTab:function(e){var t,i=this,a=$(e.currentTarget);a.hasClass("active")||(i.toSubmit?t=n.confirm($t("本次权限更改尚未保存是否不保存并跳到新页面"),$t("提示"),function(){t.destroy(),i.doSwitchTab(a.index()-1),i.toSubmit=!1}):i.doSwitchTab(a.index()-1))},doSwitchTab:function(e){var t=this,i=$(".box-auth-setting",t.$el),a=$(".box-role-assign",t.$el),r=$(".default-rolelist li",t.$el);r.removeClass("active"),r.eq(e).addClass("active"),0==e?(i.show(),a.hide(),t.renderAuthList()):(i.hide(),a.show(),t.renderRoleList())},destroy:function(){}});i.exports=e});
define("crm-setting/order/author/rolelist-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            if (roles && roles.length > 0) {
                __p += " ";
                _.each(roles, function(item, index) {
                    __p += ' <ul class="default-rolelist"> <div class="item-group">' + ((__t = item.groupName) == null ? "" : __t) + "</div> ";
                    _.each(item.roleInfoList, function(ite, ind) {
                        __p += ' <li data-id="' + ((__t = ite.roleCode) == null ? "" : __t) + '" data-type="' + ((__t = ite.roleType) == null ? "" : __t) + '" data-rolename="' + ((__t = ite.roleName) == null ? "" : __t) + '" data-description="' + ((__t = ite.description) == null ? "" : __t) + '" title="' + ((__t = item.description) == null ? "" : __t) + '"> <div class="name">' + ((__t = ite.roleName) == null ? "" : __t) + "</div> </li> ";
                    });
                    __p += " </ul> ";
                });
                __p += " ";
            }
        }
        return __p;
    };
});
define("crm-setting/order/common/preview",["crm-modules/common/util","crm-modules/common/field/field"],function(e,i,l){e("crm-modules/common/util");var t=e("crm-modules/common/field/field").BaseComponents,e=t.extend({initialize:function(){t.prototype.initialize.call(this)},render:function(){var e=this,i=e.getValue();e.$el.html(_.isArray(i)?i.join(","):i),e.setValue(e.model.get("fieldData"))},getValue:function(){var i=this.fieldName,e=this.model.get("fieldData"),l=null;return _.each(e,function(e){e.FieldName==i&&(l=e.FieldValue.Value||e.FieldValue.Values)}),l},destroy:function(){this.super.destroy.call(this)}});l.exports=e});
define("crm-setting/order/common/selfview",["crm-modules/common/field/field","./preview"],function(e,i,n){var t=e("crm-modules/common/field/field"),o=e("./preview"),e=t.View.extend({initView:function(e){this.super.initView.call(this,e),this._initSelfCompoents(e)},_initSelfCompoents:function(e){var i=this;i.$("[data-fieldtype=99999]").each(function(){var e=$(this).data();(i.forms[e.fieldname]=new o({el:this,model:i.model,fieldName:e.fieldname,fieldType:e.fieldtype})).render()})}});n.exports=e});
define("crm-setting/order/common/table",["base-modules/utils","crm-widget/table/table"],function(e,t,a){var s=e("base-modules/utils"),e=e("crm-widget/table/table").extend({_apiRequest:function(t){var a=this.options,e=this.getParam(),e=a.paramFormat?a.paramFormat(e):e;return s.api({url:a.url,data:e,type:a.method,success:function(e){"define"!=a.requestType||0!=e[a.requestCodeKey]&&null!=e.Error?e.success&&t.suc&&t.suc(e.value||{}):(e.data=e.Value,t.suc&&t.suc(e))},complete:t.complete},{autoPrependPath:"define"!=a.requestType})}});a.exports=e});
define("crm-setting/order/dialog/appclassification",["crm-widget/dialog/dialog","../api","../util","crm-modules/common/util"],function(e,a,t){var s=e("crm-widget/dialog/dialog"),i=e("../api"),l=e("../util"),n=e("crm-modules/common/util"),d=s.extend({attrs:{content:'<div class="crm-s-order-setting-orderway-dialog"><p class="title">'+$t("App分类导航")+'：</p><div class="mn-radio-box"><p class="item"><span class="mn-radio-item mn-selected" data-value=1></span><span class="label">'+$t("一级分类在左侧")+'</span></p><p class="item"><span class="mn-radio-item" data-value=2></span><span class="label">'+$t("一级分类在顶部")+"</p></div></div>",title:$t("App分类导航"),width:500,showBtns:!0,showScroll:!1},events:{"click .b-g-btn-cancel":"hide","click .b-g-btn":"_submit","click .mn-radio-item":"_changeRadio"},_changeRadio:function(e){e=$(e.currentTarget);this.$$data.value=e.data("value"),e.hasClass("mn-selected")||(this.$(".mn-selected").removeClass("mn-selected"),e.addClass("mn-selected"))},_submit:function(){var e,a=this;l.loading($(".b-g-btn",a.element),!0)||(e=[{key:"classification_navigation",value:String(a.$$data.value),type:4}],i.setSailConfig({configList:e}).done(function(e){e&&0===e.errorCode?(l.remind($t("保存成功")),a.trigger("addOrUpdate",a.$$data.value),a.hide()):l.error($t("crm.服务暂时不可用",{errorCode:e.errorCode}))}).complete(function(){l.loading($(".b-g-btn",a.element),!1)}))},show:function(e){var a=d.superclass.show.call(this),e=(this.$$data=n.deepClone(e)||{},this.$$data.value);return this.$(".mn-selected").removeClass("mn-selected"),this.$("span[data-value="+e+"]").addClass("mn-selected"),a},hide:function(){return d.superclass.hide.call(this)},destroy:function(){return this.$$data=null,d.superclass.destroy.call(this)}});t.exports=d});
define("crm-setting/order/dialog/availablerange",["crm-widget/dialog/dialog","../api","../util","crm-modules/common/util"],function(e,a,t){var s=e("crm-widget/dialog/dialog"),l=e("../api"),i=e("../util"),n=e("crm-modules/common/util"),d=s.extend({attrs:{content:'<div class="crm-s-order-setting-dialog"><p class="title">'+$t("根据客户实际业务场景，选择一种模式")+'：</p><div class="mn-radio-box"><p class="select-item"><span class="mn-radio-item mn-selected" data-value=1></span><span class="label">'+$t("标准模式：客户不用选择可售范围和价目表，根据设置的可售范围过滤，并基于价目表询价。")+'</span></p><p class="select-item"><span class="mn-radio-item" data-value=2></span><span class="label">'+$t("兼容模式：支持客户选择价目表，可售范围仅限价目表中的产品，获取价目表中的价格，不再询价。")+'</span></p><p class="select-item"><span class="mn-radio-item" data-value=3></span><span class="label">'+$t("多组织模式：客户可选择可售范围，价目表可在多个价目表中询价，适用于多品牌多产品线的客户。")+"</span></p></div></div>",title:$t("可售范围及价格控制"),width:500,height:300,showBtns:!0,showScroll:!1},events:{"click .b-g-btn-cancel":"hide","click .b-g-btn":"_submit","click .mn-radio-item":"_changeRadio"},_changeRadio:function(e){var a=this,t=$(e.currentTarget),s=Number(t.data("value"));return 2!==s||a.$$data.isPriceBookEnable?3!==s||a.$$data.isAvailableRangeEnable?(a.$$data.value=s,void(t.hasClass("mn-selected")||(this.$(".mn-selected").removeClass("mn-selected"),t.addClass("mn-selected")))):(FS.util.remind(3,$t("CRM未启用可售范围")),e.stopPropagation(),!1):(FS.util.remind(3,$t("CRM未启用价目表")),e.stopPropagation(),!1)},_submit:function(){var e,a=this;i.loading($(".b-g-btn",a.element),!0)||(e=[{key:"order_range_model",value:String(a.$$data.value),type:4}],l.setSailConfig({configList:e}).done(function(e){e&&0===e.errorCode?(i.remind($t("保存成功")),a.trigger("addOrUpdate",a.$$data.value),a.hide()):i.error($t("crm.服务暂时不可用",{errorCode:e.errorCode}))}).complete(function(){i.loading($(".b-g-btn",a.element),!1)}))},show:function(e){var a=this,t=d.superclass.show.call(this);return a.$$data=n.deepClone(e)||{},a.$$data.value=a.$$data.value||1,this.$(".mn-selected").removeClass("mn-selected"),this.$("span[data-value="+a.$$data.value+"]").addClass("mn-selected"),t},hide:function(){return d.superclass.hide.call(this)},destroy:function(){return this.$$data=null,d.superclass.destroy.call(this)}});t.exports=d});
define("crm-setting/order/dialog/bankaccount",["crm-widget/dialog/dialog","../api","../util"],function(t,e,a){var i=t("crm-widget/dialog/dialog"),l=t("../api"),d=t("../util"),n=i.extend({attrs:{content:'<div class="crm-g-form crm-c-field"><div class="field-box"><input class="fm-ipt" type="hidden" data-fieldname="id" data-fieldproperty="1"><div class="fm-item"><label title="'+$t("账户名称")+'" class="fm-lb "><em></em><span> '+$t("账户名称")+'</span></label><input maxlength="100" class="b-g-ipt fm-ipt normal-ipt"  data-fieldtype="2" data-fieldname="name" data-fieldproperty="1"></div><div class="fm-item"><label title="'+$t("开户行")+'" class="fm-lb "><em></em><span>'+$t("开户行")+'</span></label><input maxlength="100" class="b-g-ipt fm-ipt normal-ipt"  data-fieldtype="2" data-fieldname="bank" data-fieldproperty="1"></div><div class="fm-item"><label title="'+$t("银行账号")+'" class="fm-lb "><em></em><span>'+$t("银行账号")+'</span></label><input maxlength="100" class="b-g-ipt fm-ipt normal-ipt"  data-fieldtype="2" data-fieldname="account" data-fieldproperty="1"></div><div class="fm-item"><label title="'+$t("备注")+'" class="fm-lb "><em></em><span>'+$t("备注")+'</span></label><textarea maxlength="500" class="b-g-ipt fm-ipt normal-ipt" data-fieldtype="3" data-fieldname="remark" data-fieldproperty="2"></textarea></div></div></div>',title:$t("新建收款账号"),width:660,height:240,showBtns:!0,btnName:{save:$t("保存")}},events:{"click .b-g-btn-cancel":"hide","click .b-g-btn":"submit"},show:function(a){var t,e=n.superclass.show.call(this);return a&&(t=$(".fm-ipt",this.el),_.each(t,function(t){var t=$(t),e=t.data("fieldname");e&&t.val(a[e]||"")})),e},submit:function(){var e=this,t=$(".fm-ipt",e.el),a={};_.each(t,function(t){var t=$(t),e=t.data("fieldname");e&&(a[e]=t.val().trim())}),/^\d+$/.test(a.account)?500<a.remark.length?d.remind(3,$t("备注不能超过500个字符")):(a.id||delete a.id,d.loading($(".b-g-btn",e.element),!0)||l.addOrUpdateBankAccount(a).done(function(t){t&&0===t.errorCode?(e.hide(),e.trigger("addOrUpdate",t)):d.error($t("crm.服务暂时不可用",{errorCode:t.errorCode}))}).complete(function(){d.loading($(".b-g-btn",e.element),!1)})):d.remind(3,$t("请输入正确的银行账号"))},hide:function(){var t=$(".fm-ipt",this.el);return _.each(t,function(t){$(t).val("")}),n.superclass.hide.call(this)},destroy:function(){return n.superclass.destroy.call(this)}});a.exports=n});
define("crm-setting/order/dialog/intention",["crm-widget/dialog/dialog","../api","../util","./template/intention-html"],function(n,t,e){var i=n("crm-widget/dialog/dialog"),a=(n("../api"),n("../util"),n("./template/intention-html")),l=i.extend({attrs:{content:'<div class="crm-s-order-int crm-c-field j-preview"></div>',title:$t("纷享销客系统对接申请表"),width:660,showBtns:!0,btnName:{save:$t("保存"),cancel:$t("关闭")}},events:{"click .j-view-attach":"_viewAttachHandle","click .b-g-btn-cancel":"hide","click .b-g-btn":"submit"},_viewAttachHandle:function(t){var e=$(t.target).data(),i={fileId:1,fileName:e.name||e.path,filePath:e.path};return n.async("base-modules/file-preview/index",function(t){t.preview(i)}),t.preventDefault(),!1},show:function(t){var e=l.superclass.show.call(this);return t&&$(".j-preview",this.element).html(a({data:t})),$(".b-g-btn",this.element).hide(),e},hide:function(){return l.superclass.hide.call(this)},destroy:function(){return l.superclass.destroy.call(this)}});e.exports=l});
define("crm-setting/order/dialog/orderscope",["crm-widget/dialog/dialog","../api","../util","crm-modules/common/util"],function(e,t,a){var s=e("crm-widget/dialog/dialog"),d=e("../api"),r=e("../util"),i=e("crm-modules/common/util"),l=s.extend({attrs:{content:'<div class="crm-s-order-setting-orderway-dialog"><p class="title">'+$t("crm.订单范围")+'：</p><div class="mn-radio-box"><p class="item"><span class="mn-radio-item mn-selected" data-value=1></span><span class="label">'+$t("仅自己在线提交的订单")+'</span></p><p class="item"><span class="mn-radio-item" data-value=2></span><span class="label">'+$t("crm.所有订单")+"</p></div></div>",title:$t("订单范围"),width:500,showBtns:!0,showScroll:!1},events:{"click .b-g-btn-cancel":"hide","click .b-g-btn":"_submit","click .mn-radio-item":"_changeRadio"},_changeRadio:function(e){e=$(e.currentTarget);this.$$data.orderScope=e.data("value"),e.hasClass("mn-selected")||(this.$(".mn-selected").removeClass("mn-selected"),e.addClass("mn-selected"))},_submit:function(){var t,a=this;r.loading($(".b-g-btn",a.element),!0)||(t={orderScope:String(a.$$data.orderScope)},d.updateOrderScope(t).done(function(e){e&&0===e.errorCode?(r.remind($t("保存成功")),a.trigger("addOrUpdate",t.orderScope),a.hide()):r.error($t("crm.服务暂时不可用",{errorCode:e.errorCode}))}).complete(function(){r.loading($(".b-g-btn",a.element),!1)}))},show:function(e){var t=l.superclass.show.call(this),e=(this.$$data=i.deepClone(e)||{},this.$$data.orderScope);return this.$(".mn-selected").removeClass("mn-selected"),this.$("span[data-value="+e+"]").addClass("mn-selected"),t},hide:function(){return l.superclass.hide.call(this)},destroy:function(){return this.$$data=null,l.superclass.destroy.call(this)}});a.exports=l});
define("crm-setting/order/dialog/orderstock",["crm-widget/dialog/dialog","../api","../util","crm-modules/common/util","./template/orderstockoption-html"],function(t,e,i){var l=t("crm-widget/dialog/dialog"),o=t("../api"),s=(t("../util"),t("crm-modules/common/util"),t("./template/orderstockoption-html")),c=[{id:1,title:$t("不显示库存.")},{id:3,title:$t("模糊显示库存."),tipHtml:[{type:"b",label:$t("模糊库存")},{label:$t("当安全库存不为空时模糊库存有缺货少量充足三种")},{label:$t("缺货：可用库存")+"<=0"},{label:$t("少量")+"：0 <"+$t("可用库存")+"<"+$t("安全库存")},{label:$t("充足：安全库存<=可用库存，且可用库存大于0。")},{label:$t("当安全库存为空时模糊库存有缺货充足两种")},{label:$t("缺货：可用库存")+"<=0"},{label:$t("充足：可用库存")+"> 0"}],parseTipHtml:function(t){var e="";return _.each(t,function(t){"b"===t.type?e+="<b>"+_.escape(t.label)+"</b>":e+="<p>"+_.escape(t.label)+"</p>"}),e}},{id:2,title:$t("精确显示库存.")}],n=l.extend({attrs:{content:"",title:$t("订货通设置"),width:500,showBtns:!0,showScroll:!1},stockViewType:null,events:{"click .b-g-btn-cancel":"hide","click .b-g-btn":"_submit","click .j-option":"_handleSelectOption"},_handleSelectOption:function(t){t=$(t.currentTarget).data("id");this.stockViewType=t},_submit:function(){var e=this;o.updateStockView({stockViewType:e.stockViewType}).done(function(t){t.Value&&t.Value.isSuccess&&(t=_.find(c,function(t){return t.id==e.stockViewType}),$(".v-order-stock").html(t&&t.title),e.hide())})},show:function(t){this.$$data=t,this.stockViewType=t;var e=n.superclass.show.call(this);return this.setContent(s({options:c,value:t})),e},hide:function(){return this.stockViewType=this.$$data,n.superclass.hide.call(this)},destroy:function(){return this.$$data=null,n.superclass.destroy.call(this)}});i.exports=n});
define("crm-setting/order/dialog/orderWay",["crm-widget/dialog/dialog","../api","../util","crm-modules/common/util"],function(t,e,a){var s=t("crm-widget/dialog/dialog"),d=t("../api"),i=t("../util"),r=t("crm-modules/common/util"),l=s.extend({attrs:{content:'<div class="crm-s-order-setting-orderway-dialog"><p class="title">'+$t("crm.选择订购方式")+'：</p><div class="mn-radio-box"><p class="item"><span class="mn-radio-item mn-selected" data-value=1></span><span class="label">'+$t("只能选择一个规格加入购物车")+'</span></p><p class="item"><span class="mn-radio-item" data-value=2></span><span class="label">'+$t("可同时选择多个规格加入购物车")+'</span><br/><span class="tip">('+$t("建议产品规格维度不要超过2个比如颜色尺寸")+" )</span></p></div></div>",title:$t("订购方式"),width:500,height:300,showBtns:!0,showScroll:!1},events:{"click .b-g-btn-cancel":"hide","click .b-g-btn":"_submit","click .mn-radio-item":"_changeRadio"},_changeRadio:function(t){t=$(t.currentTarget);this.$$data.addCartProductSwitch=Number(t.data("value")),t.hasClass("mn-selected")||(this.$(".mn-selected").removeClass("mn-selected"),t.addClass("mn-selected"))},_submit:function(){var e,a=this;i.loading($(".b-g-btn",a.element),!0)||(e={addCartProductSwitch:a.$$data.addCartProductSwitch},d.updateOrderWay(e).done(function(t){t&&0===t.errorCode?(i.remind($t("保存成功")),a.trigger("addOrUpdate",e.addCartProductSwitch),a.hide()):i.error($t("crm.服务暂时不可用",{errorCode:t.errorCode}))}).complete(function(){i.loading($(".b-g-btn",a.element),!1)}))},show:function(t){var e=l.superclass.show.call(this),t=(this.$$data=r.deepClone(t)||{},2===this.$$data.addCartProductSwitch?2:1);return this.$(".mn-selected").removeClass("mn-selected"),this.$("span[data-value="+t+"]").addClass("mn-selected"),e},hide:function(){return l.superclass.hide.call(this)},destroy:function(){return this.$$data=null,l.superclass.destroy.call(this)}});a.exports=l});
define("crm-setting/order/dialog/picturemode",["crm-widget/dialog/dialog","../api","../util","crm-modules/common/util"],function(e,t,a){var s=e("crm-widget/dialog/dialog"),i=e("../api"),d=e("../util"),l=e("crm-modules/common/util"),n=s.extend({attrs:{content:'<div class="crm-s-order-setting-orderway-dialog"><p class="title">'+$t("请选择下游客户端使用的图片模式")+'：</p><div class="mn-radio-box"><p class="item"><span class="mn-radio-item mn-selected" data-value="1"></span><span class="label">'+$t("有图模式")+'</span></p><p class="item"><span class="mn-radio-item" data-value="2"></span><span class="label">'+$t("无图模式")+"</p></div></div>",title:$t("图片模式"),width:500,showBtns:!0,showScroll:!1},events:{"click .b-g-btn-cancel":"hide","click .b-g-btn":"_submit","click .mn-radio-item":"_changeRadio"},_changeRadio:function(e){e=$(e.currentTarget);this.$$data.pictureMode=e.data("value"),e.hasClass("mn-selected")||(this.$(".mn-selected").removeClass("mn-selected"),e.addClass("mn-selected"))},_submit:function(){var e,t=this;d.loading($(".b-g-btn",t.element),!0)||(e=[{key:"pictureMode",value:String(t.$$data.pictureMode),type:4}],i.setSailConfig({configList:e}).done(function(e){e&&0===e.errorCode?(d.remind($t("保存成功")),t.trigger("addOrUpdate",t.$$data.pictureMode),t.hide()):d.error($t("crm.服务暂时不可用",{errorCode:e.errorCode}))}).complete(function(){d.loading($(".b-g-btn",t.element),!1)}))},show:function(e){var t=n.superclass.show.call(this),e=(this.$$data=l.deepClone(e)||{},this.$$data.pictureMode);return this.$(".mn-selected").removeClass("mn-selected"),this.$("span[data-value="+e+"]").addClass("mn-selected"),t},hide:function(){return n.superclass.hide.call(this)},destroy:function(){return this.$$data=null,n.superclass.destroy.call(this)}});a.exports=n});
define("crm-setting/order/dialog/repeat",["crm-widget/dialog/dialog","crm-widget/table/table","../api","../util"],function(t,e,i){var a=t("crm-widget/dialog/dialog"),s=t("crm-widget/table/table"),l=(t("../api"),t("../util"),a.extend({attrs:{content:'<div class="crm-g-form"><div style="margin-bottom:10px;">'+$t("crm.确任相似记录")+' ：</div><div class="repeat-list" style="height:300px;"></div></div>',title:$t("crm.关联合作伙伴"),width:680,showBtns:!0,btnName:{save:$t("立即关联")}},events:{"click .b-g-btn-cancel":"hide","click .b-g-btn":"submit"},initTable:function(){var t=this;t.$$dt&&t.$$dt.destroy(),t.$$dt=new s({$el:$(".repeat-list",t.element),requestType:"define",requestCodeKey:"errorCode",method:"get",showPage:!1,showMoreBtn:!1,showFilerBtn:!1,showMultiple:!0,columns:[{data:"Name",title:$t("合作伙伴名称"),width:280,dataType:1,isFilter:!0},{data:"SimpleName",title:$t("简称"),dataType:1,isFilter:!0},{data:"Owner",title:$t("负责人"),dataType:1,isFilter:!0}]})},initialize:function(t){var e=this,t=l.superclass.initialize.call(this,t);return e.on("create",e.onCreate.bind(e)),t},onCreate:function(t){alert(0)},show:function(){var t=l.superclass.show.call(this);return this.initTable(),$(".dialog-btns",this.element).css({position:"relative"}).prepend('<div class="j-tip" style="position:absolute;top:25px;left:0;">'+$t("忽略相似信息")+'，<a href="javascript:;" action-type="create">'+$t("新建合作伙伴并关联")+"</a></div>"),t},hide:function(){return $(".dialog-btns",this.element).find(".j-tip").remove(),l.superclass.hide.call(this)},destroy:function(){return l.superclass.destroy.call(this)}}));i.exports=l});
define("crm-setting/order/dialog/searchfields",["crm-widget/dialog/dialog","../api","../util","crm-modules/common/util"],function(e,a,t){var n=e("crm-widget/dialog/dialog"),l=e("../api"),c=e("../util"),i=(e("crm-modules/common/util"),n.extend({attrs:{content:'<div class="crm-s-order-setting-searchfields-dialog"><p class="title">'+$t("crm.商品列表")+$t("字段")+'：</p><div class="clearfix sortable-wrap j-spu-fields"></div><p class="title">'+$t("crm.产品列表")+$t("字段")+'：</p><div class="clearfix sortable-wrap j-sku-fields"></div><p class="title">'+$t("crm.订单列表")+$t("字段")+'：</p><div class="clearfix sortable-wrap j-order-fields"></div></div>',title:$t("搜索字段")+$t("设置"),width:600,height:500,showBtns:!0,showScroll:!0},events:{"click .b-g-btn-cancel":"hide","click .b-g-btn":"_submit","click .mn-checkbox-item":"_changeCheckbox"},initialize:function(e){var a=Array.prototype.slice.call(arguments,1);a.unshift(e),i.superclass.initialize.apply(this,a)},_changeCheckbox:function(e){var a=$(e.currentTarget);return!!a.hasClass("mn-selected")||(9<a.parent().parent().find(".mn-selected").length?(c.error($t("crm.单个列表中的搜索字段不允许超过10个")),e.stopPropagation(),!1):void 0)},_submit:function(){var t,n,i,a,s=this;c.loading($(".b-g-btn",s.element),!0)||(t=[],n=[],i=[],s.$(".j-spu-fields .mn-selected").each(function(e,a){t.push({api_name:$(a).parent().data("apiname"),label:$(a).next().text()})}),s.$(".j-sku-fields .mn-selected").each(function(e,a){n.push({api_name:$(a).parent().data("apiname"),label:$(a).next().text()})}),s.$(".j-order-fields .mn-selected").each(function(e,a){i.push({api_name:$(a).parent().data("apiname"),label:$(a).next().text()})}),a=[{key:"search_fields",value:JSON.stringify({sku:n,spu:t,order:i}),type:4}],l.setSailConfig({configList:a}).done(function(e){e&&0===e.errorCode?(c.remind($t("保存成功")),s.trigger("addOrUpdate",a),s.hide()):c.error($t("crm.服务暂时不可用",{errorCode:e.errorCode}))}).complete(function(){c.loading($(".b-g-btn",s.element),!1)}))},_initSortable:function(){var n=this;this._sortableInited||(this._sortableInited=!0,e.async("base-sortable",function(e){var a=n.$(".sortable-wrap"),t={group:{name:"sortable-wrap",pull:!1,put:!0},animation:300,handle:".sortable-item",draggable:".sortable-item",filter:".fixed-item",scroll:!0};n.$$spuSortable=e.create(a[0],_.extend({},t,{group:{name:"sortable-warp-0"},onUpdate:function(e){}})),n.$$skuSortable=e.create(a[1],_.extend({},t,{group:{name:"sortable-warp-1"},onUpdate:function(e){}})),n.$$orderSortable=e.create(a[2],_.extend({},t,{group:{name:"sortable-warp-2"},onUpdate:function(e){}}))}))},show:function(r){r=r||{},_.isEmpty(r.order)&&(r.order=[{api_name:"name",label:$t("销售订单编号")}]),_.isEmpty(r.spu)&&(r.spu=[{api_name:"name",label:$t("商品名称")}]),_.isEmpty(r.sku)&&(r.sku=[{api_name:"name",label:$t("产品名称")}]);var m=this,e=i.superclass.show.call(this);return r.spuEnable?$(".j-spu-fields",m.$el).show().prev().show():$(".j-spu-fields",m.$el).hide().prev().hide(),l.batchGetDescribeByApiName({objectApiNameList:["SalesOrderObj","SPUObj","ProductObj"]}).done(function(e){var e=(e.Value||{}).objectDescribeList,a=e[0].fields,t=(a.pay_status={api_name:"pay_status",label:$t("付款状态"),type:"select_one"},e[2].fields),n=e[1].fields,i=["extend_obj_data_id","life_status_before_invalid","owner_department","_id","package","tenant_id","object_describe_api_name","object_describe_id","out_tenant_id","work_flow_id","lock_status"],s=$(".j-order-fields",m.$el),e=r.order,l=[],c=(e&&_.each(e,function(e){a[e.api_name]&&(e.label=a[e.api_name].label,l.push(e.api_name),s.append('<div class="item mn-checkbox-box'+("name"===e.api_name?" mn-disabled":"")+' sortable-item" data-apiname="'+e.api_name+'"><span class="mn-checkbox-item mn-selected'+("name"===e.api_name?" disabled-selected":"")+'"></span><span class="label">'+e.label+"</span></p>"))}),_.each(a,function(e){["auto_number","text","long_text","select_one","date","datetime"].indexOf(e.type)<0||0<=i.indexOf(e.api_name)||0<=l.indexOf(e.api_name)||s.append('<div class="item mn-checkbox-box sortable-item" data-apiname="'+e.api_name+'"><span class="mn-checkbox-item"></span><span class="label">'+e.label+"</span></p>")}),$(".j-spu-fields",m.$el)),e=r.spu,p=[],o=(e&&_.each(e,function(e){t[e.api_name]&&(e.label=t[e.api_name].label,p.push(e.api_name),c.append('<div class="item mn-checkbox-box'+("name"===e.api_name?" mn-disabled":"")+' sortable-item" data-apiname="'+e.api_name+'"><span class="mn-checkbox-item mn-selected'+("name"===e.api_name?" disabled-selected":"")+'"></span><span class="label">'+e.label+"</span></p>"))}),_.each(t,function(e){["auto_number","text","long_text","select_one","date","datetime"].indexOf(e.type)<0||0<=i.indexOf(e.api_name)||0<=p.indexOf(e.api_name)||c.append('<div class="item mn-checkbox-box sortable-item" data-apiname="'+e.api_name+'"><span class="mn-checkbox-item"></span><span class="label">'+e.label+"</span></p>")}),$(".j-sku-fields",m.$el)),e=r.sku,d=[];e&&_.each(e,function(e){n[e.api_name]&&(e.label=n[e.api_name].label,d.push(e.api_name),o.append('<div class="item mn-checkbox-box'+("name"===e.api_name?" mn-disabled":"")+' sortable-item" data-apiname="'+e.api_name+'"><span class="mn-checkbox-item mn-selected'+("name"===e.api_name?" disabled-selected":"")+'"></span><span class="label">'+e.label+"</span></p>"))}),_.each(n,function(e){["auto_number","text","long_text","select_one","date","datetime"].indexOf(e.type)<0||0<=i.indexOf(e.api_name)||0<=d.indexOf(e.api_name)||o.append('<div class="item mn-checkbox-box sortable-item" data-apiname="'+e.api_name+'"><span class="mn-checkbox-item"></span><span class="label">'+e.label+"</span></p>")})}).complete(function(){}),m._initSortable(),e},hide:function(){return i.superclass.hide.call(this)},destroy:function(){return this.$$data=null,i.superclass.destroy.call(this)}}));t.exports=i});
define("crm-setting/order/dialog/servicesetting",["crm-widget/dialog/dialog","base-modules/select/select","../api","./template/servicesetting-html","../util","crm-modules/common/util"],function(t,e,a){var n=t("crm-widget/dialog/dialog"),c=t("base-modules/select/select"),l=t("../api"),i=t("./template/servicesetting-html"),o=t("../util"),s=t("crm-modules/common/util"),d=n.extend({attrs:{content:'<div class="crm-g-form crm-c-field crm-s-order-setting-service-dialog j-service"></div>',title:$t("客服设置"),width:660,height:400,showBtns:!0,btnName:{save:$t("保存")}},events:{"click .b-g-btn-cancel":"hide","click .b-g-btn":"_submit","click .j-add-contactway":"_addContactWay","click .j-delete-contactway":"_deleteContactWay"},initSelect:function(t,e,a){var n=this,i=n.element;n.widgets[t]=new c({wrapper:i.find(e),options:a,zIndex:n.get("zIndex")+10}),n.widgets[t].on("change",function(t){})},checkoutFormat:function(t){$t("请填写正确格式")},_addContactWay:function(){this.$$data.contactWay.push({value:"",type:1});var t=this.$$data.contactWay.length-1,e="contantWay"+t,a=".select-wrap"+t,t='<div class="fm-item contact-way-item" data-index="'+t+'"><label class="fm-lb"><span class="select-wrap'+t+'" data-index='+t+'></span></label><input maxlength="20" class="b-g-ipt fm-ipt contact-way-ipt" placeholder='+$t("请输入")+'><div class="delete-contact-way j-delete-contactway"></div></div>';$(".contact-way-wrap").append(t),this.initSelect(e,a,[{value:1,name:$t("电话")},{value:2,name:"QQ"},{value:3,name:$t("微信")}]),8<=$(".contact-way-ipt").length&&$(".add-contact-way").css("display","none")},_deleteContactWay:function(t){t=t.currentTarget.offsetParent;delete this.widgets["contantWay"+t.dataset.index],t.remove(),$(".contact-way-ipt").length<8&&$(".add-contact-way").css("display","inline-block")},_submit:function(){var t,e,a=this,n=0,i=!0,c=[],s=$(".contact-way-ipt");for(t in a.widgets)c.push({type:a.widgets[t].value});_.each(s,function(t,e){1!=c[e].type&&2!=c[e].type||/^[\d]+$/.test(t.value)||(i=!1),c[n++].value=t.value.trim()}),i?o.loading($(".b-g-btn",a.element),!0)||(e={serviceHotline:JSON.stringify(c),updateType:2},l.updateContactOrLogo(e).done(function(t){t&&0===t.errorCode?(o.remind($t("保存成功")),a.trigger("addOrUpdate",e),a.hide()):o.error($t("crm.服务暂时不可用",{errorCode:t.errorCode}))}).complete(function(){o.loading($(".b-g-btn",a.element),!1)})):o.error($t("请输入有效的电话号或QQ号"))},show:function(t){var n=this,e=d.superclass.show.call(this);return $(".j-service",n.element).html(i({data:t})),n.$$data=s.deepClone(t)||{},n.widgets={},_.each(t.contactWay,function(t,e){var a="contantWay"+e;n.initSelect(a,".select-wrap"+e,[{value:1,name:$t("电话")},{value:2,name:"QQ"},{value:3,name:$t("微信")}]),t.type&&n.widgets[a].setValue(t.type)}),8<=$(".contact-way-ipt").length&&$(".add-contact-way").css("display","none"),e},hide:function(){return this.sb&&this.sb.destroy(),this.sb=null,d.superclass.hide.call(this)},destroy:function(){return this.sb&&this.sb.destroy(),this.sb=null,this.$$data=this.widgets=null,d.superclass.destroy.call(this)}});a.exports=d});
define("crm-setting/order/dialog/storesetting",["crm-widget/dialog/dialog","../api","base-uploader","../util","crm-modules/common/util"],function(t,e,i){var a=t("crm-widget/dialog/dialog"),o=t("../api"),l=t("base-uploader"),s=CRM.util.json,r=t("../util"),n=t("crm-modules/common/util"),d=a.extend({attrs:{content:'<div class="crm-g-form crm-c-field crm-s-order-setting-store-dialog"><div class="field-box"><div class="fm-item"><label>'+$t("企业名称")+'</label><br/><input class="b-g-ipt fm-ipt normal-ipt enterprise-name" maxlength="30" placeholder='+$t("请输入")+'></div><div class="fm-item"><label>'+$t("企业Logo")+'</label></br><div class="fm-wrap fm-ipt" data-fieldname="logo"><p class="img-tip">'+$t("crm.规定图片格式")+'</p><img class="img-logo" src=" "/><a class="img-uploder j-addimg">'+$t("上 传")+'</a><a class="img-uploder j-reset">'+$t("恢复默认")+'</a></div></div></div><div class="crm-action-field-image"><input class="input-file" type="file"/><div class="i-wrap"></div></div></div>',title:$t("供货商信息"),width:660,height:400,showBtns:!0,showScroll:!1,btnName:{save:$t("保存")}},events:{"click .b-g-btn-cancel":"hide","click .b-g-btn":"_submit","click .j-addimg":"_onAddImg","click .j-reset":"_onRestImg"},_onRestImg:function(){var t=CRMSETTING.ASSETS_PATH+"/images/order/logo.png";$(".img-logo").attr("src",t),this.$$data.logoPath="reset",this.$$data.isEditImg=!0},_onAddImg:function(t){var i=this,e=$(".input-file");i._createImgUpload(e,1,function(t){var t=t.path+"."+t.ext,e=n.getImgPath(t);i.$$data.logoPath=t,$(".img-logo").attr("src",e),i.$$data.isEditImg=!0},function(){}),e[0].click()},_createImgUpload:function(t,i,a,e){this.uploader||(this.imgUploader=new l({element:t,autoPrependPath:!1,h5UploadPath:FS.BASE_PATH+"/FSC/EM/File/UploadByStream",flashUploadPath:FS.BASE_PATH+"/FSC/EM/File/UploadByForm",h5Opts:{multiple:!0,accept:"*.jpg;*.gif;*.jpeg;*.png",filter:function(t){var e=[];return _.each(t,function(t){"jpg"==n.getFileType(t)&&e.push(t)}),e.length>i&&r.alert($t("crm.图片上传限制提示",{count:i})),e=e.slice(0,i),this.removeAllFile(),e}},flashOpts:{file_types:"*.jpg;*.gif;*.jpeg;*.png",file_types_description:$t("图片格式"),button_width:80,button_height:25},onSelect:function(t){this._upImgloaded||(this.startUpload(),this._upImgloaded=!0)},onSuccess:function(t,e){e=s.parse(e);e&&a&&a({path:e.TempFileName,ext:e.FileExtension})},onComplete:function(){this.removeAllFile(),this._upImgloaded=!1,e&&e(),this.destroy()},onFailure:function(){r.alert($t("图片上传失败请稍后再试"))}}))},_submit:function(){var e,i=this;r.loading($(".b-g-btn",i.element),!0)||(e={updateType:1,logoPath:i.$$data.isEditImg?i.$$data.logoPath:null,tenantName:$(".enterprise-name").val()},o.updateContactOrLogo(e).done(function(t){t&&0===t.errorCode?(r.remind($t("保存成功")),e.logoPath=t.data.logoPath,i.trigger("addOrUpdate",e),i.hide()):r.error($t("crm.服务暂时不可用",{errorCode:t.errorCode}))}).complete(function(){r.loading($(".b-g-btn",i.element),!1)}))},show:function(t){var e=d.superclass.show.call(this),i=(this.$$data=n.deepClone(t)||{},t.tenantName||t.enterpriseName),a=CRMSETTING.ASSETS_PATH+"/images/order/logo.png";return t.logoPath&&"reset"!==t.logoPath&&(a=n.getFscLink(t.logoPath)),$(".enterprise-name").val(i),$(".img-logo").attr("src",a),e},hide:function(){return d.superclass.hide.call(this)},destroy:function(){return this.imgUploader&&this.imgUploader.destroy&&this.imgUploader.destroy(),this.$$data=this._upImgloaded=null,d.superclass.destroy.call(this)}});i.exports=d});
define("crm-setting/order/dialog/template/intention-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="field-box"> <div class="int-fm-item fn-clear"> <label title="' + ((__t = $t("公司名称")) == null ? "" : __t) + '" class="int-fm-item-lb "><span>' + ((__t = $t("公司名称")) == null ? "" : __t) + '</span></label> <div class="int-fm-item-wrap">' + __e(data.companyName) + '</div> </div> <div class="int-fm-item fn-clear"> <label title="' + ((__t = $t("购买用户数")) == null ? "" : __t) + '" class="int-fm-item-lb "><span>' + ((__t = $t("购买用户数")) == null ? "" : __t) + '</span></label> <div class="int-fm-item-wrap">' + __e(data.usersNumber) + '</div> </div> <div class="int-fm-item fn-clear"> <label title="' + ((__t = $t("是否有对接开发人员")) == null ? "" : __t) + '" class="int-fm-item-lb "><span>' + ((__t = $t("是否有对接开发人员")) == null ? "" : __t) + '</span></label> <div class="int-fm-item-wrap">' + ((__t = data.hasDevelopper) == null ? "" : __t) + '</div> </div> <div class="int-fm-item fn-clear"> <label title="' + ((__t = $t("对接系统")) == null ? "" : __t) + '" class="int-fm-item-lb "><span>' + ((__t = $t("对接系统")) == null ? "" : __t) + '</span></label> <div class="int-fm-item-wrap">' + __e(data.integrateSystem) + '</div> </div> <div class="int-fm-item fn-clear"> <label title="' + ((__t = $t("业务对接模式")) == null ? "" : __t) + '" class="int-fm-item-lb "><span>' + ((__t = $t("业务对接模式")) == null ? "" : __t) + '</span></label> <div class="int-fm-item-wrap">' + ((__t = data.modulesToIntegrate) == null ? "" : __t) + '</div> </div> <div class="int-fm-item fn-clear"> <label title="' + ((__t = $t("业务对接模式")) == null ? "" : __t) + '" class="int-fm-item-lb "><span>' + ((__t = $t("其他对接业务")) == null ? "" : __t) + '</span></label> <div class="int-fm-item-wrap">' + ((__t = data.otherModulesToIntegrate) == null ? "" : __t) + '</div> </div> <div class="int-fm-item fn-clear"> <label title="" class="int-fm-item-lb "><span>' + ((__t = $t("对接需求或场景描述")) == null ? "" : __t) + '</span></label> <div class="int-fm-item-wrap">' + __e(data.scenariosToIntegrate) + '</div> </div> <div class="int-fm-item fn-clear"> <label title="" class="int-fm-item-lb "><span>' + ((__t = $t("补充信息")) == null ? "" : __t) + '</span></label> <div class="int-fm-item-wrap">' + __e(data.additionalInfo) + '</div> </div> <div class="int-fm-item fn-clear"> <label title="' + ((__t = $t("crm.联系人")) == null ? "" : __t) + '" class="int-fm-item-lb "><span>' + ((__t = $t("crm.联系人")) == null ? "" : __t) + '</span></label> <div class="int-fm-item-wrap">' + __e(data.contactName) + '</div> </div> <div class="int-fm-item fn-clear"> <label title="' + ((__t = $t("联系人电话")) == null ? "" : __t) + '" class="int-fm-item-lb "><span>' + ((__t = $t("联系人电话")) == null ? "" : __t) + '</span></label> <div class="int-fm-item-wrap">' + __e(data.phoneNumber) + '</div> </div> <div class="int-fm-item fn-clear"> <label title="' + ((__t = $t("联系人邮箱")) == null ? "" : __t) + '" class="int-fm-item-lb "><span>' + ((__t = $t("联系人邮箱")) == null ? "" : __t) + '</span></label> <div class="int-fm-item-wrap">' + __e(data.email) + "</div> </div> ";
            _.each(data.attachments, function(item) {
                __p += ' <div class="int-fm-item-attach fn-clear"> <div class="int-fm-item-attachname"><span style="margin-right:10px;" class="' + ((__t = FS.crmUtil.getFileIco(item.filename)) == null ? "" : __t) + '"></span>' + __e(item.filename) + '</div> <a class="j-view-attach" data-id="' + ((__t = item.id) == null ? "" : __t) + '" data-path="' + ((__t = item.path) == null ? "" : __t) + '" data-name="' + ((__t = item.filename) == null ? "" : __t) + '" data-url="" href="javascript:;">' + ((__t = $t("预览")) == null ? "" : __t) + '</a> <!-- <a class="j-down-attach" href="' + ((__t = FS.util.getFscLink(item.path, item.filename, !0)) == null ? "" : __t) + '">下载</a> --> <!-- <a href="' + ((__t = FS.util.getDfLink(item.path, item.filename, true, item.ext)) == null ? "" : __t) + '" class="int-fm-item-attachshow">' + ((__t = $t("预览")) == null ? "" : __t) + "</a> --> </div> ";
            });
            __p += " </div>";
        }
        return __p;
    };
});
define("crm-setting/order/dialog/template/orderstockoption-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-s-stock-setting-dialog"> ';
            _.each(options, function(item, index) {
                __p += ' <div class="selection-option j-option" data-id=' + ((__t = item.id) == null ? "" : __t) + '> <label for="option"> <input type="radio" name="option" value="' + ((__t = item.id) == null ? "" : __t) + '" ' + ((__t = value == item.id ? "checked" : "") == null ? "" : __t) + "> " + ((__t = item.title) == null ? "" : __t) + " ";
                if (item.tipHtml) {
                    __p += ' <div class="shiporder-tip"> <i class="shiporder-tip_icon">?</i> <div class="shiporder-tip_intro"> ' + ((__t = item.parseTipHtml(item.tipHtml)) == null ? "" : __t) + " </div> </div> ";
                }
                __p += " </label> </div> ";
            });
            __p += " </div>";
        }
        return __p;
    };
});
define("crm-setting/order/dialog/template/servicesetting-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="field-box"> <div class="contact-way-wrap"> ';
            _.each(data.contactWay, function(item, index) {
                __p += ' <div class="fm-item contact-way-item" data-index="' + ((__t = index) == null ? "" : __t) + '"> <label class="fm-lb"><span class="select-wrap' + ((__t = index) == null ? "" : __t) + '" data-index=' + ((__t = index) == null ? "" : __t) + '></span></label> <input maxlength="20" class="b-g-ipt fm-ipt contact-way-ipt" value=' + ((__t = item.value) == null ? "" : __t) + ' placeholder="' + ((__t = $t("请输入")) == null ? "" : __t) + '"> <div class="delete-contact-way j-delete-contactway"></div> </div> ';
            });
            __p += ' </div> <span class="add-contact-way j-add-contactway">+' + ((__t = $t("添加")) == null ? "" : __t) + "</span> </div>";
        }
        return __p;
    };
});
define("crm-setting/order/dialog/template/warehouseoption-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="selection-options"> ';
            _.each(options, function(item, index) {
                __p += ' <div class="selection-option" data-id=' + ((__t = item.id) == null ? "" : __t) + ">" + ((__t = item.name) == null ? "" : __t) + "</div> ";
            });
            __p += " </div>";
        }
        return __p;
    };
});
define("crm-setting/order/dialog/tyingproduct",["crm-widget/dialog/dialog","../common/table","crm-modules/common/util","../api","../util"],function(t,e,a){var d=t("crm-widget/dialog/dialog"),i=t("../common/table"),n=t("crm-modules/common/util"),c=t("../api"),r=t("../util"),o=d.extend({attrs:{content:'<div class="crm-s-order-setting-tying-dialog"><a class="add-product j-add-product">+'+$t("添加产品")+'</a><div class="tying-product-tb"></div></div>',title:$t("搭售产品"),width:1e3,showBtns:!0,showScroll:!1,btnName:{save:$t("保存")}},events:{"click .b-g-btn-cancel":"hide","click .b-g-btn":"_submit","click .j-add-product":"_onAddProduct"},initTable:function(t){var d=this;d.$$dt&&d.$$dt.destroy(),d.$$dt=new i({$el:$(".tying-product-tb",d.element),doStatic:!0,showPage:!1,columns:d.fetchColumns()}),d.$$dt.doStaticData(t),d.$$dt.on("trclick",function(t,e,a){a.hasClass("j-delete")&&d._onDel(t)})},fetchColumns:function(){return[{data:"name",title:$t("crm.产品名称"),width:250,dataType:1},{data:"specVos",title:$t("规格"),dataType:1,render:function(t,e,a,d){var i;return t&&t.length?Array.isArray(t)?(i="",_.each(t,function(t){i+=t.name+"-"+t.value+$t("；")}),i):t:"--"}},{data:"price",title:$t("价格"),dataType:1},{data:"unit",title:$t("单位"),dataType:1},{data:"amount",dataType:2,isEdit:!0,isRequired:!0,title:$t("数量")},{data:null,title:$t("操作"),width:100,render:function(t,e,a,d){return'<div class="j-operate"><a href="javascript:;" class="j-delete">'+$t("删除")+"</a></div>"}}]},_onAddProduct:function(){var a=this,e={apiname:"ProductObj",hideAdd:!0,isMultiple:!0,maxNum:10,filters:[{field_name:"product_status",field_values:[1],operator:"EQ",value_type:0}]};a.$$dt.curData&&(e.filterIds=_.pluck(a.$$dt.curData.data,"id")),a.$$dt.curData&&a.$$dt.curData.data&&(e.maxNum=10-a.$$dt.curData.data.length),t.async("crm-modules/components/pickselfobject/pickselfobject",function(t){a.pickObject=new t,a.pickObject.render(e),a.pickObject.on("select",function(t){var e;a.$$dt.curData&&a.$$dt.curData.data&&t.length>10-a.$$dt.curData.data.length?r.error($t("搭售产品不得超过10个")):(e=[],_.each(t,function(t){_.isObject(t)&&(t={name:t.name__tpd,specVos:t.specVos,price:t.price__tpd,unit:t.unit__tpd,amount:1,id:t._id},e.push(t))}),a.$$dt.addRow(e),a.$$dt.upDataScroll(),10<=a.$$dt.curData.data.length&&$(".j-add-product",a.element).css("display","none"))})})},_onDel:function(t){this.$$dt.delRow(t.__tbIndex),this.$$dt.curData.data.length<10&&$(".j-add-product",this.element).css("display","block")},_submit:function(){var e=this,a=e.$$dt.curData&&e.$$dt.curData.data,d=!0,i=[];r.loading($(".b-g-btn",e.element),!0)||(_.each(a,function(t){parseInt(t.amount)<=0&&(d=!1),i.push({productId:t.id,amount:t.amount})}),d?c.updateTiedProduct({tiedProductListArg:i}).done(function(t){t&&0===t.errorCode?(r.remind($t("保存成功")),e.trigger("addOrUpdate",a),e.hide()):r.error($t("crm.服务暂时不可用",{errorCode:t.errorCode}))}).complete(function(){r.loading($(".b-g-btn",e.element),!1)}):(r.error($t("数量必须大于0")),r.loading($(".b-g-btn",e.element),!1)))},show:function(t){t=n.deepClone(t)||[];var e=o.superclass.show.call(this);return this.initTable(t),10<=t.length&&$(".j-add-product",this.element).css("display","none"),e},hide:function(){return o.superclass.hide.call(this)},destroy:function(){var t=this;return t.$$dt&&t.$$dt.destroy&&t.$$dt.destroy(),t.pickObject&&t.pickObject.destroy&&t.pickObject.destroy(),o.superclass.destroy.call(this)}});a.exports=o});
define("crm-setting/order/dialog/warehouseSetting",["crm-widget/dialog/dialog","../api","../util","crm-modules/common/util","./template/warehouseoption-html"],function(e,s,t){var i=e("crm-widget/dialog/dialog"),a=e("../api"),o=e("../util"),n=e("crm-modules/common/util"),r=e("./template/warehouseoption-html"),l=i.extend({attrs:{content:'<div class="crm-s-warehouse-setting-dialog"><div class="crm-s-warehouse-setting-status"><span>'+$t("状态：")+'</span><span class="btn-switch j-switch"></span></div><p class="tips">'+$t("退换货单必须设置有效的审批流，下游才能够提交退换货单申请")+'</p><p class="title">'+$t("请设置一个仓库，作为下游通过订货通提交退换货申请时的默认仓库，上游审核时可修改。")+'</p><div class="warehouse-selection"><div class="selection-bar"><label>'+$t("退换货仓库")+'：</label><div class="selection-input"><span class="warehouse-name"></span><span class="icon-arrow"></span></div></div><div class="selection-wrapper" style="display: none"></div></div></div>',title:$t("退换货仓库"),width:500,showBtns:!0,showScroll:!1},warehouseList:[],warehouseId:"",events:{"click .b-g-btn-cancel":"hide","click .b-g-btn":"_submit","click .selection-input":"_toggle","click .selection-option":"_handleSelect","click .j-switch":"_handleSwitch"},_submit:function(){var e,s=this;o.loading($(".b-g-btn",s.element),!0)||(e=[{key:"return_exchange_warehouse_id",value:s.warehouseId||"",type:3},{key:"downstream_dht_support_return_exchange",value:$(".j-switch").hasClass("switch--on")?2:1,type:3}],a.setSailConfig({configList:e}).done(function(e){e&&0===e.errorCode?(o.remind($t("保存成功")),s.hide()):o.error($t("crm.服务暂时不可用",{errorCode:e.errorCode}))}).complete(function(){o.loading($(".b-g-btn",s.element),!1)}))},_toggle:function(){var e=$(".selection-wrapper");e.is(":hidden")?(e.show(),this._showOptions(),$(".icon-arrow").addClass("up")):(e.hide(),$(".icon-arrow").removeClass("up"))},_getWarehouses:function(){var s=this;a.getWarehouseList({upstreamEa:CRM.ea,rid:CRM.curEmpId,"X-out-link-type":"9",channel:"fsweb"}).done(function(e){0===e.errorCode&&(s.warehouseList=e.Value&&e.Value.warehouseVOs)})},_showOptions:function(){$(".selection-wrapper").html(r({options:this.warehouseList}))},_handleSelect:function(e){var s=$(e)[0].target.innerText,e=$(e)[0].target.dataset.id;this.warehouseId=e,$(".warehouse-name").text(s),$(".selection-wrapper").hide()},_handleSwitch:function(e){e=$(e.currentTarget);e.hasClass("switch--disabled")||(e.hasClass("switch--on")?e.removeClass("switch--on"):e.addClass("switch--on"))},show:function(e){var s=l.superclass.show.call(this);return this.$$data=n.deepClone(e)||{},this.warehouseId=e.return_exchange_warehouse_id,$(".warehouse-name").text(e&&e.warehouse_name||""),2===Number(e.downstream_dht_support_return_exchange)?$(".j-switch").addClass("switch--on switch--disabled"):$(".j-switch").removeClass("switch--on switch--disabled"),this._getWarehouses(),s},hide:function(){return l.superclass.hide.call(this)},destroy:function(){return this.$$data=null,l.superclass.destroy.call(this)}});t.exports=l});
define("crm-setting/order/guide/guide",["../util","../api","./template/tpl-html"],function(e,i,t){var o=e("../util"),n=e("../api"),l=e("./template/tpl-html"),e=Backbone.View.extend({initialize:function(e){this.$el=$(l({imageRoot:CRMSETTING.ASSETS_PATH}))},render:function(){this.$el.appendTo(document.body)},events:{"click .j-mask":"_onHide","click .j-close":"_onClose"},_onHide:function(){this.$el.hide()},_onClose:function(){var i=this;$(".j-check",i.$el)[0].checked?o.loading($(".j-close",i.$el),!0)||n.setBooted().done(function(e){i.$el.hide()}).complete(function(){o.loading($(".j-close",i.$el),!1)}):i.$el.hide()},destroy:function(){this.$el.remove()}});t.exports=e});
define("crm-setting/order/guide/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-s-order-guide"> <div class="ui-mask j-mask"></div> <div class="ui-card"> <div class="desc-wrap">' + ((__t = $t("crm.订货通介绍")) == null ? "" : __t) + '</div> <div class="flow-wrap"> <h3>' + ((__t = $t("请按照以下步骤进行初始化配置")) == null ? "" : __t) + '</h3> <ul class="flow-list b-g-clear"> <li class="flow-item"> <div class="thumb"><img src="' + ((__t = imageRoot) == null ? "" : __t) + '/images/order/guide-1.png"/></div> <div class="title">' + ((__t = $t("配置订货通客户")) == null ? "" : __t) + '</div> </li> <li class="flow-item flow-item-sep"> <i class="arrow">&nbsp;</i> </li> <li class="flow-item"> <div class="thumb"><img src="' + ((__t = imageRoot) == null ? "" : __t) + '/images/order/guide-2.png"/></div> <div class="title">' + ((__t = $t("产品信息维护")) == null ? "" : __t) + '</div> </li> <li class="flow-item flow-item-sep"> <i class="arrow">&nbsp;</i> </li> <li class="flow-item"> <div class="thumb"><img src="' + ((__t = imageRoot) == null ? "" : __t) + '/images/order/guide-3.png"/></div> <div class="title">' + ((__t = $t("企业号提交订单")) == null ? "" : __t) + '</div> </li> </ul> </div> <div class="oper-wrap"> <label class="lbl"><input type="checkbox" name="nomore" class="chk j-check">' + ((__t = $t("下次不再显示")) == null ? "" : __t) + '</label> <span class="btn b-g-btn j-close" data-loading-text="$t(\'知道了\')">' + ((__t = $t("知道了")) == null ? "" : __t) + "</span> </div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/order/homesetting/app",["../util","../api","./common","../module","./template/apptemplate/tpl-html","./template/apptemplate/search-html","./template/apptemplate/banner-html","./template/apptemplate/quickmenu-html","./template/apptemplate/promotion-html","./template/apptemplate/category-html","./template/apptemplate/product-html","./drag","./appreview","./categorynav","./checkbox"],function(e,t,a){var n=e("../util"),o=(e("../api"),e("./common")),i=e("../module"),r=e("./template/apptemplate/tpl-html"),s=e("./template/apptemplate/search-html"),c=e("./template/apptemplate/banner-html"),h=e("./template/apptemplate/quickmenu-html"),p=e("./template/apptemplate/promotion-html"),d=e("./template/apptemplate/category-html"),m=e("./template/apptemplate/product-html"),l=e("./drag"),u=e("./appreview"),g=e("./categorynav"),f=e("./checkbox"),y=i.extend(_.extend({name:"CRM-ORDER-HOME-APPSETTING",options:{promotionEnable:!1,linkAppStatus:{}},events:{"mouseenter .order-home__module-question":"showQ","mouseleave .order-home__module-question":"hideQ","click .drag-item .close":"deleteCard","click .j-preview":"previewSetting","click .j-save":"saveSetting","click .j-try-again":"getConfig","mouseenter .warn-icon":"showWarn","mouseleave .warn-icon":"hideWarn"},initialize:function(e){y.__super__.initialize.call(this,e)},render:function(){this.$el.html(r()),this.clearCards(),this.getConfig()},getConfig:function(){var n=[{title:$t("商品检索"),type:"101",checked:!0,order:1,module:"base"},{title:$t("广告Banner"),type:"102",checked:!0,order:2,module:"base"},{title:$t("快捷菜单"),type:"103",checked:!0,order:3,module:"base"},{title:$t("促销活动"),type:"104",checked:!0,order:4,module:"base"},{title:$t("推荐分类"),type:"105",checked:!1,order:5,module:"base"},{title:$t("推荐商品"),type:"106",checked:!0,order:6,module:"base"},{title:$t("促销商品"),type:"107",checked:!1,order:7,module:"base"},{title:$t("通知公告"),type:"announcement",checked:!1,order:1,module:"app",isActive:!1},{title:$t("伙伴学堂"),type:"partnerSchool",checked:!1,order:1,module:"app",isActive:!1}],r=this;this.showLoading(!0),$.when(this.getHomeSettingApi({type:2}),this.getRecommendCategories({place:"app"})).done(function(e,t){var a,o,i;r.showLoading(!1),e&&0===e.errorCode?(e.data.firstSet?r.options.promotionEnable||(n=_.filter(n,function(e){return"102"!==e.type&&"104"!==e.type&&"107"!==e.type})):(n=e.data.homeLayout&&JSON.parse(e.data.homeLayout).list,_.each(n,function(e){void 0!==e.type&&(e.type=String(e.type))})),r.$$state.categoryData=t||[],a=["announcement","partnerSchool"],o=/^www|local\.(\w+?)/.test(location.hostname),i=r.options.linkAppStatus||{},_.each(n,function(e){-1<a.indexOf(e.type)&&(e.isActive=!!i[e.type],o||(e.checked=!1))}),o||r.$(".order-home__module-app").hide(),r.formatData(n)):(r.$(".order-home__module").hide(),r.$(".order-home__preview").hide(),r.$(".order-home__operate").hide(),r.$(".order-home__empty").show())})},formatData:function(e){var t=this;this.$$state.list=[],this.$$state.baseList=[],this.$$state.appList=[],this.$$state.checkedList=[],_.each(e,function(e){"base"===e.module?t.$$state.baseList.push(e):"app"===e.module&&t.$$state.appList.push(e),e.checked&&t.$$state.checkedList.push(e)}),this.$$state.checkedList=_.sortBy(this.$$state.checkedList,function(e){return e.order}),this.$$state.list=_.clone(e),this.renderCards(),this.renderModule()},renderModule:function(){var a=this;this.$$components.baseCheckbox=new f({el:this.$(".order-home__module-base .order-home__module-checkbox"),list:this.$$state.baseList,selectFun:function(e,t){a.checkHandle(e,t)}}),this.$$components.baseCheckbox.render(this.$$state.baseList),this.$$components.baseCheckbox.on("showCategory",function(){a.showCategory()}),this.$$components.appCheckbox=new f({el:this.$(".order-home__module-app .order-home__module-checkbox"),list:this.$$state.appList,selectFun:function(e,t){a.checkHandle(e,t)}}),this.$$components.appCheckbox.render(this.$$state.appList)},showCategory:function(){var t=this;t.$$components.categoryNav||(t.$$components.categoryNav=new g,t.$$components.categoryNav.on("confirm",function(e){t.$$state.categoryData=e}),t.$$components.categoryNav.on("hide",function(){t.$$components.categoryNav.destroy(),t.$$components.categoryNav=null})),t.$$components.categoryNav.show(t.$$state.categoryData)},checkHandle:function(t,e){var a;"105"===(t=String(t))&&e&&this.showCategory(),e?(e=_.find(this.$$state.list,function(e){return e.type===t}),a=this.$$state.checkedList.length,e&&(e.order=a?this.$$state.checkedList[a-1].order+1:1,e.layout="left",e.checked=!0,"101"===e.type?this.$$state.checkedList.unshift(e):this.$$state.checkedList.push(e))):this.$$state.checkedList=_.filter(this.$$state.checkedList,function(e){return e.type!==t}),this.clearCards(),this.renderCards()},renderCards:function(){var t,e=_.template('<div class="order-setting__module drag-item empty-item"></div>');_.each(this.$$state.checkedList,function(e){switch(e.type){case"101":t=s({item:e});break;case"102":t=c({item:e});break;case"103":t=h({item:e});break;case"104":t=p({item:e});break;case"105":t=d({item:e});break;case"106":case"107":t=m({item:e});break;case"announcement":t=s({item:e});break;case"partnerSchool":t=m({item:e});break;default:t=null}t&&this.$(".order-home__preview-left").append(t)}),this.$(".order-home__preview-left").append(e),this.bindDrag(),this.checkCardAmount()},clearCards:function(){this.$(".order-home__preview-left").empty()},checkCardAmount:function(){var e=this.$(".drag-item");e&&1<e.length?(this.$(".order-home__preview-left").show(),this.$(".order-home__preview-empty").hide()):(this.$(".order-home__preview-left").hide(),this.$(".order-home__preview-empty").show())},bindDrag:function(){var e=this;this.$$components.dragObj&&this.$$components.dragObj._unbindEvents()&&(this.$$components.dragObj=null),this.$$components.dragObj=new l({$wrapper:e.$(".order-home__preview-content"),drag:".drag-item",target:".drag-item",onEnd:function(){e.dragEnd()}})},dragEnd:function(){var a=this,e=this.$(".order-home__preview-left .drag-item"),o=1;_.each(e,function(e){var t=$(e);t.hasClass("empty-item")||_.each(a.$$state.checkedList,function(e){String(e.type)===String(t.data("type"))&&(e.order=o,e.layout="left",o++)})})},deleteCard:function(e){e.preventDefault(),e.stopPropagation();var o=$(e.target),e=o.parent(".drag-item").dataset().type,i={};o.parent(".drag-item").remove(),this.$$state.checkedList=_.filter(this.$$state.checkedList,function(e){var t=String(e.type),a=String(o.parent(".drag-item").data("type"));return t===a&&(i=e),t!==a}),("base"===i.module?this.$$components.baseCheckbox:"").cancelCheck(e),this.checkCardAmount()},previewSetting:function(){var e=this.$$state.categoryData,e=_.filter(e,function(e){return"on"===e.status});new u({layout:this.$$state.checkedList,categorySelected:e}).render()},saveSetting:function(){var e=this,a=$(".j-save",this.$el);if(!n.loading(a,!0)){var o=!1,t=this.$$state.categoryData;if(_.each(e.$$state.list||[],function(t){t.checked=!1,_.each(e.$$state.checkedList||[],function(e){String(e.type)===String(t.type)&&((t=e).checked=!0,e.checked=!0)}),"105"==t.type&&t.checked&&(o=!0)}),o)if(!_.find(t,function(e){return"on"===e.status}))return n.remind(3,$t("当前推荐分类为空，不支持存储")),void n.loading(a,!1);var i={type:2,layout:JSON.stringify({list:this.$$state.list})};$.when(e.updateHomeApi(i),e.updateCategoryApi(t,o)).done(function(e,t){n.loading(a,!1),e&&t&&n.remind(1,$t("保存成功"))})}}},o));a.exports=y});
define("crm-setting/order/homesetting/appreview",["../util","./template/apptemplate/preview-html"],function(e,t,o){e("../util");var i=e("./template/apptemplate/preview-html"),e=Backbone.View.extend({options:{layout:[],categorySelected:[]},initialize:function(e){var t=_.sortBy(this.options.layout,function(e){return Number(e.order)});this.$el=$(i({layout:t,category:this.options.categorySelected,imageRoot:CRMSETTING.ASSETS_PATH}))},render:function(){this.$el.appendTo(document.body)},events:{"click .j-mask":"_onHide","click .j-close":"_onHide"},_onHide:function(){this.$el.remove()},destroy:function(){this.$el.remove()}});o.exports=e});
define("crm-setting/order/homesetting/categorynav",["crm-widget/dialog/dialog","./template/categorynav-html","base-uploader","../util","crm-modules/common/util"],function(t,e,a){var i=t("crm-widget/dialog/dialog"),n=t("./template/categorynav-html"),o=t("base-uploader"),s=CRM.util.json,c=t("../util"),l=t("crm-modules/common/util"),r=i.extend({attrs:{title:$t("推荐分类设置"),width:510,height:400,showBtns:!0},events:{"click .crm-g-checkbox":"toggleHandle","click .j-upload-img":"_onUploadImg","click .b-g-btn-cancel":"hide","click .b-g-btn":"_confirm"},initialize:function(){return r.superclass.initialize.apply(this,arguments)},show:function(t,e){var a=r.superclass.show.call(this);return this.$$state={initCategory:l.deepClone(t)||[],categoryState:{}},this.setContent(n({list:t,type:e,getFscLink:l.getFscLink})),a},toggleHandle:function(t){var t=$(t.target),e=t.data("code");t.is(".state-active"),this.$$state.categoryState[e]||(this.$$state.categoryState[e]={}),this.$$state.categoryState[e].status=t.is(".state-active")?"off":"on",t.toggleClass("state-active")},_onUploadImg:function(t){var a=this,e=$(t.target).parent(),i=e.find(".input-file"),n=e.find(".p-category-img"),o=$(t.target).data("code");a._createImgUpload(i,1,function(t){var e=t.path+"."+t.ext,e=l.getImgPath(e);$(n).attr("src",e),a.$$state.categoryState[o]||(a.$$state.categoryState[o]={}),a.$$state.categoryState[o].picture=[{path:t.path,ext:t.ext}]},function(){}),i[0].click()},_createImgUpload:function(t,a,i,e){this.uploader||(this.imgUploader=new o({element:t,autoPrependPath:!1,h5UploadPath:FS.BASE_PATH+"/FSC/EM/File/UploadByStream",flashUploadPath:FS.BASE_PATH+"/FSC/EM/File/UploadByForm",h5Opts:{multiple:!0,accept:"*.jpg;*.gif;*.jpeg;*.png",filter:function(t){var e=[];return _.each(t,function(t){"jpg"==l.getFileType(t)&&e.push(t)}),e.length>a&&c.alert($t("crm.图片上传限制提示",{count:a})),e=e.slice(0,a),this.removeAllFile(),e}},flashOpts:{file_types:"*.jpg;*.gif;*.jpeg;*.png",file_types_description:$t("图片格式"),button_width:80,button_height:25},onSelect:function(t){this._upImgloaded||(this.startUpload(),this._upImgloaded=!0)},onSuccess:function(t,e){e=s.parse(e);e&&i&&i({path:e.TempFileName,ext:e.FileExtension})},onComplete:function(){this.removeAllFile(),this._upImgloaded=!1,e&&e(),this.destroy()},onFailure:function(){c.alert($t("图片上传失败请稍后再试"))}}))},cancelCheck:function(e){var t=this.$(".crm-g-checkbox");_.each(t,function(t){t=$(t);t.data("type")==e&&t.toggleClass("state-active")})},_confirm:function(){var t=this.getValue();_.find(t,function(t){return"on"===t.status})?(this.trigger("confirm",t),this.hide()):c.remind(3,$t("当前推荐分类为空，不支持存储"))},getValue:function(){var a=this,t=a.$$state.initCategory;return _.each(t,function(t){var e=a.$$state.categoryState[t.categoryCode];e&&_.extend(t,e)}),t},hide:function(){return r.superclass.hide.call(this)},destroy:function(){return this.$$state=this._upImgloaded=null,r.superclass.destroy.call(this)}});a.exports=r});
define("crm-setting/order/homesetting/checkbox",["crm-modules/common/util"],function(t,e,i){t("crm-modules/common/util");var s=Backbone.View.extend({options:{list:[],selectFun:function(){}},template:_.template('##_.each(list, function(item) {if (item.module === \'app\'){##<div class="item"><div class="crm-g-checkbox {{item.checked ? \'state-active\' : \'\'}}" data-type="{{item.type}}" "></div><span class="text">{{item.title}}<span class="{{!item.isActive ? \'warn-icon\' : \'\'}}"></span></span></div>##} else {##<div class="item"><div class="crm-g-checkbox {{item.checked ? \'state-active\' : \'\'}}" data-type="{{item.type}}" "></div><span class="text">{{item.title}}</span>'+"<span class=\"opts j-opts-set {{(item.type == '7' || item.type == '105') && item.checked ? 'opts-active' : ''}}\">"+$t("设置")+"</span></div>##}})##"),events:{"click .crm-g-checkbox":"toggleHandle","click .j-opts-set":"showCategory"},initialize:function(t){s.__super__.initialize.call(this,t)},render:function(t){this.$el.html(this.template({list:t}))},toggleHandle:function(t){var e,t=$(t.target),i=t.data("type");"7"!=i&&"105"!=i||(e=t.parent().find(".j-opts-set"),t.is(".state-active")?e.hide():e.show()),this.options.selectFun(i,!t.is(".state-active")),t.toggleClass("state-active")},cancelCheck:function(i){var t=this.$(".crm-g-checkbox");_.each(t,function(t){var e,t=$(t);t.data("type")==i&&("7"!=i&&"105"!=i||(e=t.parent().find(".j-opts-set"),t.is(".state-active")?e.hide():e.show()),t.toggleClass("state-active"))})},showCategory:function(){this.trigger("showCategory")},getValue:function(){},destroy:function(){this.remove()}});i.exports=s});
define("crm-setting/order/homesetting/common",["../util","../api"],function(e,r,o){var t=e("../util"),n=e("../api");return{getHomeSettingApi:function(e){var r=$.Deferred();return n.getHomeSetting(e).done(function(e){r.resolve(e)}).error(function(){t.error($t("网络连接失败请稍候重试。")),r.resolve()}),r.promise()},getRecommendCategories:function(e){var r=$.Deferred();return n.getRecommendCategories(e).done(function(e){e=e.Value&&e.Value.recommendCategoryDatas||[];r.resolve(e)}).error(function(){t.error($t("网络连接失败请稍候重试。")),r.resolve()}),r.promise()},updateHomeApi:function(e){var r=$.Deferred();return n.updateHomeSetting(e).done(function(e){e.errorCode?(t.error($t("保存失败错误码")+(e&&e.errorCode?e.errorCode:$t("未知"))),r.resolve(!1)):r.resolve(!0)}),r.promise()},updateCategoryApi:function(e,r){var o=$.Deferred();return r?n.updateRecommendCategories({recommendCategoryDatas:e}).done(function(e){e.errorCode?(t.error($t("保存失败错误码")+(e&&e.errorCode?e.errorCode:$t("未知"))),o.resolve(!1)):o.resolve(!0)}):o.resolve(!0),o.promise()},showQ:function(){this.$(".order-home__question").show()},hideQ:function(){this.$(".order-home__question").hide()},showWarn:function(e){var e=$(e.target),r=_.template('<div class="warn-content">'+$t("该互联应用已被停用")+"<div>"),o=$(".warn-content",e);e.is(".warn-icon")&&(o&&o.length?o.show():e.append(r))},hideWarn:function(e){e=$(e.target),e=e.is(".warn-icon")?$(".warn-content",e):e;e&&e.hide()},showLoading:function(e){e?(this.$(".order-home__loading").show(),this.$(".order-home__empty").hide(),this.$(".order-home__module").hide(),this.$(".order-home__preview").hide(),this.$(".order-home__operate").hide()):(this.$(".order-home__loading").hide(),this.$(".order-home__module").show(),this.$(".order-home__preview").show(),this.$(".order-home__operate").show())}}});
define("crm-setting/order/homesetting/drag",[],function(r,a,t){window.document;function n(r){this.drag=r.drag,this.target=r.target,this.$wrapper=r.$wrapper||$("body"),this.onEnd=r.onEnd||null,this._render(),this._bindEvents(),this.flag=null}n.prototype=_.extend({constructor:n,$draging:null,$dropped:null,_render:function(){$(this.drag,this.$wrapper).each(function(r,a){a=$(a);a.attr("draggable","true"),a.attr("drag_id",_.uniqueId("drag_"))})},_bindEvents:function(){var r=this;this.$wrapper.on("dragstart",r.drag,$.proxy(r._ondragstart,r)),this.$wrapper.on("dragend",r.drag,$.proxy(r._ondragend,r)),this.$wrapper.on("dragenter",r.target,$.proxy(r._ondragenter,r)),this.$wrapper.on("dragover",r.target,$.proxy(r._dragover,r)),this.$wrapper.on("drop",r.drag,$.proxy(r._ondrop,r))},_ondragstart:function(r){r.originalEvent;r=$(r.currentTarget);this.$draging=r,this.$draging.addClass("drag-border").siblings().addClass("drag-border-none")},_ondragenter:function(r){var a,t=this,r=$(r.currentTarget);if(!(this.flag&&r&&$(this.flag).is(r)))return this.flag=r,!(!t.$draging||t.$draging.hasClass("empty-item")||(1==(a=t.$draging.data("type"))||13==a||14==a)&&r.parent().hasClass("order-home__preview-right")||101==t.$draging.data("type")||{101:!0,102:!0,103:!0,104:!0,105:!0,106:!0}[a]&&101==r.data("type")&&0===r.index())&&void(r.attr("drag_id")!=t.$draging.attr("drag_id")&&((t.$dropped=r).hasClass("empty-item")||t.$draging.parent()[0].className!==r.parent()[0].className||t.$draging.index()>=r.index()?r.before(t.$draging):r.after(t.$draging)))},_dragover:function(r){var a=r.originalEvent;$(r.currentTarget);return a.dataTransfer&&(a.dataTransfer.dropEffect="move"),r.preventDefault(),!1},_ondrop:function(r){var a=r.originalEvent;return a.dataTransfer&&a.dataTransfer.clearData(),r.preventDefault(),!1},_ondragend:function(r){return $(this.drag,this.$wrapper).each(function(r,a){a=$(a);a.removeClass("drag-border"),a.removeClass("drag-border-none")}),this.$draging=this.$dropped=null,this.onEnd&&this.onEnd(),!1},_unbindEvents:function(){var r=this;this.$wrapper.off("dragstart",r.drag,$.proxy(r._ondragstart,r)),this.$wrapper.off("dragend",r.drag,$.proxy(r._ondragend,r)),this.$wrapper.off("dragenter",r.target,$.proxy(r._ondragenter,r)),this.$wrapper.off("dragover",r.target,$.proxy(r._dragover,r)),this.$wrapper.off("drop",r.drag,$.proxy(r._ondrop,r))},destroy:function(){this._unbindEvents()}},Backbone.Events),t.exports=n});
define("crm-setting/order/homesetting/homesetting",["../util","../api","../module","./template/index-html","./web","./app"],function(e,t,n){var i=e("../util"),o=e("../api"),s=e("../module"),a=e("./template/index-html"),p=e("./web"),r=e("./app"),l=s.extend({name:"CRM-ORDER-HOME-SETTING",events:{"click .j-tabs":"_onSwitchView"},initialize:function(e){l.__super__.initialize.call(this,e),this.$el.html(a())},render:function(){var n=this;$.when(this.getPromotionEnable(),this.getHomeAppApi()).done(function(e,t){n.$$state.promotionEnable=e,n.$$state.linkAppStatus=t,n.showWebView()})},getPromotionEnable:function(){var e=this,n=$.Deferred();return e._getAjax&&(e._getAjax.abort(),e._getAjax=null),e._getAjax=i.FHHApi({url:"/EM1HNCRM/API/v1/object/promotion/service/is_promotion_enable",success:function(e){var t=!1;0===e.Result.StatusCode&&(t=e.Value.enable),n.resolve(t)},complete:function(){e._getAjax=null,n.resolve()}},{errorAlertModel:1}),n.promise()},getHomeAppApi:function(){var t=$.Deferred();return o.getHomeApp().done(function(e){e=e.Value||{};t.resolve(e)}).error(function(){i.error($t("网络连接失败请稍候重试。")),t.resolve()}),t.promise()},showWebView:function(){$(".tabs-web",this.$el).addClass("active"),$(".tabs-app",this.$el).removeClass("active"),this.$(".p-web-content").show(),this.$(".p-app-content").hide();var e=this;(e.$$components.websetting||(e.$$components.websetting=new p({el:this.$(".p-web-content"),promotionEnable:e.$$state.promotionEnable,linkAppStatus:e.$$state.linkAppStatus}))).render()},showAppView:function(){$(".tabs-web",this.$el).removeClass("active"),$(".tabs-app",this.$el).addClass("active"),this.$(".p-web-content").hide(),this.$(".p-app-content").show();var e=this;(e.$$components.appsetting||(e.$$components.appsetting=new r({el:this.$(".p-app-content"),promotionEnable:e.$$state.promotionEnable,linkAppStatus:e.$$state.linkAppStatus}))).render()},_onSwitchView:function(e){switch(e.target.dataset.type){case"web":this.showWebView();break;case"app":this.showAppView()}}});n.exports=l});
define("crm-setting/order/homesetting/template/apptemplate/banner-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="order-setting__module order-setting__image app drag-item" data-type="' + ((__t = item.type) == null ? "" : __t) + '"> <div class="title">' + ((__t = item.title) == null ? "" : __t) + '</div> <div class="close"></div> <div class="order-setting__image-wrapper"></div> </div>';
        }
        return __p;
    };
});
define("crm-setting/order/homesetting/template/apptemplate/category-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="order-setting__module order-setting__qucikmenu app drag-item" data-type="' + ((__t = item.type) == null ? "" : __t) + '"> <div class="title">' + ((__t = item.title) == null ? "" : __t) + '</div> <div class="close"></div> <div class="qucikmenu-list"> <div class="qucikmenu-item"> <div class="qucikmenu-item__img"></div><br/> <div class="qucikmenu-item__text">{$t("分类一")}}</div> </div> <div class="qucikmenu-item"> <div class="qucikmenu-item__img"></div><br/> <div class="qucikmenu-item__text">{$t("分类二")}}</div> </div> <div class="qucikmenu-item"> <div class="qucikmenu-item__img"></div><br/> <div class="qucikmenu-item__text">{$t("分类三")}}</div> </div> <div class="qucikmenu-item"> <div class="qucikmenu-item__img"></div><br/> <div class="qucikmenu-item__text">{$t("分类四")}}</div> </div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/order/homesetting/template/apptemplate/preview-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-s-order-preview"> <div class="ui-mask j-mask"></div> <div class="preview-content"> <div class="ui-close j-close">×</div> <div class="preview-content__wrap"> <div class="card card-top"></div> ';
            _.each(layout, function(item) {
                switch (item.type) {
                  case 101:
                    ;
                    __p += ' <div class="card card-search"></div> ';
                    break;

                  case 102:
                    ;
                    __p += ' <div class="card card-banner"></div> ';
                    break;

                  case 103:
                    ;
                    __p += ' <div class="card card-nav"></div> ';
                    break;

                  case 104:
                    ;
                    __p += ' <div class="card card-promotion"></div> ';
                    break;

                  case 105:
                    ;
                    __p += ' <div class="card-category"> <div class="card-title">' + ((__t = item.title) == null ? "" : __t) + '</div> <div class="card-category__list"> ';
                    _.each(category, function(a) {
                        __p += ' <div class="card-category__list-item"> <img class="card-category__list-img" src="' + ((__t = a.picturePath || imageRoot + "/images/dht-empty.png") == null ? "" : __t) + '"> <div class="card-category__list-name">' + ((__t = a.name) == null ? "" : __t) + "</div> </div> ";
                    });
                    __p += " </div> </div> ";
                    break;

                  case 106:
                  case 107:
                    ;
                    __p += ' <div class="card card-product"></div> ';
                    break;

                  case "announcement":
                    ;
                    __p += ' <div class="card card-announcement"></div> ';
                    break;

                  case "partnerSchool":
                    ;
                    __p += ' <div class="card card-partnerSchool"></div> ';
                    break;

                  default:
                    break;
                }
            });
            __p += " </div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/order/homesetting/template/apptemplate/product-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="order-setting__module order-setting__recproduct app drag-item" data-type="' + ((__t = item.type) == null ? "" : __t) + '"> <div class="title">' + ((__t = item.title) == null ? "" : __t) + '</div> <div class="close"></div> <div class="recproduct-content"> <div class="recproduct-item"> <div class="recproduct-item__img"></div> <div class="order-setting__text-content short"></div> </div> <div class="recproduct-item"> <div class="recproduct-item__img"></div> <div class="order-setting__text-content short"></div> </div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/order/homesetting/template/apptemplate/promotion-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="order-setting__module order-setting__promotion drag-item" data-type="' + ((__t = item.type) == null ? "" : __t) + '"> <div class="title">' + ((__t = item.title) == null ? "" : __t) + '</div> <div class="close"></div> <div class="promotion-content"> <div class="promotion-content__right"></div> <div class="promotion-content__left"> <div class="order-setting__text-content short"></div> <div class="order-setting__text-content"></div> <div class="order-setting__text-content"></div> </div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/order/homesetting/template/apptemplate/quickmenu-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="order-setting__module order-setting__qucikmenu app drag-item" data-type="' + ((__t = item.type) == null ? "" : __t) + '"> <div class="title">' + ((__t = item.title) == null ? "" : __t) + '</div> <div class="close"></div> <div class="qucikmenu-list"> <div class="qucikmenu-item"> <div class="qucikmenu-item__img"></div><br/> <div class="qucikmenu-item__text">' + ((__t = $t("新品上架")) == null ? "" : __t) + '</div> </div> <div class="qucikmenu-item"> <div class="qucikmenu-item__img"></div><br/> <div class="qucikmenu-item__text">' + ((__t = $t("促销商品")) == null ? "" : __t) + '</div> </div> <div class="qucikmenu-item"> <div class="qucikmenu-item__img"></div><br/> <div class="qucikmenu-item__text">' + ((__t = $t("收藏商品")) == null ? "" : __t) + '</div> </div> <div class="qucikmenu-item"> <div class="qucikmenu-item__img"></div><br/> <div class="qucikmenu-item__text">' + ((__t = $t("最近订购")) == null ? "" : __t) + "</div> </div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/order/homesetting/template/apptemplate/search-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="order-setting__module order-setting__search app drag-item" data-type="' + ((__t = item.type) == null ? "" : __t) + '"> <div class="title">' + ((__t = item.title) == null ? "" : __t) + '</div> <div class="close"></div> </div>';
        }
        return __p;
    };
});
define("crm-setting/order/homesetting/template/apptemplate/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-s-order-home crm-s-order-home-app"> <div class="order-home__loading"> <div class="order-home__loading-content"></div> </div> <div class="order-home__empty"> <div class="order-home__empty-content"> <div class="order-home__empty-image"></div> <div class="order-home__empty-text">' + ((__t = $t("获取服务器信息失败。")) == null ? "" : __t) + '<span class="try-again j-try-again">' + ((__t = $t("重试")) == null ? "" : __t) + '</span></div> </div> </div> <div class="order-home__module"> <div class="order-home__module-base"> <div class="order-home__module-title">' + ((__t = $t("基础模块")) == null ? "" : __t) + '</div> <div class="order-home__module-checkbox"></div> </div> <div class="order-home__module-app"> <div class="order-home__module-title"> <span>' + ((__t = $t("互联应用")) == null ? "" : __t) + '</span> <span class="crm-doclink order-home__module-question"></span> <div class="order-home__question"> <div class="order-home__question-title">' + ((__t = $t("互联应用展示")) == null ? "" : __t) + '</div> <div class="order-home__question-content">' + ((__t = $t("crm.确保授权与订货通一致")) == null ? "" : __t) + '</div> </div> </div> <div class="order-home__module-checkbox"></div> </div> </div> <div class="order-home__preview"> <div class="order-home__preview-content"> <div class="order-home__preview-left"></div> <div class="order-home__preview-empty"> <div class="order-home__preview-empty-image"></div> <div class="order-home__preview-empty-text">' + ((__t = $t("请在左侧选中你要展示的内容")) == null ? "" : __t) + '</div> </div> </div> </div> <div class="order-home__operate"> <span class="crm-btn b-g-btn-small order-home__operate-preview j-preview">' + ((__t = $t("预览")) == null ? "" : __t) + '</span> <span class="crm-btn crm-btn-primary b-g-btn-small order-home__operate-save j-save">' + ((__t = $t("保存")) == null ? "" : __t) + "</span> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/order/homesetting/template/categorynav-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-s-order-home-categorynav-dialog ' + ((__t = type === "web" ? "crm-s-order-home-categorynav-dialog-web" : "") == null ? "" : __t) + '"> <p class="categorynav-tip">' + ((__t = $t("请选择要在订货通首页展示的商品一级分类名称")) == null ? "" : __t) + '</p> <div class=""> <ul class="categorynav-list"> ';
            _.each(list, function(item) {
                __p += " ";
                var picture = item.picture && item.picture[0];
                var picturePath = picture ? getFscLink(picture.path + "." + picture.ext) : null;
                __p += ' <li class="categorynav-item"> <span class="crm-g-checkbox categorynav-item__right ' + ((__t = item.status === "on" ? "state-active" : "") == null ? "" : __t) + '" data-code="' + ((__t = item.categoryCode) == null ? "" : __t) + '" title="' + ((__t = item.name) == null ? "" : __t) + '"></span>' + ((__t = item.name) == null ? "" : __t) + ' <span class="categorynav-item__left"> <img class="categorynav-item__left-img p-category-img" src="' + ((__t = picturePath || "") == null ? "" : __t) + '"> <a class="j-upload-img" data-code="' + ((__t = item.categoryCode) == null ? "" : __t) + '">' + ((__t = $t("上传图片")) == null ? "" : __t) + '</a> <div class="crm-action-field-image"><input class="input-file" type="file"/><div class="i-wrap"></div></div> </span> </li> ';
            });
            __p += " </ul> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/order/homesetting/template/index-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-s-order-home-wrap"> <div class="order-home__tabs"> <ul> <li class="j-tabs tabs-web" data-type="web">' + ((__t = $t("web首页")) == null ? "" : __t) + '</li> <li class="j-tabs tabs-app" data-type="app">' + ((__t = $t("app首页")) == null ? "" : __t) + '</li> </ul> </div> <div class="order-home__content p-web-content"></div> <div class="order-home__content p-app-content"></div> </div>';
        }
        return __p;
    };
});
define("crm-setting/order/homesetting/template/webtemplate/image-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="order-setting__module order-setting__image drag-item" data-type="' + ((__t = item.type) == null ? "" : __t) + '"> <div class="title">' + ((__t = item.title) == null ? "" : __t) + '</div> <div class="close"></div> <div class="order-setting__image-wrapper"></div> <div class="order-setting__image-size left">' + ((__t = $t("尺寸 374")) == null ? "" : __t) + '*110</div> <div class="order-setting__image-size right">' + ((__t = $t("尺寸 176")) == null ? "" : __t) + "*110</div> </div>";
        }
        return __p;
    };
});
define("crm-setting/order/homesetting/template/webtemplate/imageText-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="order-setting__module order-setting__image-text drag-item" data-type="' + ((__t = item.type) == null ? "" : __t) + '"> <div class="title">' + ((__t = item.title) == null ? "" : __t) + '</div> <div class="close"></div> <div class="order-setting__image-text__item first"> <div class="order-setting__image-text__image"></div> <div class="order-setting__image-text__text"></div> </div> <div class="order-setting__image-text__item"> <div class="order-setting__image-text__image"></div> <div class="order-setting__image-text__text"></div> </div> <div class="order-setting__image-text__item"> <div class="order-setting__image-text__image"></div> <div class="order-setting__image-text__text"></div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/order/homesetting/template/webtemplate/kpi-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="order-setting__module order-setting__kpi drag-item" data-type="' + ((__t = item.type) == null ? "" : __t) + '"> <div class="title">' + ((__t = item.title) == null ? "" : __t) + '</div> <div class="close"></div> <div class="order-setting__kpi-bold"></div> <div class="order-setting__kpi-bolder"></div> </div>';
        }
        return __p;
    };
});
define("crm-setting/order/homesetting/template/webtemplate/lineChart-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="order-setting__module order-setting__linechart drag-item" data-type="' + ((__t = item.type) == null ? "" : __t) + '"> <div class="title">' + ((__t = item.title) == null ? "" : __t) + '</div> <div class="close"></div> <div class="order-setting__linechart-content"> <canvas class="canvas-chart ' + ((__t = "canvas-chart" + item.type) == null ? "" : __t) + '" width="343" height="74"></canvas> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/order/homesetting/template/webtemplate/moreImage-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="order-setting__module order-setting__more-image drag-item" data-type="' + ((__t = item.type) == null ? "" : __t) + '"> <div class="title">' + ((__t = item.title) == null ? "" : __t) + '</div> <div class="close"></div> <div class="order-setting__wrapper"> <div class="order-setting__item first"> <div class="order-setting__item-content"> <div class="order-setting__item-image"></div> <div class="order-setting__item-size">' + ((__t = $t("尺寸 100")) == null ? "" : __t) + '*100</div> </div> <div class="order-setting__item-span"></div> </div> <div class="order-setting__item"> <div class="order-setting__item-content"> <div class="order-setting__item-image"></div> <div class="order-setting__item-size">' + ((__t = $t("尺寸 100")) == null ? "" : __t) + '*100</div> </div> <div class="order-setting__item-span"></div> </div> <div class="order-setting__item"> <div class="order-setting__item-content"> <div class="order-setting__item-image"></div> <div class="order-setting__item-size">' + ((__t = $t("尺寸 100")) == null ? "" : __t) + '*100</div> </div> <div class="order-setting__item-span"></div> </div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/order/homesetting/template/webtemplate/search-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="order-setting__module order-setting__search drag-item" data-type="' + ((__t = item.type) == null ? "" : __t) + '"> <div class="title">' + ((__t = item.title) == null ? "" : __t) + '</div> <div class="close"></div> <div class="order-setting__search-content"> <button class="order-setting__search-icon h-search-btn"></button> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/order/homesetting/template/webtemplate/text-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="order-setting__module order-setting__text drag-item" data-type="' + ((__t = item.type) == null ? "" : __t) + '"> <div class="title">' + ((__t = item.title) == null ? "" : __t) + '</div> <div class="close"></div> <div class="order-setting__text-content long first"></div> <div class="order-setting__text-content long"></div> <div class="order-setting__text-content long"></div> <div class="order-setting__text-content middle"></div> <div class="order-setting__text-content short"></div> </div>';
        }
        return __p;
    };
});
define("crm-setting/order/homesetting/template/webtemplate/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-s-order-home"> <div class="order-home__loading"> <div class="order-home__loading-content"></div> </div> <div class="order-home__empty"> <div class="order-home__empty-content"> <div class="order-home__empty-image"></div> <div class="order-home__empty-text">' + ((__t = $t("获取服务器信息失败。")) == null ? "" : __t) + '<span class="try-again j-try-again">' + ((__t = $t("重试")) == null ? "" : __t) + '</span></div> </div> </div> <div class="order-home__module"> <div class="order-home__module-base"> <div class="order-home__module-title">' + ((__t = $t("基础模块")) == null ? "" : __t) + '</div> <div class="order-home__module-checkbox"></div> </div> <div class="order-home__module-app"> <div class="order-home__module-title"> <span>' + ((__t = $t("互联应用")) == null ? "" : __t) + '</span> <span class="crm-doclink order-home__module-question"></span> <div class="order-home__question"> <div class="order-home__question-title">' + ((__t = $t("互联应用展示")) == null ? "" : __t) + '</div> <div class="order-home__question-content">' + ((__t = $t("crm.确保授权与订货通一致")) == null ? "" : __t) + '</div> </div> </div> <div class="order-home__module-checkbox"></div> </div> <div class="order-home__module-bi"> <div class="order-home__module-title">' + ((__t = $t("报表应用")) == null ? "" : __t) + '</div> <div class="order-home__module-checkbox"></div> </div> </div> <div class="order-home__preview"> <div class="order-home__preview-head"></div> <div class="order-home__preview-content"> <div class="order-home__preview-left"></div> <div class="order-home__preview-right"></div> <div class="order-home__preview-empty"> <div class="order-home__preview-empty-image"></div> <div class="order-home__preview-empty-text">' + ((__t = $t("请在左侧选中你要展示的内容")) == null ? "" : __t) + '</div> </div> </div> </div> <div class="order-home__operate"> <span class="crm-btn b-g-btn-small order-home__operate-preview j-preview">' + ((__t = $t("预览")) == null ? "" : __t) + '</span> <span class="crm-btn crm-btn-primary b-g-btn-small order-home__operate-save j-save">' + ((__t = $t("保存")) == null ? "" : __t) + "</span> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/order/homesetting/web",["../util","../api","./common","./template/webtemplate/tpl-html","../module","./template/webtemplate/text-html","./template/webtemplate/search-html","./template/webtemplate/moreImage-html","./template/webtemplate/image-html","./template/webtemplate/imageText-html","./template/webtemplate/kpi-html","./template/webtemplate/lineChart-html","./drag","./categorynav","./checkbox"],function(e,t,i){var r=e("../util"),o=(e("../api"),e("./common")),a=e("./template/webtemplate/tpl-html"),s=e("../module"),n=e("./template/webtemplate/text-html"),h=e("./template/webtemplate/search-html"),c=e("./template/webtemplate/moreImage-html"),l=e("./template/webtemplate/image-html"),d=e("./template/webtemplate/imageText-html"),m=e("./template/webtemplate/kpi-html"),p=e("./template/webtemplate/lineChart-html"),u=e("./drag"),y=e("./categorynav"),g=e("./checkbox"),f=s.extend(_.extend({name:"CRM-ORDER-HOME-SETTING",options:{promotionEnable:!1,linkAppStatus:{}},events:{"mouseenter .order-home__module-question":"showQ","mouseleave .order-home__module-question":"hideQ","click .drag-item .close":"deleteCard","click .j-preview":"previewSetting","click .j-save":"saveSetting","click .j-try-again":"getConfig","mouseenter .warn-icon":"showWarn","mouseleave .warn-icon":"hideWarn"},initialize:function(e){f.__super__.initialize.call(this,e)},render:function(){this.$el.html(a()),this.clearCards(),this.getConfig()},getConfig:function(){var s=[{title:$t("广告Banner"),layout:"left",type:"1",checked:!0,order:1,module:"base"},{title:$t("新品上架"),layout:"left",type:"2",checked:!0,order:2,module:"base"},{title:$t("商品检索"),layout:"right",type:"3",checked:!0,order:4,module:"base"},{title:$t("促销活动"),layout:"right",type:"4",checked:!0,order:5,module:"base"},{title:$t("应用通知"),layout:"left",type:"5",checked:!0,order:3,module:"base"},{title:$t("常见问题"),layout:"right",type:"6",checked:!0,order:6,module:"base"},{title:$t("促销商品"),layout:"left",type:"8",checked:!1,order:8,module:"base"},{title:$t("推荐分类"),layout:"left",type:"7",checked:!1,order:7,module:"base"},{title:$t("通知公告"),layout:"left",type:"announcement",checked:!1,order:1,module:"app",isActive:!1},{title:$t("伙伴学堂"),layout:"left",type:"partnerSchool",checked:!1,order:1,module:"app",isActive:!1},{title:$t("互联网盘"),layout:"left",type:"internetDisk",checked:!1,order:1,module:"app",isActive:!1},{title:$t("订单金额/笔数KPI图"),layout:"right",type:"11",checked:!1,order:1,module:"bi"},{title:$t("退货金额/笔数KPI图"),layout:"right",type:"12",checked:!1,order:1,module:"bi"},{title:$t("订单金额趋势图"),layout:"left",type:"13",checked:!1,order:1,module:"bi"},{title:$t("订单笔数趋势图"),layout:"left",type:"14",checked:!1,order:1,module:"bi"}],n=this;this.showLoading(!0),$.when(this.getHomeSettingApi({type:1}),this.getRecommendCategories({place:"web"})).done(function(e,t){if(n.showLoading(!1),e&&0===e.errorCode){var i=[];if(e.data.homeLayout)try{i=JSON.parse(e.data.homeLayout).list,_.each(i,function(e){void 0!==e.type&&(e.type=String(e.type))})}catch(e){}s=e.data.firstSet?(s=n.options.promotionEnable?s:_.filter(s,function(e){return"1"!==e.type&&"4"!==e.type&&"8"!==e.type})).concat(i):i,n.$$state.categoryData=t||[];var o=["announcement","internetDisk","partnerSchool"],a=/^www|local\.(\w+?)/.test(location.hostname),r=n.options.linkAppStatus||{};_.each(s,function(e){-1<o.indexOf(e.type)&&(e.isActive=!!r[e.type],a||(e.checked=!1))}),a||n.$(".order-home__module-app").hide(),n.formatData(s)}else n.$(".order-home__module").hide(),n.$(".order-home__preview").hide(),n.$(".order-home__operate").hide(),n.$(".order-home__empty").show()})},formatData:function(e){var t=this;this.$$state.list=[],this.$$state.baseList=[],this.$$state.appList=[],this.$$state.biList=[],this.$$state.checkedList=[],_.each(e,function(e){"base"===e.module?t.$$state.baseList.push(e):"app"===e.module?t.$$state.appList.push(e):"bi"===e.module&&t.$$state.biList.push(e),e.checked&&t.$$state.checkedList.push(e)}),this.$$state.checkedList=_.sortBy(this.$$state.checkedList,function(e){return e.order}),this.$$state.list=_.clone(e),this.renderCards(),this.renderModule()},renderModule:function(){var i=this;this.$$components.baseCheckbox=new g({el:this.$(".order-home__module-base .order-home__module-checkbox"),list:this.$$state.baseList,selectFun:function(e,t){i.checkHandle(e,t)}}),this.$$components.baseCheckbox.render(this.$$state.baseList),this.$$components.baseCheckbox.on("showCategory",function(){i.showCategory()}),this.$$components.appCheckbox=new g({el:this.$(".order-home__module-app .order-home__module-checkbox"),list:this.$$state.appList,selectFun:function(e,t){i.checkHandle(e,t)}}),this.$$components.appCheckbox.render(this.$$state.appList),this.$$components.biCheckbox=new g({el:this.$(".order-home__module-bi .order-home__module-checkbox"),list:this.$$state.biList,selectFun:function(e,t){i.checkHandle(e,t)}}),this.$$components.biCheckbox.render(this.$$state.biList)},showCategory:function(){var t=this;t.$$components.categoryNav||(t.$$components.categoryNav=new y,t.$$components.categoryNav.on("confirm",function(e){t.$$state.categoryData=e}),t.$$components.categoryNav.on("hide",function(){t.$$components.categoryNav.destroy(),t.$$components.categoryNav=null})),t.$$components.categoryNav.show(t.$$state.categoryData,"web")},checkHandle:function(t,e){var i;"7"==(t=String(t))&&e&&this.showCategory(),e?(e=_.find(this.$$state.list,function(e){return e.type===t}),i=this.$$state.checkedList.length,e&&(e.order=i?this.$$state.checkedList[i-1].order+1:1,e.layout="left",e.checked=!0,this.$$state.checkedList.push(e))):this.$$state.checkedList=_.filter(this.$$state.checkedList,function(e){return e.type!==t}),this.clearCards(),this.renderCards()},renderCards:function(){var t,i=this,e=_.template('<div class="order-setting__module drag-item empty-item"></div>'),o=_.template('<div class="order-setting__module drag-item empty-item"></div>');_.each(this.$$state.checkedList,function(e){if("bi"===e.module&&/^BI_\w+/.test(e.type))t=p({item:e});else switch(e.type){case"1":t=l({item:e});break;case"2":case"7":case"8":case"partnerSchool":t=c({item:e});break;case"3":t=h({item:e});break;case"4":case"5":case"6":case"internetDisk":t=n({item:e});break;case"announcement":t=d({item:e});break;case"11":case"12":t=m({item:e});break;case"13":case"14":t=p({item:e});break;default:t=null}t&&(("left"===e.layout?this.$(".order-home__preview-left"):this.$(".order-home__preview-right")).append(t),"13"===e.type&&i.initChart(e),"14"===e.type)&&i.initChart(e)}),this.$(".order-home__preview-left").append(e),this.$(".order-home__preview-right").append(o),this.bindDrag(),this.checkCardAmount()},initChart:function(e){var i,e=e.type,o=this.el.querySelector(".canvas-chart"+e).getContext("2d");14===e?(i=[{y:40},{y:61},{y:30},{y:45},{y:20},{y:30},{y:35},{y:10}],o.lineWidth=4,o.strokeStyle="#EDF2FA",o.moveTo(0,74),i.forEach(function(e,t){o.lineTo(313/i.length*(t+1),e.y)}),o.stroke()):13===e&&(o.fillStyle="#EDF2FA",[{h:56},{h:23},{h:35},{h:43},{h:35},{h:31},{h:43},{h:28},{h:23},{h:13},{h:23}].forEach(function(e,t){var i=74-e.h;o.fillRect(12*(t+1)+17*t,i,17,e.h)}))},clearCards:function(){this.$(".order-home__preview-left").empty(),this.$(".order-home__preview-right").empty()},checkCardAmount:function(){var e=this.$(".drag-item");e&&2<e.length?(this.$(".order-home__preview-left").show(),this.$(".order-home__preview-right").show(),this.$(".order-home__preview-empty").hide()):(this.$(".order-home__preview-left").hide(),this.$(".order-home__preview-right").hide(),this.$(".order-home__preview-empty").show())},bindDrag:function(){var e=this;this.$$components.dragObj&&this.$$components.dragObj._unbindEvents()&&(this.$$components.dragObj=null),this.$$components.dragObj=new u({$wrapper:e.$(".order-home__preview-content"),drag:".drag-item",target:".drag-item",onEnd:function(){e.dragEnd()}})},dragEnd:function(){var i=this,e=this.$(".order-home__preview-left .drag-item"),t=this.$(".order-home__preview-right .drag-item"),o=1;_.each(e,function(e){var t=$(e);t.hasClass("empty-item")||_.each(i.$$state.checkedList,function(e){String(e.type)===String(t.data("type"))&&(e.order=o,e.layout="left",o++)})}),_.each(t,function(e){var t=$(e);t.hasClass("empty-item")||_.each(i.$$state.checkedList,function(e){String(e.type)===String(t.data("type"))&&(e.order=o,e.layout="right",o++)})})},deleteCard:function(e){e.preventDefault(),e.stopPropagation();var o=$(e.target),e=o.parent(".drag-item").dataset().type,a={};o.parent(".drag-item").remove(),this.$$state.checkedList=_.filter(this.$$state.checkedList,function(e){var t=String(e.type),i=String(o.parent(".drag-item").data("type"));return t===i&&(a=e),t!==i}),("base"===a.module?this.$$components.baseCheckbox:"app"===a.module?this.$$components.appCheckbox:this.$$components.biCheckbox).cancelCheck(e),this.checkCardAmount()},previewSetting:function(){var t=this,e=window.location.hostname.split(".")[1],i=(["ceshi112","fxiaoke"].indexOf(e)<0&&(e="ceshi112"),window.open("http://dht."+e+".com/prerelease/index.html#/preview"));setTimeout(function(){var e=JSON.stringify({firstSet:!1,homeLayout:JSON.stringify({list:t.$$state.checkedList})});i.postMessage(e,"*")},1e3)},saveSetting:function(){var e=this,i=$(".j-save",this.$el);if(!r.loading(i,!0)){var o=!1,t=e.$$state.categoryData;if(_.each(e.$$state.list||[],function(t){t.checked=!1,_.each(e.$$state.checkedList||[],function(e){String(e.type)===String(t.type)&&((t=e).checked=!0,e.checked=!0)}),"7"===String(t.type)&&t.checked&&(o=!0)}),o)if(!_.find(t,function(e){return"on"===e.status}))return r.remind(3,$t("当前推荐分类为空，不支持存储")),void r.loading(i,!1);var a={type:1,layout:JSON.stringify({list:this.$$state.list})};$.when(e.updateHomeApi(a),e.updateCategoryApi(t,o)).done(function(e,t){r.loading(i,!1),e&&t&&r.remind(1,$t("保存成功"))})}}},o));i.exports=f});
define("crm-setting/order/module",[],function(t,e,n){var o=Backbone.View.extend({name:"base-module",initialize:function(t){this.$$components={},this.$$dt=null,this.$$state={}},render:function(){return this},destroyComponents:function(){var t,e=this;for(t in e.$$components)e.$$components[t]&&(e.$$components[t].destroy(),delete e.$$components[t]);e.$$dt&&(e.$$dt.destroy(),delete e.$$dt)},destroy:function(){var t=this;t.$$state={},t.destroyComponents(),t.$el.html("")}});n.exports=o});
define("crm-setting/order/noticejump/noticejump",["../module","./template/tpl-html"],function(t,e,i){var n=t("../module"),l=t("./template/tpl-html"),o=n.extend({name:"CRM-ORDER-NOTICEJUMP",events:{"click .dht-manage-text a":"toClick"},initialize:function(t){o.__super__.initialize.call(this,t)},render:function(){this.$el.html(l())},toClick:function(){window.location.hash="#paasapp/index/=/appId_FSAID_PaaS_6841442477632",this.destroy()},destroy:function(){this.$el.remove()}});i.exports=o});
define("crm-setting/order/noticejump/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="dht_manage-migration-guide"> <div class="dht-manage"> <img src="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/biz-manage.png") == null ? "" : __t) + '"> </div> <div class="dht-manage-text"> ' + ((__t = $t("dht.crm.stop_iteration")) == null ? "" : __t) + '<a href="javascript:;">' + ((__t = $t("dht.component.setting.app.gotosetting")) == null ? "" : __t) + "></a> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/order/order",["./util","./api","./template/tpl-html","./module","./account/account","./process/process","./setting/setting","./homesetting/homesetting","./agreement/agreement","./receipt/receipt","./statement/statement","./noticejump/noticejump"],function(e,t,n){var o=e("./util"),r=(e("./api"),e("./template/tpl-html")),i=e("./module"),s=e("./account/account"),c=e("./process/process"),a=e("./setting/setting"),m=e("./homesetting/homesetting"),p=e("./agreement/agreement"),u=e("./receipt/receipt"),l=e("./statement/statement"),d=(OrderNoticeJump=e("./noticejump/noticejump"),i.extend({name:"CRM-ORDER",events:{"click .crm-tab span":"_onTab"},initialize:function(e){d.__super__.initialize.call(this,e),this.setElement(e.wrapper)},render:function(){var e=this;e.currentPage="bankaccount",e.destroyComponents(),e.$el.html(r({})),e._switchPage(0),FS.util.getUserAttribute("eSign")&&$(".j-esign",e.$el).show()},_onTab:function(e){var e=$(e.currentTarget),t=e.index();e.addClass("cur").siblings().removeClass("cur"),this.currentPage=e.attr("data-type"),this._switchPage(t)},_switchPage:function(t){var n=this;this.getOldSailConfig().then(function(e){e.Value.oldDht?(e=n.currentPage.replace(/(\w)/,function(e){return"_render"+e.toUpperCase()}),n.$(".tab-con > .item-wrap").eq(t).show().siblings().hide(),n[e]&&n[e]()):n._renderNoticeHump()})},_renderNoticeHump:function(){var e=this;(e.$$components.noticeJump||(e.$$components.noticeJump=new OrderNoticeJump({el:e.$(".bankaccount-box")}))).render()},_renderBankaccount:function(){var e=this;(e.$$components.bankaccount||(e.$$components.bankaccount=new s({el:e.$(".bankaccount-box")}))).render()},_renderProcess:function(){var e=this;(e.$$components.process||(e.$$components.process=new c({el:e.$(".process-box")}))).render()},getOldSailConfig:function(e){return o.FHHApi({url:"/EM1HDHT/API/v1/object/dht_config/service/is_old_black_list",data:{},success:function(e){if(0===e.Result.StatusCode)return e;reject()}},{errorAlertModel:1})},_renderHomesetting:function(){var e=this;(e.$$components.homesetting||(e.$$components.homesetting=new m({el:e.$(".homesetting-box")}))).render()},_renderReceipt:function(){var e=this;(e.$$components.receipt||(e.$$components.receipt=new u({el:e.$(".receipt-box")}))).render()},_renderStatement:function(){var e=this;(e.$$components.statement||(e.$$components.statement=new l({el:e.$(".statement-box")}))).render()},_renderAgreement:function(){(this.$$components.agreement||(this.$$components.agreement=new p({el:this.$(".agreement-box")}))).render()},_renderOnlineservice:function(){location.hash="app/onlineservice/imaccess"},_renderSetting:function(){(this.$$components.setting||(this.$$components.setting=new a({el:this.$(".setting-box")}))).render()}}));n.exports=d});
define("crm-setting/order/process/process",["../util","../api","./template/tpl-html","../module","./selector"],function(e,o,t){var l=e("../util"),d=e("../api"),n=e("./template/tpl-html"),r=e("../module"),i=e("./selector"),s=r.extend({name:"CRM-ORDER-PROCESS",events:{"click .j-add":"createFlowNode","click .j-add-node":"addFlowNode","click .j-del-node":"delFlowNode"},initialize:function(e){s.__super__.initialize.call(this,e),this.nodes=[],this.editable=!1,this.$el.html(n({list:this.nodes,editable:this.editable}))},render:function(){this._getData()},_refresh:function(){this.$el.html(n({list:this.nodes,editable:this.editable})),this.editable||this.$el.find(".process-tip").show()},_getData:function(){var o=this;d.getCrmBasicConfig({key:"21"}).done(function(e){e&&e.Value&&"1"===e.Value.value&&(o.editable=!0),d.getWorkflowInfo().done(function(e){e&&0===e.errorCode?(o.nodes=e.data||[],o._refresh()):l.error($t("加载订货流程配置失败错误码")+(e?e.errorCode:$t("未知")))})})},_addFlowNode:function(o){var t=this,e=t.nodes.concat(o);o.level=t.nodes.length+1,o.approverType=parseInt(o.approverType),d.setWorkflowInfo(e).done(function(e){e&&0===e.errorCode?(t.nodes.push(o),t.$el.html(n({list:t.nodes,editable:t.editable}))):l.error($t("添加订货流程节点失败错误码")+(e?e.errorCode:$t("未知")))})},_delFlowNode:function(o){var t=this,e=t.nodes.concat([]);e.splice(o,1),d.setWorkflowInfo(e).done(function(e){e&&0===e.errorCode?(t.nodes.splice(o,1),t.$el.html(n({list:t.nodes,editable:t.editable}))):l.error($t("删除订货流程节点失败错误码")+(e?e.errorCode:$t("未知")))})},addFlowNode:function(e){$(".flow-picker",this.$el).toggle()},delFlowNode:function(e){e=$(e.target).closest(".j-parent").data("index");e<0||e>=this.nodes.length||this._delFlowNode(e)},createFlowNode:function(e){var o=this,e=$(e.target),t=e.data("type")+"";e.parent().hide(),"1"==t?((e=o.$$components.selector)||(e=o.$$components.selector=new i).on("success",function(e){o._addFlowNode({approverType:1,approverID:e[0],level:0})}),e.open(_.pluck(o.nodes,"approverID"))):o._addFlowNode({approverType:t,approverID:0,level:0})}});t.exports=s});
define("crm-setting/order/process/selector",["crm-modules/common/util","crm-widget/dialog/dialog","crm-widget/selector/selector"],function(e,t,s){var i=e("crm-modules/common/util"),l=e("crm-widget/dialog/dialog"),c=e("crm-widget/selector/selector"),n=l.extend({attrs:{content:'<div><span style="float:left;line-height:34px;">'+$t("选择同事")+'</span><div style="margin-left:60px;" class="select"></div></div>',title:$t("选择同事"),width:500,showBtns:!0,zIndex:2e3},events:{"click .b-g-btn":"submit","click .b-g-btn-cancel":"close"},render:function(){return this.employeeList=i.getAllEmployees(),n.superclass.render.call(this)},open:function(e){this.show(),i.hideErrmsg(this.$(".select")),this.initSelect(e)},initSelect:function(t){var s=this;s.destroySelect(),t=t?_.filter(s.employeeList,function(e){return!_.contains(t,e.id)&&e}):s.employeeList,s.sb=new c({$wrap:$(".select",s.$el),tabs:[{id:"member",title:$t("同事"),type:"sort",searchKeys:["name","nameSpell"],data:FS.contacts.sortEmployeesByLetter(t)}],zIndex:1e4,label:$t("添加同事"),single:!0}),s.sb.on("addItem",function(e){i.hideErrmsg(s.$(".select"))})},submit:function(){var e=this.sb.getValue("member");e&&0!=e.length?(this.trigger("success",e),this.close()):i.showErrmsg(this.$(".select"),$t("请选择同事"))},close:function(){this.destroySelect(),this.hide()},destroySelect:function(){this.sb&&(this.sb.destroy(),this.sb=null)},destroy:function(){this.destroySelect(),this.employeeList=null,n.superclass.destroy.call(this)}});s.exports=n});
define("crm-setting/order/process/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-s-order-process"> <div class="process-tip">' + ((__t = $t("固定审批流")) == null ? "" : __t) + '<a class="process-switch-link" href="#crmmanage/=/module-approval">' + ((__t = $t("查看固定审批流")) == null ? "" : __t) + '</a></div> <div class="section"> <div class="title">' + ((__t = $t("订货流程")) == null ? "" : __t) + '</div> <div class="flow b-g-clear"> <div class="flow-item" data-type="0"> <div class="icon">' + ((__t = $t("客")) == null ? "" : __t) + '</div> <div class="name">' + ((__t = $t("客户下单")) == null ? "" : __t) + '</div> </div> <div class="flow-item flow-item-sep"></div> ';
            _.each(list, function(item, index) {
                __p += ' <div class="flow-item j-parent" data-index=' + ((__t = index) == null ? "" : __t) + "> ";
                if (item.approverType == 3) {
                    __p += ' <div class="icon">' + ((__t = $t("订")) == null ? "" : __t);
                    if (editable) {
                        __p += '<i class="delete j-del-node">×</i>';
                    }
                    __p += '</div> <div class="name">' + ((__t = $t("订单管理员")) == null ? "" : __t) + "</div> ";
                } else if (item.approverType === 2) {
                    __p += ' <div class="icon">' + ((__t = $t("财")) == null ? "" : __t);
                    if (editable) {
                        __p += '<i class="delete j-del-node">×</i>';
                    }
                    __p += '</div> <div class="name">' + ((__t = $t("财务人员")) == null ? "" : __t) + "</div> ";
                } else {
                    __p += ' <div class="avatar"> <img src="' + ((__t = FS.crmUtil.getAvatarLinkById(item.approverID)) == null ? "" : __t) + '"/> ';
                    if (editable) {
                        __p += ' <i class="delete j-del-node">×</i> ';
                    }
                    __p += " </div> ";
                    employee = FS.crmUtil.getEmployeeById(item.approverID);
                    __p += ' <div class="name">' + ((__t = employee && !employee.isStop && employee.name ? employee.name : '<span class="isstop-employee">' + $t("停用员工") + "<span>") == null ? "" : __t) + "</div> ";
                }
                __p += ' </div> <div class="flow-item flow-item-sep"></div> ';
            });
            __p += " ";
            if (editable) {
                __p += ' <div class="flow-item flow-item-plus j-add-node"> <div class="icon">+</div> <div class="name"></div> </div> <div class="flow-item flow-picker" style="display:none;"> <div class="flow-picker-item j-add" data-type="3">' + ((__t = $t("添加订单管理员")) == null ? "" : __t) + '</div> <div class="flow-picker-item j-add" data-type="2">' + ((__t = $t("添加财务人员")) == null ? "" : __t) + '</div> <div class="flow-picker-item j-add" data-type="1">' + ((__t = $t("添加同事")) == null ? "" : __t) + '</div> </div> <!-- <div class="flow-item flow-item-sep"></div> <div class="flow-item"> <div class="icon">' + ((__t = $t("财")) == null ? "" : __t) + '<i class="delete">×</i></div> <div class="name">' + ((__t = $t("财务人员")) == null ? "" : __t) + '</div> </div> --> <div class="flow-item flow-item-sep"></div> ';
            }
            __p += ' <div class="flow-item"> <div class="icon">' + ((__t = $t("发")) == null ? "" : __t) + '</div> <div class="name">' + ((__t = $t("crm.发货人员")) == null ? "" : __t) + '</div> </div> <div class="flow-item flow-item-sep"></div> <div class="flow-item"> <div class="icon">' + ((__t = $t("收")) == null ? "" : __t) + '</div> <div class="name">' + ((__t = $t("收货确认")) == null ? "" : __t) + '</div> </div> </div> </div> <div class="section crm-g-form crm-c-field"> <div class="workflow-wrap"></div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/order/receipt/receipt",["../util","../api","./template/tpl-html","../module","vue-selector-input","base-modules/select/select"],function(e,t,n){var a=e("../util"),l=e("../api"),i=e("./template/tpl-html"),r=e("../module"),d=e("vue-selector-input"),g=e("base-modules/select/select"),s=r.extend({name:"CRM-ORDER-SIGNATURE",events:{"click .j-save1":"setConfigKeyword","click .j-save2":"setConfigSigner","click .j-add-signer":"addSigner","click .j-del":"deleteSigner"},initialize:function(e){s.__super__.initialize.call(this,e),this.objectApiName="SalesOrderObj",this.rowIndex=0,this.pkey="",this.rkey="",this.signer={},this.groups=[],this.$el.html(i({pkey:"",rkey:""})),this.getConfig()},render:function(){},addSigner:function(){this.addRow({},this.rowIndex)},deleteSigner:function(e){var t=this,n=$(e.target).closest(".fm-esign-item").data("row-index"),i=t.signer[n];""===i.id||null==i.id?(e=t.signer[n],delete t.signer[n],e.setting.forEach(function(e){e.destroy&&e.destroy()}),t.$el.find(".row-index-"+n).remove(),t.cleanGroups()):a.alert({msg:$t("删除操作无法撤销，是否确定删除本条记录？"),btnText:$t("确认删除"),noIcon:!0,callback:function(){a.showLoading(!0,$t("删除中...")),l.deleteSign({id:i.id}).done(function(e){e.Error?a.error(e.Error.Message||$t("未知错误请联系系统管理员")):(a.showLoading(!1,$t("删除中...")),e=t.signer[n],delete t.signer[n],e.setting.forEach(function(e){e.destroy&&e.destroy()}),t.$el.find(".row-index-"+n).remove(),t.cleanGroups())})}})},addRow:function(e,t,n){e=e||{};var i=this,r=i.$el.find(".clone-row").clone().addClass("row-index-"+t).data("row-index",t).removeClass("clone-row"),n=(n&&r.addClass("first-row"),FS.contacts.getAllCircles().map(function(e){return e.id}).filter(function(e){return 999999!==e})),n=new d({el:$(".fm-statement-select-depart",r),single:!0,group:{company:!0},defaultSelectedItems:{group:[999999]},excludeItems:{group:n},label:$t("选择部门"),zIndex:9}),s=[],o="",s=(e.sealId&&(s=e.sealList.map(function(e){return{name:e.sealName,value:e.sealId}}),o=e.sealId),new g({wrapper:$(".fm-statement-select-sign",r),options:s,defaultValue:o})),a=new d({el:$(".fm-statement-select-staff",r),single:!0,member:!0,label:$t("选择人员"),zIndex:9});e.userId&&a.setValue({member:[e.userId]}),a.on("change",function(e){0===e.member.length?(i.signer[t].setting[2].resetOptions([]),i.signer[t].setting[2].clean()):l.getSignFromSigner({userId:e.member[0]}).done(function(e){e.Value&&e.Value.signAccountList&&(e=e.Value.signAccountList.map(function(e){return{name:e.sealName,value:e.sealId}}),i.signer[t].setting[2].resetOptions(e),e.length?(e=(_.find(e,function(e){return e.value===o})||e[0]).value,i.signer[t].setting[2].setValue(e)):i.signer[t].setting[2].clean())})}),i.$el.find(".signer-data-row").append(r),i.signer[t]={id:e.id||"",setting:[n,a,s]},i.rowIndex++},getConfig:function(){var n=this;n.showLoading(),l.getSignConfig({objectApiName:n.objectApiName}).done(function(e){var t;n.hideLoading(),e.Value&&(e.Value.signKeyword&&(t=e.Value.signKeyword.split(";"),n.pkey=t[0]||"",n.rkey=t[1]||"",n.$el.html(i({pkey:n.pkey,rkey:n.rkey}))),e.Value.sealConfigList)&&e.Value.sealConfigList.length?e.Value.sealConfigList.forEach(function(e,t){n.addRow(e,t,0===t)}):n.addRow({},0,!0)})},setConfigKeyword:function(){var e=$(".j-keyword-poster",this.$el),t=$(".j-keyword-receiver",this.$el);a.loading($(".j-save1"),!0),l.setSignConfigKeyword({objectApiName:this.objectApiName,signKeyword:[e.val().trim(),t.val().trim()].join(";")}).done(function(e){a.loading($(".j-save1"),!1),e.Error?a.error(e.Error.Message||$t("未知错误请联系系统管理员")):a.remind($t("保存成功"))})},setConfigSigner:function(){var r=this,s=[],o=!1;Object.keys(r.signer).forEach(function(e){var t=r.signer[e].setting,e=r.signer[e].id,n=t[0].getValue(),i=t[1].getValue(),t=t[2].getValue(),n={deptId:n.group[0],userId:i.member[0],sealId:t,objectApiName:r.objectApiName};""!==e&&null!=e&&(n.id=e),s.push(n),n.deptId&&n.userId&&n.sealId||(o=!0)}),o?a.error($t("请填写完整的签署人设置")):(a.loading($(".j-save2"),!0),l.setSignConfigSigner({sealConfigList:s}).done(function(e){a.loading($(".j-save2"),!1),e.Error?a.error(e.Error.Message||$t("未知错误请联系系统管理员")):a.remind($t("保存成功"))}))},showLoading:function(){this.$el.find(".receipt-loading").show()},hideLoading:function(){this.$el.find(".receipt-loading").hide()}});n.exports=s});
define("crm-setting/order/receipt/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-intro" style="display:none;"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>" + ((__t = $t("crm.请设置客服电话")) == null ? "" : __t) + '</li> </ul> </div> <div class="crm-g-form crm-s-order-service crm-s-order-sign"> <div class="receipt-loading"> <div class="loading-block"> </div> </div> <div class="statement-block"> <div class="statement-wrapper"> <div class="fm-item fm-offset"> <div class="fm-tip"><span style="color: #333;">' + ((__t = $t("设置关键词")) == null ? "" : __t) + '</span></div> </div> <div class="fm-item fm-offset"> <div class="fm-tip">' + ((__t = $t("电子签章程序通过关键词位置来确定厂家和经销商的签署位置，请根据销售订单打印模板上的字眼进行设置，比如厂商签名、经销商签收等，只能输入一个关键词。")) == null ? "" : __t) + '</div> </div> <div class="fm-item fm-offset"> <label class="fm-lb">' + ((__t = $t("厂商签署关键词")) == null ? "" : __t) + ':</label> <div> <input type="text" :placeholder="$t(\'crm.setting.order.our_signature\')" class="b-g-ipt fm-ipt j-keyword-poster" value="' + ((__t = pkey) == null ? "" : __t) + '"/> </div> </div> <div class="fm-item fm-offset"> <label class="fm-lb">' + ((__t = $t("经销商签署关键词")) == null ? "" : __t) + ':</label> <div> <input type="text" :placeholder="$t(\'crm.setting.order.dealer_signature\')" class="b-g-ipt fm-ipt j-keyword-receiver" value="' + ((__t = rkey) == null ? "" : __t) + '"/> </div> </div> </div> <div class="fm-offset statement-save"> <span class="b-g-btn j-save1" data-loading-text="' + ((__t = $t("保存中")) == null ? "" : __t) + '">' + ((__t = $t("保存")) == null ? "" : __t) + '</span> </div> </div> <div class="statement-block"> <div class="statement-wrapper"> <div class="fm-item fm-offset"> <div class="fm-tip"><span style="color: #333;">' + ((__t = $t("签署人设置")) == null ? "" : __t) + '</span></div> </div> <div class="fm-item fm-offset"> <div class="fm-tip"> <p>' + ((__t = $t("发货单需要厂商和经销商双方签字确认，这里请设置厂商签署人，而经销商签署人默认为经销商老板（互联对接企业主负责人，不需要设置）。")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("全公司的所有发货单可指定由一人签署，暂不支持多人签署。")) == null ? "" : __t) + '</p> </div> </div> <div class="fm-item signer-data-row fm-offset"> <div class="fm-esign-item clone-row"> <div class="fm-statement-select-depart fm-esign-sub-item"></div> <div class="fm-statement-select-staff fm-esign-sub-item"></div> <div class="fm-statement-select-sign fm-esign-sub-item"></div> <span class="fs-statement-row-delete j-del"><i class="crm-ico-del"></i></span> </div> </div> </div> <div class="fm-offset statement-save"> <span class="b-g-btn j-save2" data-loading-text="' + ((__t = $t("保存中")) == null ? "" : __t) + '">' + ((__t = $t("保存")) == null ? "" : __t) + "</span> </div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/order/richtext/richtext",["crm-modules/action/field/field"],function(t,e,i){var o=t("crm-modules/action/field/field").components.Base,r=FS.crmUtil;return o.extend({initialize:function(){this.ueId="crm_layer_editor"+r.getUUId(4),o.prototype.initialize.apply(this,arguments),this.$el.html('<div id="'+this.ueId+'" class="order-agreement-richtext"></div>')},render:function(){var o=this;o.get("apiname");t.async(["app-common-modules/xss/index","crm-modules/components/ueditor/ueditor","base-ajaxform"],function(t,e){o.XSS=t;var i=o.getData();o.objData=i?t(i):[],o.editor=new e({id:o.ueId,config:{disabledTableInTable:!1,autoClearinitialContent:!0,enableAutoSave:!1,imageScaleScrollIsParent:!0,autoHeightEnabled:!1,zIndex:999,allowDivTransToP:!1,initialFrameHeight:450,toolbars:[["undo","redo","|","fontsize","bold","italic","underline","strikethrough","forecolor","backcolor","|","indent","insertorderedlist","insertunorderedlist","|","justifyleft","justifycenter","justifyright","justifyjustify","formatmatch","|","FXEmoji","link","unlink","|","FXImgUpload","FXInsertVideo"]]},listeners:{ready:function(){_.isEmpty(o.objData)||o.editor.setContent(o.objData),$(".order-agreement-richtext").closest(".f-g-item").css("width","100%")},beforeExecCommand:function(){},afterExecCommand:function(){},firstBeforeExecCommand:function(){},wordcount:function(){o.hideError()}}})}),o.setStatus()},getValue:function(){var t=(this.editor?this.editor.getContent():this.objData)||[];return!t.length&&this.isRequired()&&this.showError(),this.XSS?this.XSS(t):t},setValue:function(t){this.editor&&(t=this.XSS?this.XSS(t):t,this.editor.setContent(t))},destroy:function(){this.editor&&this.editor.destroy(),this.editor=null,this.super.destroy.call(this)}})});
define("crm-setting/order/setting/setting",["../util","../api","./template/tpl-html","../dialog/storesetting","../dialog/servicesetting","../dialog/tyingproduct","../dialog/orderWay","../dialog/orderscope","../dialog/searchfields","../dialog/warehouseSetting","../dialog/picturemode","../dialog/availablerange","../dialog/appclassification","../dialog/orderstock","../module","crm-modules/common/util"],function(e,t,a){var i=e("../util"),o=e("../api"),n=e("./template/tpl-html"),l=e("../dialog/storesetting"),s=e("../dialog/servicesetting"),r=e("../dialog/tyingproduct"),c=e("../dialog/orderWay"),g=e("../dialog/orderscope"),d=e("../dialog/searchfields"),u=e("../dialog/warehouseSetting"),f=e("../dialog/picturemode"),h=e("../dialog/availablerange"),p=e("../dialog/appclassification"),v=e("../dialog/orderstock"),m=e("../module"),D=e("crm-modules/common/util"),y=m.extend({name:"CRM-ORDER-SETTING",events:{"click .j-setting-logo":"_settingLogo","click .j-setting-contact":"_settingContact","click .j-setting-product":"_settingProduct","click .j-setting-scope":"_settingScope","click .j-preview":"_previewSetting","click .j-setting-order":"_settingOrderWay","click .j-search-fileds":"_settingSearchFields","click .j-setting-warehouse":"_settingWarehouse","click .j-picture-model":"_settingPictureMode","click .j-setting-receive":"_settingReceive","click .j-order-stock":"_settingOrderStock","click .j-available-range":"_settingAvailableRange","click .j-app-classification":"_setAppClassification"},initialize:function(e){y.__super__.initialize.call(this,e)},render:function(){this._getData()},_getData:function(){var t=this,a={logoPath:"",serviceHotline:"",tenantName:"",enterpriseName:FS.getAppStore("contactData").companyName};o.getContactAndLogo().done(function(e){_.extend(a,e.data)}).complete(function(){var e;Number(a.serviceHotline)?a.contactWay=[{value:a.serviceHotline,type:1}]:(a.serviceHotline||(a.serviceHotline="[]"),a.contactWay=JSON.parse(a.serviceHotline)),t.$$state.data=a,t.$el.html(n({imageRoot:CRMSETTING.ASSETS_PATH,data:a})),a.logoPath&&"reset"!==a.logoPath&&(e=D.getFscLink(a.logoPath),$(".company-logo",t.$el).attr("src",e)),t.$$state.product=[],a.spuEnable||($(".w-order-way",t.$el).hide(),$(".v-spu-search-fields",t.$el).parent().hide()),o.getTiedProduct().done(function(e){t.$$state.product=e.data||[],t.tyingProductStr(t.$$state.product)}),t.getSailConfig(),t.getStockData(),t.getReceiveInfo()})},getSailConfig:function(){var g=this;o.getSailConfig({keys:["search_fields","return_exchange_warehouse_id","downstream_dht_support_return_exchange","pictureMode","order_range_model","classification_navigation"]}).done(function(e){g.$$state.sailConfig=e.Value||{};var c=g.$$state.sailConfig.configResult;_.map(c||{},function(e,t){var a="";switch(t){case"pictureMode":a="2"===e?$t("无图模式"):$t("有图模式"),$(".v-picture-model",g.$el).html(a);break;case"order_range_model":a={1:$t("标准模式"),2:$t("兼容模式"),3:$t("多组织模式")}[String(e)]||$t("标准模式");$(".v-available-range",g.$el).html(a);break;case"classification_navigation":a="2"===e?$t("一级分类在顶部"):$t("一级分类在左侧"),$(".v-app-classification",g.$el).html(a);break;case"search_fields":if(e)try{e=JSON.parse(e);var i=(c.search_fields=e).spu,o=e.sku,n=e.order,l=_.map(i,function(e){return e.label}),s=_.map(o,function(e){return e.label}),r=_.map(n,function(e){return e.label});$(".v-spu-search-fields",g.$el).html(l.join(", ")),$(".v-sku-search-fields",g.$el).html(s.join(", ")),$(".v-order-search-fields",g.$el).html(r.join(", "))}catch(e){}}})})},getReceiveInfo:function(){o.getReceiveConfig().done(function(e){e=e.Value?e.Value.values:[];2==+(_.find(e,function(e){return"delivery_note_status"==e.key})||{}).value?t():$(".j-auto-receive").hide()});var t=function(){o.getReceiveInfo().done(function(e){e=e.Value||{};2==+e.status?$(".v-auto-receive").html($t("设置发货单确认固定的天数后，系统将自动收货")):$(".v-auto-receive").html($t("发货单确认后")+e.daysAfterShipment+$t("天，系统自动收货"))})}},getStockData:function(){var a=this;o.getStockData().done(function(e){var t,e=e.Value||{};a.$$state.stockViewType=e.stockViewType||1,1==+e.stockSwitch?$(".j-stock-config").hide():(t={1:$t("不显示库存."),3:$t("模糊显示库存."),2:$t("精确显示库存.")},$(".v-order-stock").html(t[e.stockViewType]))})},tyingProductStr:function(e){var a=[],t="",e=(_.each(e,function(e,t){2<t||a.push(e.name)}),t=a.join($t("，")),e.length);3<e&&(t=$t("{{name}}等{{length}}个产品",{name:t,length:e})),$(".v-product-list",this.$el).html(t)},_settingLogo:function(){var a=this;a.storeDialog||(a.storeDialog=new l,a.storeDialog.on("addOrUpdate",function(e){var t;e.logoPath&&(t=CRMSETTING.ASSETS_PATH+"/images/order/logo.png","reset"!==e.logoPath&&(t=D.getFscLink(e.logoPath)),$(".company-logo",a.$el).attr("src",t),a.$$state.data.logoPath=e.logoPath),e.tenantName&&$(".conpany-name",a.$el).html(e.tenantName)&&(a.$$state.data.tenantName=e.tenantName)}),a.storeDialog.on("hide",function(){a.storeDialog.destroy(),a.storeDialog=null})),a.storeDialog.show(a.$$state.data)},_settingContact:function(){var a=this;a.serviceDialog||(a.serviceDialog=new s,a.serviceDialog.on("addOrUpdate",function(e){var t="";a.$$state.data.contactWay=JSON.parse(e.serviceHotline),_.each(a.$$state.data.contactWay,function(e){t=t+'<div class="fm-ct-item"><span class="fm-lb">'+(1==e.type?$t("电话"):2==e.type?"QQ":$t("微信"))+'</span><span class="fm-value">'+e.value+"</span></div>"}),$(".contact-way").html(t)}),a.serviceDialog.on("hide",function(){a.serviceDialog.destroy(),a.serviceDialog=null})),a.serviceDialog.show(a.$$state.data)},_settingProduct:function(){var t=this;t.tyingDialog||(t.tyingDialog=new r,t.tyingDialog.on("addOrUpdate",function(e){t.$$state.product=e||[],t.tyingProductStr(t.$$state.product)}),t.tyingDialog.on("hide",function(){t.tyingDialog.destroy(),t.tyingDialog=null})),t.tyingDialog.show(t.$$state.product)},_settingOrderWay:function(){var a=this;a.orderWayDialog||(a.orderWayDialog=new c,a.orderWayDialog.on("addOrUpdate",function(e){var t=2===e?$t("可同时选择多个规格加入购物车"):$t("只能选择一个规格加入购物车");$(".v-order-way",a.$el).html(t)&&(a.$$state.data.addCartProductSwitch=e)}),a.orderWayDialog.on("hide",function(){a.orderWayDialog.destroy(),a.orderWayDialog=null})),a.orderWayDialog.show(a.$$state.data)},_settingSearchFields:function(){var r=this,e=(r.searchFieldsDialog||(r.searchFieldsDialog=new d,r.searchFieldsDialog.on("addOrUpdate",function(e){if(e){r.$$state.sailConfig.configResult||(r.$$state.sailConfig.configResult={}),e.forEach(function(e){r.$$state.sailConfig.configResult[e.key]=e.value});e=r.$$state.sailConfig.configResult;if(t=e.search_fields)try{var t=JSON.parse(t),a=(e.search_fields=t).spu,i=t.sku,o=t.order,n=_.map(a,function(e){return e.label}),l=_.map(i,function(e){return e.label}),s=_.map(o,function(e){return e.label});$(".v-spu-search-fields",r.$el).html(n.join(", ")),$(".v-sku-search-fields",r.$el).html(l.join(", ")),$(".v-order-search-fields",r.$el).html(s.join(", "))}catch(e){}}}),r.searchFieldsDialog.on("hide",function(){r.searchFieldsDialog.destroy(),r.searchFieldsDialog=null})),r.$$state.sailConfig.configResult.search_fields||{});e.spuEnable=r.$$state.data.spuEnable,r.searchFieldsDialog.show(e)},_settingScope:function(){var a=this;a.orderScopeDialog||(a.orderScopeDialog=new g,a.orderScopeDialog.on("addOrUpdate",function(e){var t="2"==e?$t("crm.所有订单"):$t("仅自己在线提交的订单");$(".v-order-scope",a.$el).html(t)&&(a.$$state.data.orderScope=e)}),a.orderScopeDialog.on("hide",function(){a.orderScopeDialog.destroy(),a.orderScopeDialog=null})),a.orderScopeDialog.show(a.$$state.data)},_previewSetting:function(){var t=this,e=window.location.hostname.split(".")[1],a=(["ceshi112","fxiaoke"].indexOf(e)<0&&(e="ceshi112"),window.open("http://dht."+e+".com/prerelease/index.html#/preview"));setTimeout(function(){var e=(e=t.$$state.data.logoPath)&&"reset"!==e?D.getFscLink(e):null,e=JSON.stringify({firstSet:!0,logoPath:e,tenantName:t.$$state.data.tenantName});a.postMessage(e,"*")},1e3)},_settingWarehouse:function(){var t=this;o.getExchangeReturnStatus().done(function(e){if(!e.Value)return i.remind(3,$t("请先开启退换货单！"));t.warehouseDialog||(t.warehouseDialog=new u,t.warehouseDialog.on("hide",function(){t.warehouseDialog.destroy(),t.warehouseDialog=null,t._getData()})),t.warehouseDialog.show(t.$$state.sailConfig.configResult)})},_settingPictureMode:function(){var a=this,e=(a.pictureModeDialog||(a.pictureModeDialog=new f,a.pictureModeDialog.on("addOrUpdate",function(e){var t="2"==e?$t("无图模式"):$t("有图模式");a.$$state.sailConfig.configResult||(a.$$state.sailConfig.configResult={}),$(".v-picture-model",a.$el).html(t)&&(a.$$state.sailConfig.configResult.pictureMode=e)}),a.pictureModeDialog.on("hide",function(){a.pictureModeDialog.destroy(),a.pictureModeDialog=null})),a.$$state.sailConfig.configResult||{}),e=e.pictureMode||"1";a.pictureModeDialog.show({pictureMode:e})},_settingReceive:function(){sessionStorage.setItem("curTab",1),location.hash="#crmmanage/=/module-shiporder"},_settingOrderStock:function(){var e=this;this.orderStockDialog||(this.orderStockDialog=new v,this.orderStockDialog.on("hide",function(){e.orderStockDialog.destroy(),e.orderStockDialog=null,e._getData()})),this.orderStockDialog.show(e.$$state.stockViewType)},_settingAvailableRange:function(){var a=this,e=(a.rangeDialog||(a.rangeDialog=new h,a.rangeDialog.on("addOrUpdate",function(e){var t={1:$t("标准模式"),2:$t("兼容模式"),3:$t("多组织模式")}[String(e)]||$t("标准模式");a.$$state.sailConfig.configResult||(a.$$state.sailConfig.configResult={}),$(".v-available-range",a.$el).html(t)&&(a.$$state.sailConfig.configResult.order_range_model=e)}),a.rangeDialog.on("hide",function(){a.rangeDialog.destroy(),a.rangeDialog=null})),a.$$state.sailConfig.configResult||{}),e=e.order_range_model||"1";a.rangeDialog.show({value:e,isPriceBookEnable:a.$$state.sailConfig.priceBookEnable,isAvailableRangeEnable:a.$$state.sailConfig.availableRangeEnable})},_setAppClassification:function(){var a=this,i="classification_navigation",o=a.$$state.sailConfig.configResult,e=(a.appClassificationDialog||(a.appClassificationDialog=new p,a.appClassificationDialog.on("addOrUpdate",function(e){var t="2"===e?$t("一级分类在顶部"):$t("一级分类在左侧");o=o||{},$(".v-app-classification",a.$el).html(t)&&(o[i]=e)}),a.appClassificationDialog.on("hide",function(){a.appClassificationDialog.destroy(),a.appClassificationDialog=null})),o[i]||"1");a.appClassificationDialog.show({value:e})}});a.exports=y});
define("crm-setting/order/setting/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-intro" style="display:none;"> <h3>' + ((__t = $t("说明")) == null ? "" : __t) + "：</h3> <ul> <li>" + ((__t = $t("crm.请设置客服电话")) == null ? "" : __t) + '</li> </ul> </div> <div class="crm-g-form crm-s-order-service"> <div class="fm-item fm-line"> <label class="fm-tit">' + ((__t = $t("供货商信息")) == null ? "" : __t) + '</label> <div class="fm-ct"> <div class="fm-ct-item"> <span class="fm-lb">' + ((__t = $t("企业名称")) == null ? "" : __t) + '</span> <span class="fm-value conpany-name">' + ((__t = data.tenantName || data.enterpriseName || "--") == null ? "" : __t) + '</span> </div> <div class="fm-ct-item"> <span class="fm-lb">' + ((__t = $t("企业Logo")) == null ? "" : __t) + '</span> <span class="fm-value img-logo"><img class="company-logo" src="' + ((__t = imageRoot) == null ? "" : __t) + '/images/order/logo.png"/></span> </div> </div> <span class="crm-btn btn-setting j-preview" style="right: 85px;" data-type="logo">' + ((__t = $t("预览")) == null ? "" : __t) + '</span> <span class="b-g-btn btn-setting j-setting-logo" data-type="logo">' + ((__t = $t("设置")) == null ? "" : __t) + '</span> </div> <div class="fm-item fm-line"> <label class="fm-lb">' + ((__t = $t("可售范围及价格控制")) == null ? "" : __t) + '</label> <div class="fm-offset"> <div class="fm-tip v-available-range">' + ((__t = $t("标准模式")) == null ? "" : __t) + '</div> </div> <span class="b-g-btn btn-setting j-available-range" data-type="picture">' + ((__t = $t("设置")) == null ? "" : __t) + '</span> </div> <!-- <div class="fm-item fm-line"> <label class="fm-tit">' + ((__t = $t("客服设置")) == null ? "" : __t) + '</label> <div class="fm-ct contact-way"> ';
            data.contactWay.forEach(function(item) {
                __p += ' <div class="fm-ct-item"> <span class="fm-lb">' + ((__t = item.type == 1 ? $t("电话") : item.type == 2 ? "QQ" : $t("微信")) == null ? "" : __t) + '</span> <span class="fm-value">' + ((__t = item.value) == null ? "" : __t) + "</span> </div> ";
            });
            __p += ' </div> <span class="b-g-btn btn-setting j-setting-contact" data-type="contact">' + ((__t = $t("设置")) == null ? "" : __t) + '</span> </div> --> <div class="fm-item fm-line"> <label class="fm-tit">' + ((__t = $t("搭售产品")) == null ? "" : __t) + '<a href="//www.fxiaoke.com/mob/guide/dht/7.2%E7%B3%BB%E7%BB%9F%E9%85%8D%E7%BD%AE.html#6-%E5%85%B6%E4%BB%96%E9%85%8D%E7%BD%AE" style="vertical-align: -3px;" class="crm-doclink" target="_blank" title=\'' + ((__t = $t("crm.设置为搭售产品后")) == null ? "" : __t) + '\'></a></label> <div class="fm-ct"> <div class="fm-ct-item"> <span class="fm-lb">' + ((__t = $t("crm.产品")) == null ? "" : __t) + '</span> <span class="fm-value v-product-list"></span> </div> </div> <span class="b-g-btn btn-setting j-setting-product" data-type="product">' + ((__t = $t("设置")) == null ? "" : __t) + '</span> </div> <div class="fm-item fm-line"> <label class="fm-lb">' + ((__t = $t("App分类导航")) == null ? "" : __t) + '</label> <div class="fm-offset"> <div class="fm-tip v-app-classification">' + ((__t = $t("一级分类在左侧")) == null ? "" : __t) + '</div> </div> <span class="b-g-btn btn-setting j-app-classification">' + ((__t = $t("设置")) == null ? "" : __t) + '</span> </div> <div class="fm-item fm-line w-order-way"> <label class="fm-lb">' + ((__t = $t("订购方式")) == null ? "" : __t) + '</label> <div class="fm-offset"> <div class="fm-tip v-order-way">' + ((__t = data.addCartProductSwitch == 2 ? $t("可同时选择多个规格加入购物车") : $t("只能选择一个规格加入购物车")) == null ? "" : __t) + '</div> </div> <span class="b-g-btn btn-setting j-setting-order" data-type="order">' + ((__t = $t("设置")) == null ? "" : __t) + '</span> </div> <div class="fm-item fm-line"> <label class="fm-lb">' + ((__t = $t("图片模式")) == null ? "" : __t) + '</label> <div class="fm-offset"> <div class="fm-tip v-picture-model">' + ((__t = $t("有图模式")) == null ? "" : __t) + '</div> </div> <span class="b-g-btn btn-setting j-picture-model" data-type="picture">' + ((__t = $t("设置")) == null ? "" : __t) + '</span> </div> <div class="fm-item fm-line"> <label class="fm-lb">' + ((__t = $t("订单范围")) == null ? "" : __t) + '</label> <div class="fm-offset"> <div class="fm-tip v-order-scope">' + ((__t = data.orderScope == "2" ? $t("crm.所有订单") : $t("仅自己在线提交的订单")) == null ? "" : __t) + '</div> </div> <span class="b-g-btn btn-setting j-setting-scope" data-type="scope">' + ((__t = $t("设置")) == null ? "" : __t) + '</span> </div> <div class="fm-item fm-line j-stock-config"> <label class="fm-lb">' + ((__t = $t("库存显示")) == null ? "" : __t) + '</label> <div class="fm-offset"> <div class="fm-tip v-order-stock">' + ((__t = $t("不显示库存")) == null ? "" : __t) + '</div> </div> <span class="b-g-btn btn-setting j-order-stock" data-type="scope">' + ((__t = $t("设置")) == null ? "" : __t) + '</span> </div> <div class="fm-item fm-line"> <label class="fm-lb">' + ((__t = $t("退换货")) == null ? "" : __t) + '</label> <div class="fm-offset"> <div class="fm-tip v-warehouse-fields">' + ((__t = $t("启用退换货管理，设置下游通过订货通提交退换货申请时的默认仓库")) == null ? "" : __t) + '</div> </div> <span class="b-g-btn btn-setting j-setting-warehouse" data-type="scope">' + ((__t = $t("设置")) == null ? "" : __t) + '</span> </div> <div class="fm-item fm-line j-auto-receive"> <label class="fm-lb">' + ((__t = $t("自动收货")) == null ? "" : __t) + '</label> <div class="fm-offset"> <div class="fm-tip v-auto-receive">' + ((__t = $t("物流签收后3天，系统自动收货")) == null ? "" : __t) + '</div> </div> <span class="b-g-btn btn-setting j-setting-receive" data-type="receive">' + ((__t = $t("设置")) == null ? "" : __t) + '</span> </div> <div class="fm-item fm-line"> <label class="fm-tit">' + ((__t = $t("搜索字段")) == null ? "" : __t) + '<a href="//www.fxiaoke.com/mob/guide/dht/7.2%E7%B3%BB%E7%BB%9F%E9%85%8D%E7%BD%AE.html#6-%E5%85%B6%E4%BB%96%E9%85%8D%E7%BD%AE" style="vertical-align: -3px;" class="crm-doclink" target="_blank" title=\'' + ((__t = $t("crm.设置为搭售产品后")) == null ? "" : __t) + '\'></a></label> <div class="fm-ct"> <div class="fm-ct-item clearfix"> <span class="fm-lb">' + ((__t = $t("crm.商品列表")) == null ? "" : __t) + '</span> <span class="fm-value v-spu-search-fields">' + ((__t = $t("商品名称")) == null ? "" : __t) + '</span> </div> <div class="fm-ct-item clearfix"> <span class="fm-lb">' + ((__t = $t("crm.产品列表")) == null ? "" : __t) + '</span> <span class="fm-value v-sku-search-fields">' + ((__t = $t("产品名称")) == null ? "" : __t) + '</span> </div> <div class="fm-ct-item clearfix"> <span class="fm-lb">' + ((__t = $t("crm.订单列表")) == null ? "" : __t) + '</span> <span class="fm-value v-order-search-fields">' + ((__t = $t("订单编号")) == null ? "" : __t) + '</span> </div> </div> <span class="b-g-btn btn-setting j-search-fileds" data-type="search-fields">' + ((__t = $t("设置")) == null ? "" : __t) + '</span> </div> <div class="fm-item fm-line"> <label class="fm-lb">' + ((__t = $t("小程序")) == null ? "" : __t) + '</label> <div class="fm-img fm-offset"> <img class="xcx_preview" src="' + ((__t = imageRoot) == null ? "" : __t) + '/images/order/xcx_200x200.png"/> <a class="xcx_download" href="' + ((__t = imageRoot) == null ? "" : __t) + '/images/order/xcx.jpg" target="_blank">' + ((__t = $t("下载")) == null ? "" : __t) + '</a> </div> <div class="fm-offset"> <div class="fm-tip">' + ((__t = $t("在微信服务号中绑定该小程序方便下游订货。")) == null ? "" : __t) + '</div> </div> </div> <!--<div class="fm-offset"> <span class="b-g-btn j-save" data-loading-text="保存中">保 存</span> </div>--> </div>';
        }
        return __p;
    };
});
define("crm-setting/order/statement/statement",["../util","../api","./template/tpl-html","../module","vue-selector-input","base-modules/select/select"],function(e,n,t){var l=e("../util"),d=e("../api"),i=e("./template/tpl-html"),r=e("../module"),g=e("vue-selector-input"),u=e("base-modules/select/select"),s=r.extend({name:"CRM-ORDER-SIGNATURE",events:{"click .j-save1":"setConfigKeyword","click .j-save2":"setConfigSigner","click .j-add-signer":"addSigner","click .j-del":"deleteSigner"},initialize:function(e){s.__super__.initialize.call(this,e),this.objectApiName="object_22473__c",this.rowIndex=0,this.pkey="",this.rkey="",this.signer={},this.groups=[],this.$el.html(i({pkey:"",rkey:""})),this.getConfig()},render:function(){},addSigner:function(){this.addRow({},this.rowIndex)},deleteSigner:function(e){var n=this,t=$(e.target).closest(".fm-esign-item").data("row-index"),i=n.signer[t];""===i.id||null==i.id?(e=n.signer[t],delete n.signer[t],e.setting.forEach(function(e){e.destroy&&e.destroy()}),n.$el.find(".row-index-"+t).remove(),n.cleanGroups()):l.alert({msg:$t("删除操作无法撤销，是否确定删除本条记录？"),btnText:$t("确认删除"),noIcon:!0,callback:function(){l.showLoading(!0,$t("删除中...")),d.deleteSign({id:i.id}).done(function(e){e.Error?l.error(e.Error.Message||$t("未知错误请联系系统管理员")):(l.showLoading(!1,$t("删除中...")),e=n.signer[t],delete n.signer[t],e.setting.forEach(function(e){e.destroy&&e.destroy()}),n.$el.find(".row-index-"+t).remove(),n.cleanGroups())})}})},addRow:function(e,i,n){e=e||{};var r=this,t=r.$el.find(".clone-row").clone().addClass("row-index-"+i).data("row-index",i).removeClass("clone-row"),s=(n&&t.addClass("first-row"),new g({el:$(".fm-statement-select-depart",t),single:!0,group:{company:!0},label:$t("选择部门"),zIndex:9})),n=(e.deptId&&s.setValue({group:[e.deptId]}),s.on("change",function(e,n){if(e.group[0]){if(-1!==r.groups.indexOf(e.group[0]))return l.error($t("同一个部门只能存在一个签章人")),void s.setValue({group:[]});r.groups.push(e.group[0])}r.cleanGroups()}),[]),o="",n=(e.sealId&&(n=e.sealList.map(function(e){return{name:e.sealName,value:e.sealId}}),o=e.sealId),new u({wrapper:$(".fm-statement-select-sign",t),options:n,defaultValue:o})),a=new g({el:$(".fm-statement-select-staff",t),single:!0,member:!0,label:$t("选择人员"),zIndex:9});e.userId&&a.setValue({member:[e.userId]}),a.on("change",function(e){0===e.member.length?(r.signer[i].setting[2].resetOptions([]),r.signer[i].setting[2].clean()):d.getSignFromSigner({userId:e.member[0]}).done(function(e){var n,t;e.Value&&e.Value.signAccountList&&(e=e.Value.signAccountList.map(function(e){return{name:e.sealName,value:e.sealId}}),n="",r.signer[i].setting[2].resetOptions(e),e.length?((n=_.find(e,function(e){return e.value===o}))?t=n.value:o||(t=e[0].value),r.signer[i].setting[2].setValue(t)):r.signer[i].setting[2].clean())})}),r.$el.find(".signer-data-row").append(t),r.signer[i]={id:e.id||"",setting:[s,a,n]},r.rowIndex++},getConfig:function(){var t=this;t.showLoading(),d.getSignConfig({objectApiName:t.objectApiName}).done(function(e){var n;t.hideLoading(),e.Value&&(e.Value.signKeyword&&(n=e.Value.signKeyword.split(";"),t.pkey=n[0]||"",t.rkey=n[1]||"",t.$el.html(i({pkey:t.pkey,rkey:t.rkey}))),e.Value.sealConfigList)&&e.Value.sealConfigList.length?e.Value.sealConfigList.forEach(function(e,n){t.addRow(e,n,0===n)}):t.addRow({},0,!0)})},cleanGroups:function(){var i=this,r=[];Object.keys(i.signer).forEach(function(e){e=i.signer[e].setting[0].getValue();e.group[0]&&r.push(e.group[0])}),i.groups.forEach(function(n,e){var t=!1;r.forEach(function(e){e===n&&(t=!0)}),t||delete i.groups[e]})},setConfigKeyword:function(){var e=$(".j-keyword-poster",this.$el),n=$(".j-keyword-receiver",this.$el);l.loading($(".j-save1"),!0),d.setSignConfigKeyword({objectApiName:this.objectApiName,signKeyword:[e.val().trim(),n.val().trim()].join(";")}).done(function(e){l.loading($(".j-save1"),!1),e.Error?l.error(e.Error.Message||$t("未知错误请联系系统管理员")):l.remind($t("保存成功"))})},setConfigSigner:function(){var r=this,s=[],o=!1;Object.keys(r.signer).forEach(function(e){var n=r.signer[e].setting,e=r.signer[e].id,t=n[0].getValue(),i=n[1].getValue(),n=n[2].getValue(),t={deptId:t.group[0],userId:i.member[0],sealId:n,objectApiName:r.objectApiName};""!==e&&null!=e&&(t.id=e),s.push(t),t.deptId&&t.userId&&t.sealId||(o=!0)}),o?l.error($t("请填写完整的签署人设置")):(l.loading($(".j-save2"),!0),d.setSignConfigSigner({sealConfigList:s}).done(function(e){var n;l.loading($(".j-save2"),!1),e.Error?l.error(e.Error.Message||$t("未知错误请联系系统管理员")):(n=e.Value.sealConfigList,Object.keys(r.signer).forEach(function(e){var t=r.signer[e],e=_.find(n,function(e){var n=t.setting[0].getValue();return e.deptId==n.group[0]});e&&(t.id=e.id)}),l.remind($t("保存成功")))}))},showLoading:function(){this.$el.find(".statement-loading").show()},hideLoading:function(){this.$el.find(".statement-loading").hide()}});t.exports=s});
define("crm-setting/order/statement/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-intro" style="display:none;"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>" + ((__t = $t("crm.请设置客服电话")) == null ? "" : __t) + '</li> </ul> </div> <div class="crm-g-form crm-s-order-service crm-s-order-sign"> <div class="statement-loading"> <div class="loading-block"> </div> </div> <div class="statement-block"> <div class="statement-wrapper"> <div class="fm-item fm-offset"> <div class="fm-tip"><span style="color: #333;">' + ((__t = $t("设置关键词")) == null ? "" : __t) + '</span></div> </div> <div class="fm-item fm-offset"> <div class="fm-tip">' + ((__t = $t("电子签章程序通过关键词位置来确定厂家和经销商的签署位置，请根据对账单打印模板上的字眼进行设置，比如厂商签名、经销商签收等，只能输入一个关键词。")) == null ? "" : __t) + '</div> </div> <div class="fm-item fm-offset"> <label class="fm-lb">' + ((__t = $t("厂商签署关键词")) == null ? "" : __t) + ':</label> <div> <input type="text" :placeholder="$t(\'crm.setting.order.our_signature\')" class="b-g-ipt fm-ipt j-keyword-poster" value="' + ((__t = pkey) == null ? "" : __t) + '"/> </div> </div> <div class="fm-item fm-offset"> <label class="fm-lb">' + ((__t = $t("经销商签署关键词")) == null ? "" : __t) + ':</label> <div> <input type="text" :placeholder="$t(\'crm.setting.order.dealer_signature\')" class="b-g-ipt fm-ipt j-keyword-receiver" value="' + ((__t = rkey) == null ? "" : __t) + '"/> </div> </div> </div> <div class="fm-offset statement-save"> <span class="b-g-btn j-save1" data-loading-text="' + ((__t = $t("保存中")) == null ? "" : __t) + '">' + ((__t = $t("保存")) == null ? "" : __t) + '</span> </div> </div> <div class="statement-block"> <div class="statement-wrapper"> <div class="fm-item fm-offset"> <div class="fm-tip"><span style="color: #333;">' + ((__t = $t("签署人设置")) == null ? "" : __t) + '</span></div> </div> <div class="fm-item fm-offset"> <div class="fm-tip"> <p>' + ((__t = $t("对账单需要厂商和经销商双方签字确认，这里请设置厂家签署人，而经销商签署人默认为经销商老板（互联对接企业主负责人，不需要设置）。")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("全公司的对账单可指定由一人签署，暂不支持多人签署。")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("当有多个子公司时，可设置不同子公司指定不同人签署。")) == null ? "" : __t) + '</p> </div> </div> <div class="fm-item signer-data-row fm-offset"> <div class="fm-esign-item clone-row"> <div class="fm-statement-select-depart fm-esign-sub-item"></div> <div class="fm-statement-select-staff fm-esign-sub-item"></div> <div class="fm-statement-select-sign fm-esign-sub-item"></div> <span class="fs-statement-row-delete j-del"><i class="crm-ico-del"></i></span> </div> </div> <div class="fm-item fm-offset"> <div class="fm-statement-row-add j-add-signer">+' + ((__t = $t("添加签署组织和签署人")) == null ? "" : __t) + '</div> </div> </div> <div class="fm-offset statement-save"> <span class="b-g-btn j-save2" data-loading-text="' + ((__t = $t("保存中")) == null ? "" : __t) + '">' + ((__t = $t("保存")) == null ? "" : __t) + "</span> </div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/order/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-tit clearfix"> <h2><span class="tit-txt">' + ((__t = $t("订货通管理")) == null ? "" : __t) + '<a class="crm-doclink" href="//www.fxiaoke.com/mob/guide/dht/" target="_blank"></a></span></h2> </div> <div id="cmr-s-order-wrapper" class="crm-module-con"> <div class="crm-tab b-g-clear"> <span data-type="bankaccount" class="cur">' + ((__t = $t("收款账户")) == null ? "" : __t) + '</span> <span data-type="process">' + ((__t = $t("流程设置")) == null ? "" : __t) + '</span> <span data-type="homesetting">' + ((__t = $t("首页设置")) == null ? "" : __t) + '</span> <span data-type="agreement">' + ((__t = $t("订货协议")) == null ? "" : __t) + '</span> <span data-type="statement" class="j-esign" style="display: none">' + ((__t = $t("crm.对账单")) == null ? "" : __t) + '</span> <span data-type="receipt" class="j-esign" style="display: none">' + ((__t = $t("确认收货")) == null ? "" : __t) + '</span> <span data-type="onlineservice">' + ((__t = $t("在线客服")) == null ? "" : __t) + '</span> <span data-type="setting">' + ((__t = $t("其他设置")) == null ? "" : __t) + '</span> </div> <div class="tab-con"> <div class="item item-wrap bankaccount-box"></div> <div class="item item-wrap b-g-hide process-box"></div> <div class="item item-wrap b-g-hide homesetting-box"></div> <div class="item item-wrap b-g-hide agreement-box"></div> <div class="item item-wrap b-g-hide statement-box"></div> <div class="item item-wrap b-g-hide receipt-box"></div> <div class="item item-wrap b-g-hide onlineservice-box"></div> <div class="item item-wrap b-g-hide setting-box"></div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/order/util",["base-modules/dialog/dialog"],function(e,t,n){var o=e("base-modules/dialog/dialog"),i=CRM.util,e={_template:_.template('<div class="f-g-confirm-wrapper crm-s-order-dialog fn-clear"><div class="confirm-message"><p class="confirm-message-f-line">{{ tip }}</p><p class="confirm-message-s-line">{{ msg }}</p><i class="icon {{ icon }}"></i></div><div class="confirm-actions">## _.each(_btns, function(item) { ##<span class="{{ item.klass }}" action-type="action" data-value="{{ item.action }}">{{ item.text }}</span>## }) ##</div></div>'),_dialog:function(t){(t=t||{}).tip=t.tip||"",t.msg=t.msg||"",t.icon=t.icon||"",t._btns=t.btns||[],delete t.btns;var n,e=_.extend({title:$t("提示"),width:500,zIndex:1e4},t,{content:this._template(t)});return(n=new o(e)).on("hide",function(e){n.destroy()}),n.on("action",function(e){e=$(e.target).data("value");!1!==(t.callback||FS.EMPTY_FN)(e)&&n.hide()}),n.show(),!1!==t.stopPropagation&&n.element.on("click",function(){return!1}),n},dialog:function(e){return(e=e||{}).icon="icon-succ",this._dialog(e)},alert:function(e){return(e=e||{}).noIcon||(e.icon="icon-succ"),e.btns=[{klass:"confirm",action:"confirm",text:e.btnText}],this._dialog(e)},error2:function(e){return(e=e||{}).icon="icon-fail",this._dialog(e)},showLoading:function(e,t){t=t||$t("正在处理中")+"...",e?i.showGlobalLoading(1,null,t):i.hideGlobalLoading(1)},loading:function(e,t){var n,e=$(e),o="state-requesting";return e.hasClass(o)===t||(t?(t=e.html(),n=(n=e.data("loadingText"))||t,e.addClass(o).width(e.width()+35).html('<span class="icon-requesting" style="padding:0;"><img src="'+FS.BLANK_IMG+'" alt="loading" />&nbsp;&nbsp;</span>'+n).data("originText",t)):e.removeClass(o).removeAttr("style").html(e.data("originText")),!1)},dealCoustomerDesc:function(e){return e&&(e.customerType&&!_.findWhere(e.customerType,{ItemCode:"-9999"})&&e.customerType.unshift({ItemCode:"-9999",ItemName:$t("全部")}),e.customerLevel)&&!_.findWhere(e.customerLevel,{ItemCode:"-9999"})&&e.customerLevel.unshift({ItemCode:"-9999",ItemName:$t("全部")}),e},covertResponse:function(e){return _.extend(e,{errorCode:0,errorMessage:null}),e&&e.Result&&e.Result.FailureCode?_.extend(e,{errorCode:String(e.Result.FailureCode),errorMessage:e.Result.FailureMessage}):e.Error&&e.Error.Code&&_.extend(e,{errorCode:a[e.Error.Code],errorMessage:e.Error.Message}),e.data=e.Value,e},getExtendDomainFscLink:function(e,t,n,o){n=encodeURIComponent(n);var i="//"+location.hostname;return o?i+"/FSC/EM/File/DownloadByPath?path="+t+"&name="+n+"&traceId="+FS.util.getTraceId("FSC"):i+"/FSC/EM/File/GetByPath?path="+t}},a={320020001:-1,320020002:-2,320020003:-3,320020004:-4,320020005:-100,320020006:-101,320020007:-102,320020008:-103,320020200:-104,320020500:-504,320020501:-505,320020502:-506,320020503:-510,320020504:-511,320020505:-512,320020506:-513,320020507:-514,320020508:-515,320020509:-516,320020510:-517,320020511:-518,320020750:-801,320020218:-901,320020400:-1e5};n.exports=_.extend(i,e)});