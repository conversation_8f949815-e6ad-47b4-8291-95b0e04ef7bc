define("crm-setting/promotionrebate/common/config",[],function(e,i,t){t.exports={CONFIG_DATA:[{domId:"level0",moduleId:"advancedPricing",title:$t("高级定价",null,"价格政策"),licenseKey:"advanced_pricing_app",moduleList:[{type:"switch",title:$t("开启高级定价",null,"开启价格政策"),key:"price_policy",dependKeys:["promotion_status","28"],value:!1,enableClose:!1,setConfigParam:2,describeList:[{title:$t("高级定价开关",null,"价格政策开关")}],children:[{_id:"price_policy_objects",key:"price_policy_objects",title:"",type:"PricePolicyObject",dependKeys:["price_policy"],isShow:function(e){return e.price_policy}}]},{title:$t("sfa.crm.price_policy_config.multiple_object"),type:"switch",key:"multiple_object_price_policy",isShow:function(e){return e.price_policy&&CRM.util.isGrayScale("CRM_POLICY_MULTIPLE_OBJECT")},value:!1,enableClose:!1,setConfigParam:2,describeList:[{title:$t("sfa.crm.price_policy_config.multiple_object_desc")}]},{title:$t("允许切换政策："),type:"checkbox",key:["allow_switch_master_price_policy","allow_switch_detail_price_policy"],isShow:function(e){return e.price_policy},value:[!1,!1],options:[{key:"allow_switch_master_price_policy",label:$t("整单促:主对象上匹配的价格政策允许手工切换或取消")},{key:"allow_switch_detail_price_policy",label:$t("产品促:从对象上匹配的价格政策允许手工切换或取消")}],describeList:[{title:$t("注意:如果有限额限量控制，禁止切换可能导致无法下单")}]},{title:$t("crm.price_policy_config.percentile_gift"),key:"price_policy_support_percentile_gift",isShow:function(e){return e.price_policy},type:"switch",value:!1,enableClose:!1,describeList:[{title:$t("crm.price_policy_config.percentile_gift_no")},{title:$t("crm.price_policy_config.percentile_gift_yes")}]},{title:$t("sfa.crm.promotionrebate.price_policy_unit_title"),key:"price_policy_unit",type:"PricePolicyUnit",isShow:function(e){return e.price_policy&&e.multiple_unit}},{title:$t("赠品费用分摊依据："),key:"gift_amortize_basis",isShow:function(e){return e.price_policy},type:"radio",value:"price_book_price",radioOptions:[{label:$t("价目表价格"),value:"price_book_price"},{label:$t("产品档案价格"),value:"product_price"}]},{title:$t("赠品参与分摊："),type:"radio",key:"gift_attend_amortize",isShow:function(e){return e.price_policy},value:"0",radioOptions:[{value:"0",label:$t("否:赠品的价值全部分摊到本品行，赠品最终【销售单价】为0、【费用分摊后小计】即为赠品自己的价值")},{value:"1",label:$t("是:赠品的价值分摊到本品行和赠品行，赠品最终【销售单价】为0、【费用分摊后小计】为赠品自己的价值扣除分摊值的部分")}]},{title:$t("crm.price_policy_config.gift_range_title"),type:"radio",key:"enable_gift_range_shelves",isShow:function(e){return e.price_policy},value:"0",radioOptions:[{value:"0",label:$t("crm.price_policy_config.gift_range_no")},{value:"1",label:$t("crm.price_policy_config.gift_range_yes")}]},{title:$t("crm.price_policy_config.stacked_discount_title"),type:"switch",enableClose:!1,key:"discount_on_discount",isShow:function(e){return e.price_policy&&CRM.util.isGrayScale("CRM_POLICY_STACKED_DISCOUNT")},value:!1,setConfigParam:2,describeList:[{title:$t("crm.price_policy_config.stacked_discount_no")},{title:$t("crm.price_policy_config.stacked_discount_yes")}]},{title:$t("crm.price_policy_config.match_mode"),key:"match_mode",isShow:function(e){return e.price_policy},type:"radio",value:"immediately",radioOptions:[{label:$t("crm.price_policy_config.match_immediately"),value:"immediately"},{label:$t("crm.price_policy_config.match_once"),value:"once"}]},{type:"checkbox",title:$t("选产品页，各产品显示的内容"),key:["show_price_policy_name"],isShow:function(e){return e.price_policy},value:[!1],options:[{label:$t("移动端显示政策名称"),key:"show_price_policy_name"}],describeList:[{title:$t("移动端选产品页，各产品将展示“全部政策名称”，建议创建价格政策时，只创建一条价格规则（或者多条规则的产品范围完全相同），将政策名称置为具体的促销信息，（如：满200减20）起到在选产品时可以查看产品的具体促销信息的作用。")}]},{title:$t("crm.price_policy_config.dep"),key:"price_policy_dept_not_this",isShow:function(e){return e.price_policy},type:"radio",value:"0",radioOptions:[{label:$t("crm.price_policy_config.dep_self"),value:"0"},{label:$t("crm.price_policy_config.dep_all"),value:"1"}]}]},{domId:"level1",moduleId:"rebate",title:$t("返利单"),licenseKey:"rebate_app",moduleList:[{title:$t("开启返利单"),type:"switch",key:"rebate",value:!1,enableClose:!1,setConfigParam:2,describeList:[{title:$t("返利单政策开关，开启后不可关闭。")}]},{title:$t("crm.setting.promotionrebate.rebate_policy_source_title",null,"返利产生来源"),key:"rebate_policy_source",isShow:function(e){return e.rebate},type:"RebatePolicySource"},{title:$t("sfa.crm.setting.promotionrebate.rebate_product_range_shelves",null,"返利品校验可售范围"),key:"rebate_product_range_shelves",isShow:function(e){return e.rebate},type:"radio",value:"0",radioOptions:[{label:$t("sfa.crm.rebate_config.product_range_no"),value:"0"},{label:$t("sfa.crm.rebate_config.product_range_yes"),value:"1"}]}]},{domId:"level2",moduleId:"coupon",title:$t("优惠券"),licenseKey:"coupon_app",moduleList:[{title:$t("开启优惠券"),type:"switch",key:"coupon",value:!1,enableClose:!1,setConfigParam:2,describeList:[{title:$t("优惠券政策开关，开启后不可关闭。")}]},{title:$t("启用纸质券业务"),key:"paper_coupon",value:!1,enableClose:!1,setConfigParam:2,isShow:CRM.util.isGrayScale("CRM_PAPER_COUPON")}]}],KEY_CONFIG:{price_policy:{cache_key:"advancedPricing"},multiple_object_price_policy:{type:"boolean"},28:{type:"string"},promotion_status:{type:"string"},clone_history_order_product:{type:"string"},gift_amortize_basis:{cache_key:"giftAmortizeBasis",type:"string"},gift_attend_amortize:{cache_key:"giftAttendAmortize",type:"string"},enable_gift_range_shelves:{type:"string"},show_price_policy_name:{cache_key:"showPricePolicyName"},allow_switch_master_price_policy:{cache_key:"allowSwitchMasterPricePolicy"},allow_switch_detail_price_policy:{cache_key:"allowSwitchDetailPricePolicy"},price_policy_support_percentile_gift:{type:"boolean"},match_mode:{type:"string"},price_policy_dept_not_this:{type:"string"},discount_on_discount:{type:"boolean"},gift_fixed_attribute:{type:"string"},coupon:{cache_key:"openCoupon"},paper_coupon:{cache_key:"openPaperCoupon"},rebate:{cache_key:"openRebate"},rebate_policy_source:{type:"string"},price_policy_unit:{type:"string"},rebate_product_range_shelves:{type:"string"}}}});
function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _toConsumableArray(e){return _arrayWithoutHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray(e)||_nonIterableSpread()}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){var n;if(e)return"string"==typeof e?_arrayLikeToArray(e,t):"Map"===(n="Object"===(n={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:n)||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_arrayLikeToArray(e,t):void 0}function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray(e)}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function asyncGeneratorStep(e,t,n,r,o,i,a){try{var u=e[i](a),c=u.value}catch(e){return void n(e)}u.done?t(c):Promise.resolve(c).then(r,o)}function _asyncToGenerator(u){return function(){var e=this,a=arguments;return new Promise(function(t,n){var r=u.apply(e,a);function o(e){asyncGeneratorStep(r,t,n,o,i,"next",e)}function i(e){asyncGeneratorStep(r,t,n,o,i,"throw",e)}o(void 0)})}}function ownKeys(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"==_typeof(e)?e:e+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);n=n.call(e,t||"default");if("object"!=_typeof(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}define("crm-setting/promotionrebate/components/Base",[],function(e,t,n){n.exports=Backbone.View.extend({initialize:function(e){e=e.wrapper;this.setElement(e),this.wContent=null,this.pricePolicyObjects=[]},render:function(){this.initMainContentView()},initMainContentView:function(){var t=this;e.async("vcrm/sdk",function(e){e.getComponent("backstage").then(function(e){t.wContent=FxUI.create(t.getMainContentInitOptions(e.default))})})},getMainContentInitOptions:function(e){var l=this,c=l.getConfigKeyData(),t=l.getConfigData();return{wrapper:this.$el[0],template:'\n                    <backstage\n                        ref="rBackstage"\n                        :data="cConfigData"\n                        :changeLoading="changeLoading"\n                        @change="handleChange"\n                        @update="(keys) => getConfig(keys, true)"\n                    />\n                ',components:{Backstage:e},data:function(){return{configData:t,licenses:{},keyValues:{},oriKeyValues:{},changeLoading:!1}},computed:{cConfigData:function(){var s=this;return this.configData.map(function(e){function t(){return(0<arguments.length&&void 0!==arguments[0]?arguments[0]:[]).map(function(e){return e.moduleList&&e.moduleList.length?_objectSpread(_objectSpread({},e),{},{moduleList:t(e.moduleList)}):c(e)}).filter(function(e){return e.moduleList||e.isShow})}var c=function(e){e.key;var t=e.value,n=e.isShow,r=e.radioOptions,r=void 0===r?[]:r,o=e.options,o=void 0===o?[]:o,i=e.depKeys,i=void 0===i?[]:i,a=Array.isArray(e.key)?e.key.map(function(e){return s.keyValues[e]}):s.keyValues[e.key],a=Object.keys(s.keyValues).length?a:t,t=i.reduce(function(e,t){return e[t]=s.keyValues[t],e},{}),i=void 0===n||(_.isFunction(n)?e.isShow(s.keyValues,s.licenses):n),n=r.map(function(e){var t=e.disabled,t=void 0!==t&&(_.isFunction(t)?t(s.keyValues):t);return _objectSpread(_objectSpread({},e),{},{disabled:t})}),r=_.isFunction(o)?o(s.keyValues):o,o=(e.children||[]).map(function(e){return c(e)}).filter(function(e){return e.isShow}),u=l.getExtraStatus&&l.getExtraStatus(e.key,s.oriKeyValues);return _objectSpread(_objectSpread({},e),{},{isShow:i,value:a,depValues:t,radioOptions:n,options:r,children:o,extraStatus:u})},n=!e.licenseKey||s.licenses[e.licenseKey];return _objectSpread(_objectSpread({},e),{},{visible:n,moduleList:t(e.moduleList)})})}},created:function(){this.getLicenses(),this.getConfig()},methods:{getKeyValues:function(){return this.keyValues},transValue:function(e,t,n){var r=(c[e]||{}).type,o=t;return"string"===r?o=t:"jsonstring"===r?o=n?JSON.stringify(t):JSON.parse(t):"boolean"!==r&&r||(o=n?t?"1":"0":"1"===t),l.transValue(e,o,n)},handleChange:function(a){var u=this;return _asyncToGenerator(regeneratorRuntime.mark(function e(){var t,n,r,o,i;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=l.handleModuleItemChange(a),t=n.key,n=n.value,u.oriKeyValues.hasOwnProperty(t),r=u.keyValues[t],l.currentChangeKey=t,u.changeLoading=!0,e.prev=5,i=u.transValue(t,n,!0),e.next=9,l.beforeSetConfig(t,i);case 9:if((o=e.sent).confirmInfo)return e.next=13,l.confirm(o.confirmInfo);e.next=13;break;case 13:return e.next=15,l.setConfig(o,t);case 15:u.keyValues=_objectSpread(_objectSpread({},u.keyValues),{},_defineProperty({},t,n)),u.oriKeyValues=_objectSpread(_objectSpread({},u.oriKeyValues),{},_defineProperty({},t,n)),CRM.util.remind(1,$t("操作成功")),l.afterSetConfig({update:function(e){return u.getConfig.call(u,e,!0)}}),e.next=29;break;case 21:if(e.prev=21,e.t0=e.catch(5),u.keyValues=_objectSpread(_objectSpread({},u.keyValues),{},_defineProperty({},t,r)),"confirmCancel"===e.t0)return e.abrupt("return");e.next=27;break;case 27:i=(null==e.t0?void 0:e.t0.message)||("string"==typeof e.t0?e.t0:$t("操作失败!")),CRM.util.alert(i);case 29:u.changeLoading=!1,l.currentChangeKey=null;case 31:case"end":return e.stop()}},e,null,[[5,21]])}))()},commonSetConfig:function(){var e;return(e=this.$refs.rBackstage).commonSetConfig.apply(e,arguments)},getConfig:function(n,a){var u=this;return _asyncToGenerator(regeneratorRuntime.mark(function e(){var t,o,i;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,l.getConfig(n,a);case 3:t=e.sent,o={},i={},t.forEach(function(e){var t=e.key,e=e.value,n=(c[t]||{}).cache_key,r=u.transValue&&u.transValue(t,e,!1);n&&(CRM._cache[n]=r),a?(u.$set(u.keyValues,t,r),u.$set(u.oriKeyValues,t,e)):(o[t]=r,i[t]=e)}),a||(u.keyValues=o,u.oriKeyValues=i),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(0),CRM.util.remind(3,e.t0||$t("crm.setting.tradeconfigure.init_config_failed",null,"初始化开关失败！"));case 13:case"end":return e.stop()}},e,null,[[0,10]])}))()},getLicenses:function(){var e=this,t=this.configData.map(function(e){return e.licenseKey}).filter(function(e){return e}),n=l.getLicenseKeys();t.push.apply(t,_toConsumableArray(n)),t.length&&CRM.api.get_licenses({key:t,cb:function(){e.licenses=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{}}})}}}},getLicenseKeys:function(){return[]},getConfigData:function(){return[]},getConfigKeyData:function(){return{}},getConfigKeyDataByKey:function(t){var n,r,e=this.getConfigData();return t?(n=[],(r=function(e){e.forEach(function(e){e.moduleList&&e.moduleList.length?r(e.moduleList):(n.push(e),e.children&&e.children.length&&n.push.apply(n,_toConsumableArray(e.children)))})})(e),n.find(function(e){return Array.isArray(e.key)?e.key.includes(t):e.key===t})||{}):e},getKeyValues:function(e){var t=(null==(t=this.wContent)?void 0:t.getKeyValues())||{};return e?t[e]:t},getOKeyValues:function(e){var t=(null==(t=this.wContent)?void 0:t.oriKeyValues)||{};return e?t[e]:t},transValue:function(e,t,n){return t},handleModuleItemChange:function(e){return e},beforeSetConfig:function(u,c){var s=this;return _asyncToGenerator(regeneratorRuntime.mark(function e(){var t,n,r,o,i,a;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(i=s.getConfigKeyDataByKey(u),t=i.title,n=i.enableClose,r=i.enableOpen,o=i.setUrl,i=void 0===(i=i.setConfigParam)?1:i,a=function(){var e=t?'"'.concat(t,'"'):"";return!1===n?$t("crm.setting.tradeconfigure.confirm_open_notclose",{name:e}):!1===r?$t("crm.setting.tradeconfigure.confirm_close_notopen",{name:e}):null}(),1===i)return e.abrupt("return",{url:o||"/EM1HNCRM/API/v1/object/biz_config/service/set_config_value",data:{key:u,value:c},confirmInfo:a});e.next=5;break;case 5:return e.abrupt("return",{url:o||"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",data:{tenantId:CRM.enterpriseId,moduleCode:u,openStatus:c},confirmInfo:a});case 6:case"end":return e.stop()}},e)}))()},setConfig:function(e){return this.commonSetConfigFetch(e).then(function(e){return _.isEmpty(e.Value)||e.Value.success&&0===e.Value.errCode?e:Promise.reject(new Error(e.Value.errMessage))})},afterSetConfig:function(){},getConfig:function(e){return this.getConfigValues(e)},commonSetConfigFetch:function(e){var t=e.url,n=e.data,r=e.otherOpts,r=void 0===r?{}:r,e=e.logData,e=void 0===e?{}:e;return CRM.util.showLoading_tip(),this.wContent.commonSetConfig({url:t,data:n},r,e).finally(function(){CRM.util.hideLoading_tip()})},fetch:function(e){var r=e.url,o=e.data,i=e.otherOpts;return new Promise(function(t,n){CRM.util.showLoading_tip(),CRM.util.FHHApi({url:r,data:o,success:function(e){CRM.util.hideLoading_tip(),0===e.Result.StatusCode?t(e):n(new Error(null==e||null==(e=e.Result)?void 0:e.FailureMessage))},error:function(){CRM.util.hideLoading_tip(),n(new Error($t("网络异常，请稍后重试")))}},_objectSpread({errorAlertModel:1},i))})},confirm:function(r){var o=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};return new Promise(function(e,t){var n=CRM.util.confirm(r,$t("提示"),function(){n.hide(),e()},_objectSpread({cancelBack:function(){n.hide(),t("confirmCancel")}},o))})},getConfigValues:function(e){return Promise.resolve(CRM.util.getConfigValues(e))},destroy:function(){var e;null!=(e=this.wContent)&&e.destroy()}})});
function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ownKeys(r,e){var t,n=Object.keys(r);return Object.getOwnPropertySymbols&&(t=Object.getOwnPropertySymbols(r),e&&(t=t.filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})),n.push.apply(n,t)),n}function _objectSpread(r){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(t),!0).forEach(function(e){_defineProperty(r,e,t[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(t)):ownKeys(Object(t)).forEach(function(e){Object.defineProperty(r,e,Object.getOwnPropertyDescriptor(t,e))})}return r}function _defineProperty(e,r,t){return(r=_toPropertyKey(r))in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"==_typeof(e)?e:e+""}function _toPrimitive(e,r){if("object"!=_typeof(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0===t)return("string"===r?String:Number)(e);t=t.call(e,r||"default");if("object"!=_typeof(t))return t;throw new TypeError("@@toPrimitive must return a primitive value.")}function asyncGeneratorStep(e,r,t,n,o,i,a){try{var c=e[i](a),u=c.value}catch(e){return void t(e)}c.done?r(u):Promise.resolve(u).then(n,o)}function _asyncToGenerator(c){return function(){var e=this,a=arguments;return new Promise(function(r,t){var n=c.apply(e,a);function o(e){asyncGeneratorStep(n,r,t,o,i,"next",e)}function i(e){asyncGeneratorStep(n,r,t,o,i,"throw",e)}o(void 0)})}}function _slicedToArray(e,r){return _arrayWithHoles(e)||_iterableToArrayLimit(e,r)||_unsupportedIterableToArray(e,r)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,r){var t;if(e)return"string"==typeof e?_arrayLikeToArray(e,r):"Map"===(t="Object"===(t={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:t)||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?_arrayLikeToArray(e,r):void 0}function _arrayLikeToArray(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,n=Array(r);t<r;t++)n[t]=e[t];return n}function _iterableToArrayLimit(e,r){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(t=t.call(e)).next,0===r){if(Object(t)!==t)return;u=!1}else for(;!(u=(n=i.call(t)).done)&&(c.push(n.value),c.length!==r);u=!0);}catch(e){l=!0,o=e}finally{try{if(!u&&null!=t.return&&(a=t.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}function _arrayWithHoles(e){if(Array.isArray(e))return e}define("crm-setting/promotionrebate/promotionrebate",["./components/Base","./common/config"],function(e,r,t){var i=e("./components/Base"),e=e("./common/config"),n=e.CONFIG_DATA,o=e.KEY_CONFIG;t.exports=i.extend({getConfigData:function(){return n},getConfigKeyData:function(){return o},getConfig:function(e){return Promise.all([this.getConfigValues(e),this.getRebatePolicySourceConfig(),this.getPolicyObjectConfig()]).then(function(e){var e=_slicedToArray(e,3),r=e[0],t=e[1],e=e[2],r=r.map(function(e){var r=e.key;return r===t.key?t:{key:r,value:e.value}});return r.push({key:"price_policy_objects",value:e}),r})},getRebatePolicySourceConfig:function(){return this.getConfigValues(["rebate_policy_source"]).then(function(e){return _slicedToArray(e,1)[0]})},getPolicyObjectConfig:function(){var t=this;return _asyncToGenerator(regeneratorRuntime.mark(function e(){var r;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.fetch({url:"/EM1HNCRM/API/v1/object/price_policy/service/findObjectsByModule",data:{module:"price_policy"}});case 3:return r=e.sent,e.abrupt("return",(null==r?void 0:r.Value)||[]);case 7:e.prev=7,e.t0=e.catch(0),CRM.util.alert($t("网络异常，请稍后重试"));case 10:case"end":return e.stop()}},e,null,[[0,7]])}))()},getExtraStatus:function(e,r){switch(e){case"price_policy_objects":var t={price_policy_objects:((null==r?void 0:r.price_policy_objects)||[]).map(function(e){return _objectSpread(_objectSpread({},e),{},{value:null==r?void 0:r["price_policy_".concat(e.apiName)]})})};return t.gift_fixed_attribute=null==r?void 0:r.gift_fixed_attribute,t;case"rebate":t=Array.isArray(e)?e.map(function(e){return r[e]}):r[e]||null;return["2","3"].includes(t)?{isDisable:!0,isWaiting:"2"===t,statusInfo:"2"===t?$t("crm.setting.promotionrebate.rebate.open_tip1",null,"返利开启中，开启时间较长，请等待CRM通知"):$t("crm.setting.promotionrebate.rebate.open_tip2",null,"开启失败，请查看CRM通知")}:null;default:return null}},beforeSetConfig:function(t,e){var n=arguments,o=this;return _asyncToGenerator(regeneratorRuntime.mark(function e(){var r;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,i.prototype.beforeSetConfig.apply(o,n);case 2:if(r=e.sent,"price_policy"===t)return e.next=6,o.isPricePolicyAllowed();e.next=8;break;case 6:e.next=18;break;case 8:if(!o.pricePolicyObjects.includes(t)){e.next=13;break}if(CRM._cache.advancedPricing){e.next=11;break}throw new Error($t("请先开启高级定价"));case 11:e.next=18;break;case 13:if("enforce_price_policy_priority"!==t){e.next=18;break}if(CRM._cache.advancedPricing){e.next=17;break}throw CRM.util.alert($t("请先开启高级定价")),new Error;case 17:r.confirmInfo=CRM._cache.openEnforcePricePolicy?$t("确认关闭高级定价优先级吗"):$t("确认开启高级定价优先级吗");case 18:return e.abrupt("return",r);case 19:case"end":return e.stop()}},e)}))()},isPricePolicyAllowed:function(){var o=this;return new Promise(function(e,r){var t=$t("crm.advancedPricingOpenFailureTitle",null,"[高级定价]开关，开启失败！"),n=[];"1"!==o.getKeyValues("28")?r(new Error($t("请先开启价目表"))):("2"===o.getKeyValues("promotion_status")&&n.push("[".concat($t("促销设置"),"]")),n.length?(t=t+"<br/>"+$t("crm.propertiesOpenFailedInfo",null,"失败原因：当前租户已开启")+n.join($t("和")),r(new Error(t))):e())})}}),t.exports.Base=i,t.exports.config=e});