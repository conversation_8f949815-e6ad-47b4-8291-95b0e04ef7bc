function asyncGeneratorStep(e,t,i,n,o,r,u){try{var a=e[r](u),c=a.value}catch(e){return void i(e)}a.done?t(c):Promise.resolve(c).then(n,o)}function _asyncToGenerator(a){return function(){var e=this,u=arguments;return new Promise(function(t,i){var n=a.apply(e,u);function o(e){asyncGeneratorStep(n,t,i,o,r,"next",e)}function r(e){asyncGeneratorStep(n,t,i,o,r,"throw",e)}o(void 0)})}}function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){var i;if(e)return"string"==typeof e?_arrayLikeToArray(e,t):"Map"===(i="Object"===(i={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:i)||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?_arrayLikeToArray(e,t):void 0}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=Array(t);i<t;i++)n[i]=e[i];return n}function _iterableToArrayLimit(e,t){var i=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=i){var n,o,r,u,a=[],c=!0,l=!1;try{if(r=(i=i.call(e)).next,0===t){if(Object(i)!==i)return;c=!1}else for(;!(c=(n=r.call(i)).done)&&(a.push(n.value),a.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=i.return&&(u=i.return(),Object(u)!==u))return}finally{if(l)throw o}}return a}}function _arrayWithHoles(e){if(Array.isArray(e))return e}define("crm-setting/cmmodityproduct/cmmodityproduct",["./data","crm-modules/common/util"],function(i,e,t){var n=i("./data"),d=i("crm-modules/common/util"),o=Backbone.View.extend({initialize:function(e){this.setElement(e.wrapper),this.init()},init:function(e){var t=this;i.async("vcrm/sdk",function(e){e.getComponent("backstage").then(function(e){t.initView(e.default,n)})})},initView:function(e,t){new Vue({el:this.$el[0],template:'<div style=\'height:100%\'>\n                                <Backstage ref="backstage" :data="dataList" @change="change"></Backstage>\n                          </div>',components:{Backstage:e},data:function(){return{dataList:t,modelValues:{is_open_quoter:{cache_key:"openQuoter",value:!1},is_open_attribute:{cache_key:"openAttribute",value:!1},is_open_nonstandard_attribute:{cache_key:"openNsAttribute",value:!1},multiple_unit:{cache_key:"multiunitStatus",value:!1},multi_unit_price_book:{cache_key:"multi_unit_price_book",value:!1},simple_cpq:{cache_key:"fixedCollocationOpenStatus",value:!1},spu:{cache_key:"productOpenSpu",value:!1},saveConfig:{cache_key:"spuStatus",value:!1},bom_price_calculation_configuration:{cache_key:"bom_price_calculation_configuration",value:"0"},multi_spec_display_style:{cache_key:"multi_spec_display_style",value:""},is_open_incremental_pricing:{cache_key:"is_open_incremental_pricing",value:"0"},close_old_category:{cache_key:"close_old_category",value:"0"},periodic_product:{cache_key:"periodic_product",value:!1},periodic_product_plugin:{cache_key:"periodic_product_plugin",value:"[]"},cpq_ui_mode:{cache_key:"cpq_ui_mode",value:"0"},non_standard_product:{value:!1},change_product_type_refresh_price:{value:!1}},licenses:{}}},watch:{},computed:{},created:function(){},mounted:function(){this.init()},methods:{getLisenses:function(e){return new Promise(function(t){CRM.api.get_licenses({key:e,cb:function(e){t(e)}})})},getDataByFilter:function(e,t,i,n){var o=this;e&&t&&i&&e.forEach(function(e){t(e)&&(i&&i(e),n)||(e=e.moduleList||e.children)&&o.getDataByFilter(e,t,i)})},isShowFixedCollocation:function(){return new Promise(function(t){CRM.util.getConfigValue("price_policy").then(function(e){return t("1"===e&&CRM.util.isGrayScale("CRM_FIXED_COLLOCATION"))})})},init:function(){var t=this,e=this;this.getLisenses(["cpq_attribute_app","cpq_subscription_product_management_app"]).then(function(e){t.licenses=e,t.dataList.forEach(function(e){"attributeConfig"==e.moduleId&&(e.visible=t.licenses.cpq_attribute_app),"productCyclical"==e.moduleId&&(e.visible=t.licenses.cpq_subscription_product_management_app)})}),e.isShowFixedCollocation().then(function(t){e.dataList.forEach(function(e){"simple_cpq"==e.moduleList[0].key&&(e.moduleList[0].isShow=t,e.visible=t)})}),CRM.util.getproductWithSpuConfig().done(function(t){e.dataList.forEach(function(e){e.moduleList[1]&&"saveConfig"==e.moduleList[1].key&&(e.moduleList[1].isShow=t,e.moduleList[2].isShow=t)})}),e.isShowPeriodicProduct().then(function(t){e.dataList.forEach(function(e){e=e.moduleList.find(function(e){return"periodic_product"===e.key});e&&(e.children[0].options=e.children[0].options.filter(function(e){return"NewOpportunityObj"===e.key?"open"==t.config_newopportunity_open:"SaleContractObj"!==e.key||"1"==t.sale_contract}))})}),e.getConfig()},getSpuConfig:function(){return new Promise(function(i,n){d.FHHApi({url:"/EM1HNCRM/API/v1/object/spu_sku_choose/service/choose_spu",success:function(e){var t;0===e.Result.StatusCode?i(null==(t=e.Value)?void 0:t.result):n(e.Result.FailureMessage)}},{errorAlertModel:1,submitSelector:""})})},getConfig:function(){var o=this,e=(e=Object.keys(this.modelValues)).filter(function(e){return"saveConfig"!==e});CRM.util.showLoading_tip(),Promise.all([this.getSpuConfig(),CRM.util.getConfigValues(e)]).then(function(e){var e=_slicedToArray(e,2),t=e[0],e=e[1];CRM.util.hideLoading_tip(),e.unshift({key:"saveConfig",value:t}),e&&e.forEach(function(e){var t=e.key,e=e.value,i=o.modelValues[t].cache_key,n=null;"saveConfig"===t?n="true"==e:["multi_spec_display_style","bom_price_calculation_configuration","cpq_ui_mode"].includes(t)?(n=e,"multi_spec_display_style"===t&&(n=n||"capsule")):n=["periodic_product_plugin"].includes(t)?e:"1"===e,i&&(CRM._cache[i]=n),o.$set(o.modelValues[t],"value",n),o.$set(o.modelValues[t],"originalValue",e)}),o.initDataList()}).catch(function(e){CRM.util.hideLoading_tip(),CRM.util.remind(3,e||$t("操作失败!"))})},isShowPeriodicProduct:function(){return _asyncToGenerator(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,CRM.util.getConfigValues(["config_newopportunity_open","sale_contract"]);case 2:return t=e.sent,e.abrupt("return",(t||[]).reduce(function(e,t){return e[t.key]=t.value,e},{}));case 4:case"end":return e.stop()}},e)}))()},initDataList:function(){var r=this;this.getDataByFilter(this.dataList,function(e){return!!e.key||!!e.moduleId},function(e){var t,i,n,o;e.moduleId?("mobileMultiUnitConfig"===e.moduleId&&(i=r.modelValues.multiple_unit.value,r.$set(e,"visible",i)),"cpqUIMode"===e.moduleId&&(i=r.modelValues.is_open_attribute.value||CRM._cache.cpqStatus,r.$set(e,"visible",i))):(i=null,(t=e.key)instanceof Array?i=t.map(function(e){return r.modelValues[e].value}):r.modelValues[t]&&(i=r.modelValues[t].value),["periodic_product_plugin"].includes(t)&&(i=JSON.parse(r.modelValues[t].value||"[]")),"close_old_category"===t?(o="2"==(n=r.modelValues[t].originalValue)?$t("sfa.crm.setting_cmmodityproduct.product_category_opening_status"):$t("crm.setting.tradeconfigure.warn_cannot_closed"),r.$set(e,"extraStatus",{statusInfo:o}),i="0"!==n):"simple_cpq"===t&&i?r.dataList.forEach(function(e){"simple_cpq"==e.moduleList[0].key&&(e.moduleList[0].children[0].isShow=!0)}):"periodic_product_plugin"===t&&r.$set(e,"isShow",r.modelValues.periodic_product.value),r.$set(e,"value",i))})},change:function(u,a){var c=this;return _asyncToGenerator(regeneratorRuntime.mark(function e(){var t,i,n,o,r;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=u.type,i=u.key,n=u.value,o=u.values,"switch"!==t&&"checkbox"!==t||(n=n?"1":"0"),e.next=4,c.beforeSetConfig({type:t,key:i,value:n,values:o},a);case 4:if(r=e.sent){e.next=7;break}return e.abrupt("return");case 7:r.confirmInfo?c.confirm=d.confirm(r.confirmInfo,$t("提示"),c.setConfig.bind(null,r,u)):c.setConfig(r,u);case 8:case"end":return e.stop()}},e)}))()},beforeSetConfig:function(e,u){var a=this,c=(e.type,e.key),l=e.value;return new Promise(function(t,e){if(c){var i,n={url:"/EM1HNCRM/API/v1/object/biz_config/service/set_config_value",data:{key:c,value:l}},o=u.confirmInfo?_.isFunction(u.confirmInfo)?u.confirmInfo():u.confirmInfo:null;if(n.confirmInfo=o,"saveConfig"==c&&(n={url:"/EM1HNCRM/API/v1/object/spu_sku_choose/service/save_spu_selector_config",data:{key:c,value:!CRM._cache.spuStatus,saveConfig:!CRM._cache.spuStatus},confirmInfo:CRM._cache.spuStatus?$t("crm.确定关闭选产品吗"):$t("crm.确定启用选产品吗")}),"spu"===c&&(n.confirmInfo=CRM._cache.productOpenSpu?$t("确认关闭商品设置吗"):$t("确认开启商品设置吗"),t(n)),"multiple_unit"===c&&(n=r(c,l)),"multi_unit_price_book"===c){if(!CRM._cache.multiunitStatus)return void d.alert($t("crm.multiunitStatus.open"));n=r(c,l)}"simple_cpq"===c&&(n=r(c,l)),"non_standard_product"===c&&(n=r(c,l,o)),"is_open_attribute"===c||"is_open_nonstandard_attribute"===c?a.isAttributeAllowed().done(function(e){e.value?("is_open_attribute"===c&&(n.confirmInfo=$t("开启当前开关时，请先开启[价目表开关].注意：开启属性后，将不能开启[商品设置][多单位设置][促销设置]")),"is_open_nonstandard_attribute"===c&&(n.confirmInfo=$t("开启当前开关时，请先开启[价目表开关].注意：开启非标属性后，将不能开启[商品设置][多单位设置][促销设置]")),t(n)):CRM.util.alert(e.info)}):"is_open_quoter"!==c||"1"!==l||CRM._cache.openAttribute?("periodic_product_plugin"===c&&(i=JSON.parse(a.modelValues[c].value),n.data.value=JSON.stringify(n.data.value.filter(function(e){return!i.includes(e)}))),t(n)):d.alert($t("开启当前开关时，请先开启[{{title}}]。",{title:$t("属性配置开启开关")}))}function r(e,t,i){var n={multiple_unit:$t("确认开启多单位吗"),multi_unit_price_book:$t("确认开启多单位定价吗"),simple_cpq:$t("确认开启固定搭配")};return{url:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",data:{tenantId:CRM.enterpriseId,moduleCode:e,openStatus:t},confirmInfo:i||n[e]}}})},isAttributeAllowed:function(){return new Promise(function(a,e){CRM.util.showLoading_tip(),CRM.util.getConfigValues(["28","spu","multiple_unit","promotion_status"]).then(function(e){CRM.util.hideLoading_tip();var t=$t("crm.propertiesOpenFailedTitle"),i=$t("crm.propertiesOpenFailedInfo"),n=!1,o=!1,r=!1,u=!1,e=(e.forEach(function(e){"promotion_status"===e.key&&(u="2"==e.value),"28"===e.key&&(n="1"==e.value),"spu"===e.key&&(o="1"==e.value),"multiple_unit"===e.key&&(r="1"==e.value)}),n&&!o&&!r&&!u);n?(o&&(i+="[".concat($t("商品设置"),"]")),r&&(i+=(o?$t("和"):"")+"[".concat($t("多单位设置"),"]")),u&&(i+=(!o||r?$t("和"):"")+"[".concat($t("促销设置"),"]"))):(t=$t("crm.setting.pricebookStatus",null,"开启当前开关时，请先开启[价目表开关] "),i=$t("crm.setting.pricebookStatus_tip",null,"注意：开启属性后，将不能开启[商品设置][多单位设置][促销设置]")),a({value:e,info:t+"</br>"+i})})}).catch(function(){CRM.util.hideLoading_tip()})},setConfig:function(n,e){var l=this,s=e.type,p=e.values;return _asyncToGenerator(regeneratorRuntime.mark(function e(){var o,t,i,r,u,a,c;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:o=l,t=n.data,i=t.key,r=t.moduleCode,u=t.value,a=t.openStatus,c=i||r,CRM.util.waiting(),l.$refs.backstage.commonSetConfig({url:n.url,data:n.data,complete:function(){var e;null!=(e=o.confirm)&&e.hide()}}).then(function(e){CRM.util.waiting(!1);var t,i,n;!["multiple_unit","multi_unit_price_book","simple_cpq"].includes(c)||e.Value.success&&0===e.Value.errCode?(t=null,i=l.modelValues[c].cache_key,n="switch"===s&&"[object Object]"===Object.prototype.toString.call(e.Value)&&_.isEmpty(e.Value),["is_open_attribute","is_open_nonstandard_attribute","spu","is_open_quoter","close_old_category","saveConfig"].includes(c)||n?(t=r?"1"===a:"1"===u,"spu"==c?o.init():"close_old_category"==c?l.getConfig():"saveConfig"===c&&o.dataList.forEach(function(e){e.moduleList[2]&&"mobilemultispec"==e.moduleList[2].type&&(e.moduleList[2].isVis=t)})):t="simple_cpq"==c?(o.dataList.forEach(function(e){"simple_cpq"==e.moduleList[0].key&&(e.moduleList[0].children[0].isShow=!0)}),"1"===e.Value.value.openStatus):"bom_price_calculation_configuration"==c?(o.dataList.forEach(function(e){"simple_cpq"==e.moduleList[0].key&&(e.moduleList[0].children[0].value=u)}),u):"is_open_incremental_pricing"===c?"1"===u:["multi_spec_display_style","cpq_ui_mode"].includes(c)?u:["periodic_product_plugin"].includes(c)?JSON.stringify(p):"1"===e.Value.value.openStatus,l.$set(l.modelValues[c],"value",t),i&&(CRM._cache[i]=t),l.initDataList(),d.remind(1,$t("操作成功"))):d.alert(e.Value.errMessage||$t("操作失败!"))}).catch(function(e){CRM.util.waiting(!1),d.alert(e||$t("操作失败!"))});case 5:case"end":return e.stop()}},e)}))()}}})},destroy:function(){}});t.exports=o});
function asyncGeneratorStep(t,e,i,l,o,s,c){try{var n=t[s](c),r=n.value}catch(t){return void i(t)}n.done?e(r):Promise.resolve(r).then(l,o)}function _asyncToGenerator(n){return function(){var t=this,c=arguments;return new Promise(function(e,i){var l=n.apply(t,c);function o(t){asyncGeneratorStep(l,e,i,o,s,"next",t)}function s(t){asyncGeneratorStep(l,e,i,o,s,"throw",t)}o(void 0)})}}define("crm-setting/cmmodityproduct/data",[],function(t,e,i){var l,o=[{domId:"level0",moduleId:"multipleUnit",title:$t("多单位配置"),moduleList:[{type:"switch",title:$t("多单位开启开关"),key:"multiple_unit",value:!1,displayCount:3,isDisabled:!1,isShow:!0,enableClose:!1,render:null,radioOptions:[],describeList:[{title:$t("多单位设置说明1"),list:[]},{title:$t("多单位设置说明2"),list:[]},{title:$t("多单位设置说明3"),list:[]},{title:$t("单位性质"),list:[$t("不支持自定义选项值"),$t("根据单位性质指定赠品单位")]},{title:$t("多单位设置说明4"),list:[]},{title:$t("多单位设置说明5"),list:[]},{title:$t("多单位设置说明6"),list:[]}]},{type:"switch",title:$t("多单位定价开启开关"),key:"multi_unit_price_book",value:!1,displayCount:3,isDisabled:!1,isShow:!0,enableClose:!1,render:null,radioOptions:[],describeList:[{title:$t("多单位定价说明1"),list:[]},{title:$t("多单位定价说明2"),list:[]},{title:$t("多单位定价说明3"),list:[$t("多单位定价说明3-1"),$t("多单位定价说明3-2")]},{title:$t("多单位定价说明4"),list:[]}]}]},{domId:"level1",title:$t("多单位模式配置"),visible:CRM._cache.multiunitStatus,moduleId:"mobileMultiUnitConfig",isChange:!1,moduleList:[{type:"MobileMulticonfig",title:$t("crm.MobileMulticonfig.modeSelection"),value:"0",_id:"multi_unit_mode_config",key:"multi_unit_mode_config",describeList:[{title:$t("多单位模式配置1"),list:[{title:$t("多单位模式配置1.1")},{title:$t("多单位模式配置1.2")}]},{title:$t("多单位模式配置2"),list:[{title:$t("多单位模式配置2.1")}]},{title:$t("多单位模式配置3")}]},{type:"MobileMultUnitConfig",title:$t("单位显示样式"),key:"multi_unit_show_type",describeList:[{title:$t("单位显示样式1"),list:[{title:$t("单位显示样式1.1")}]},{title:$t("下拉展示")+":",list:[{title:$t("下拉展示1.1")},{title:$t("下拉展示1.2")},{title:$t("下拉展示1.3")}]},{title:$t("单位显示样式3")}]}]},{domId:"level2",moduleId:"spuConfig",title:$t("商品配置"),moduleList:[{type:"switch",title:$t("商品配置开启开关"),key:"spu",value:!1,displayCount:3,isDisabled:!1,isShow:!0,enableClose:!0,render:null,radioOptions:[],describeList:[{title:$t("开关默认关闭，商品、规格&规格值对象隐藏，直接在产品对象下维护产品数，且选择产品页面，无选择商品页面，直接选择产品，即开关“选择产品设置”无法打开"),list:[]},{title:$t("此开关可以从默认的关闭状态修改为开启状态，但开启后不可再关闭"),list:[]},{title:$t("线上企业，如不存在多规格产品，可以关闭此开关，系统会将商品上的字段“标签（原商品标签）、是否多单位、批次与序列号管理“的值自动赋值到产品对象，但关闭后，不可再开启"),list:[]},{title:$t("线上企业，如关闭此开关，系统会判断商品上是否存在自定义字段，商品对象被查找关联，字段被引用等，均需要调整现有关联到所需产品对象之后，才可关闭，系统不做自动处理"),list:[]},{title:$t("如企业有OpenAPI的对接，请做好接口的升级后，开启或关闭此开关"),list:[]}]},{type:"switch",title:$t("基于商品选择产品"),key:"saveConfig",value:!1,displayCount:3,isDisabled:!1,isShow:!1,enableClose:!0,render:null,radioOptions:[],describeList:[{title:$t("crm.选商品产品开关描述"),list:[]},{title:$t("开启该开关后，移动端支持选择产品样式配置"),list:[]},{title:$t("crm.该开关仅作用"),list:[]},{title:$t("此开关可根据业务需要反复开启和关闭。"),list:[]},{title:$t("请注意: 移动端使用该功能，需升级至6.5以上版本"),list:[]}]},{type:"mobilemultispec",title:$t("移动端多规格商品选择产品样式配置"),key:"multi_spec_display_style",value:"",isVis:CRM._cache.spuStatus,isDisabled:!1,isShow:!1,radioOptions:[],describeList:[]}]},{domId:"level3",moduleId:"attributeConfig",title:$t("属性配置"),visible:!1,licenseKey:"cpq_attribute_app",moduleList:[{type:"switch",title:$t("属性配置开启开关"),key:"is_open_attribute",value:!1,displayCount:2,isDisabled:!1,isShow:!0,enableClose:!1,render:null,radioOptions:[],describeList:[{title:$t("该开关一旦开启，不可关闭。")},{title:$t("开启当前开关时，请先开启 [ 价目表开关 ]。"),list:[function(t){return[t("div",{class:"crm-intro-warning"},[$t("注：开启属性后，将不能开启 [ 商品设置 ] [ 多单位设置 ] [ 促销设置 ]")])]}]},{title:$t("开关开启：通过属性属性值关联产品，供报价或下单时使用。属性有启用/禁用，关联产品等功能，实现产品业务差异化"),list:[$t("[ 属性 ]、[ 产品属性价目表 ] 对象显示"),function(t){return[t("div",{style:{padding:"0 10px"}},$t("注：同时订单产品、报价单明细：显示 [ 属性 ]、[ 产品属性价目表名称 ]"))]},$t("[订单 ]、[报价单 ] “从历史订单”入口屏蔽"),function(t){var e={padding:"0 10px"};return[t("li",{},"3.3 ".concat($t("[ 产品属性价目表 ] 使用"))),t("li",{style:e},"3.3.1 ".concat($t("销售订单、报价单中，产品适配的价目表带出规则不变（默认带出客户的最优可售范围中产品的最优的价目表）"))),t("li",{style:e},"3.3.2 ".concat($t("影响点：产品、订单产品、报价单明细中，产品价格以及折扣的取值"))),t("li",{style:e},"a、 ".concat($t("开启的[ 产品属性价目表 ] 适配某价目表后， [ 产品属性选择后 ] 价格取值： [ 产品属性价目表 ] 中 [ 当前属性价格 ] ，折扣带出依据  [ 产品属性价目表 ] 中[ 定价方式 ] 判断"))),t("li",{style:e},"① ".concat($t("[ 定价方式 ] 为 [ 价目表折扣 ] ，[ 折扣 ] 取值 [ 适配的价目表 ] 中：产品折扣"))),t("li",{style:e},"② ".concat($t("[ 定价方式 ] 为 [ 指定折扣 ] ，[ 折扣 ] 取值 [ 产品属性价目表 ] 中：产品属性的[ 折扣 ]"))),t("li",{style:e},"b、".concat($t("[ 产品属性价目表 ] 未适配某价目表， [ 产品属性选择后 ] 取值：依据 [ 价目表 ] 中 [ 价目表售价 ] 和 [ 价目表折扣 ]")))]}]}],children:[{title:$t("报价器启用开关",null,"报价器启用开关"),key:"is_open_quoter",type:"switch",value:!1,isShow:!0,enableClose:!1,describeList:[$t("该开关一旦开启，不可关闭。"),$t("开启当前开关时，请先开启[{{title}}]。",{title:$t("属性配置开启开关")})]},{title:$t("sfa.CRM.setting.enable_attribute_incremental_pricing",null,"启用属性增量定价"),key:"is_open_incremental_pricing",type:"switch",value:!1,enableClose:!1,describeList:[$t("该开关一旦开启，不可关闭。"),$t("开启当前开关时，请先开启[{{title}}]。",{title:$t("属性配置开启开关")})]}]},{isShow:!0,title:$t("非标属性开启开关"),key:"is_open_nonstandard_attribute",type:"switch",value:!1,enableClose:!1,describeList:[{title:$t("开启或关闭的操作，只可操作一次，请慎重操作")}]}]},{domId:"level34",moduleId:"cpqUIMode",title:$t("cpq.cpq_ui_mode",null,"产品选配模式"),visible:!1,moduleList:[{type:"CpqUIMode",key:"cpq_ui_mode",value:"0",displayCount:3,isDisabled:!1,isShow:!0,enableClose:!0,render:null}]},{domId:"level4",moduleId:"simpleCpq",title:$t("固定搭配"),visible:(l=_asyncToGenerator(regeneratorRuntime.mark(function t(){var e;return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,CRM.util.getConfigValue("price_policy");case 2:return e=t.sent,t.abrupt("return",e&&"1"===e.value&&CRM.util.isGrayScale("CRM_FIXED_COLLOCATION"));case 4:case"end":return t.stop()}},t)})),function(){return l.apply(this,arguments)}),moduleList:[{type:"switch",title:$t("固定搭配开启开关"),key:"simple_cpq",value:!1,displayCount:3,isDisabled:!1,isShow:!1,enableClose:!1,render:null,radioOptions:[],describeList:[$t("CRM.setting.fastsale.fixedCollocation.intro.desc1"),$t("CRM.setting.fastsale.fixedCollocation.intro.desc2"),$t("CRM.setting.fastsale.fixedCollocation.intro.desc3"),$t("CRM.setting.fastsale.fixedCollocation.intro.desc4")],children:[{title:$t("CRM.setting.fastsale.fixedCollocation.bom.description.title",null,"产品包价格是否包含包内产品价格"),key:"bom_price_calculation_configuration",type:"radio",value:"0",isShow:!1,radioOptions:[{label:$t("CRM.setting.fastsale.fixedCollocation.bom.description.option1",null,"产品包价格包含包内产品价格"),warningMessage:$t("CRM.setting.fastsale.fixedCollocation.bom.description.option1.warn_info1",null,"选用此选项，则：产品包价格为整个固定搭配的价格，可用于为整个固定搭配定价。"),value:"0"},{label:$t("CRM.setting.fastsale.fixedCollocation.bom.description.option2",null,"产品包价格不包含所添加产品价格"),warningMessage:$t("CRM.setting.fastsale.fixedCollocation.bom.description.option2.warn_info1",null,"选用此选项，则：产品包价格+搭配内产品的金额（数量x价格）为固定搭配的价格；"),secWaringMessage:$t("CRM.setting.fastsale.fixedCollocation.bom.description.option2.warn_info2",null,"当固定搭配的“组合销售类型”为【产品包不独立销售】时，选此开关产品包价格设置为0，则：搭配内产品的总金额（数量x价格）为固定搭配的价格。"),value:"1"}]}]}]},{domId:"level5",moduleId:"productCategory",title:$t("sfa.crm.setting_cmmodityproduct.product_category_module_title"),moduleList:[{type:"switch",title:$t("sfa.crm.setting_cmmodityproduct.product_category_title"),key:"close_old_category",value:!1,enableClose:!1,confirmInfo:$t("crm.setting.tradeconfigure.confirm_open_notclose",{name:$t("sfa.crm.setting_cmmodityproduct.product_category_module_title")}),describeList:[{title:$t("sfa.crm.setting_cmmodityproduct.product_category_desc1")},{title:$t("sfa.crm.setting_cmmodityproduct.product_category_desc2")},{title:$t("sfa.crm.setting_cmmodityproduct.product_category_desc3")},{title:$t("sfa.crm.setting_cmmodityproduct.product_category_desc4")},{title:$t("sfa.crm.setting_cmmodityproduct.product_category_desc5"),list:[{title:$t("sfa.crm.setting_cmmodityproduct.product_category_desc5.1")},{title:$t("sfa.crm.setting_cmmodityproduct.product_category_desc5.2"),list:[{title:$t("sfa.crm.setting_cmmodityproduct.product_category_desc5.2.1")},{title:$t("sfa.crm.setting_cmmodityproduct.product_category_desc5.2.2")},{title:$t("sfa.crm.setting_cmmodityproduct.product_category_desc5.2.3")}]},{title:$t("sfa.crm.setting_cmmodityproduct.product_category_desc5.3")}]},{title:$t("sfa.crm.setting_cmmodityproduct.product_category_desc6"),list:[{title:$t("sfa.crm.setting_cmmodityproduct.product_category_desc6.1")},{title:$t("sfa.crm.setting_cmmodityproduct.product_category_desc6.2")},{title:$t("sfa.crm.setting_cmmodityproduct.product_category_desc6.3")}]}]}]},{domId:"level6",moduleId:"productCyclical",title:$t("sfa.crm.setting_cmmodityproduct.periodic_product_module_title",null,"周期性产品"),visible:!1,licenseKey:"cpq_subscription_product_management_app",moduleList:[{type:"switch",title:$t("sfa.crm.setting_cmmodityproduct.periodic_product_title",null,"周期性产品开启开关"),key:"periodic_product",value:!1,enableClose:!1,confirmInfo:$t("确认开启{{name}}吗",{name:$t("sfa.crm.setting_cmmodityproduct.periodic_product_title")}),isShow:!0,describeList:[],children:[{title:$t("sfa.crm.setting_cmmodityproduct.periodic_product_plugin_title",null,"开启以下对象范围"),key:"periodic_product_plugin",type:"checkbox-group",value:[],isShow:!1,enableClose:!1,confirmInfo:$t("开启后不可关闭"),options:[{key:"NewOpportunityObj",label:$t("商机")},{key:"QuoteObj",label:$t("报价单")},{key:"SaleContractObj",label:$t("销售合同")},{key:"SalesOrderObj",label:$t("销售订单")}],describeList:[$t("sfa.crm.setting_cmmodityproduct.periodic_product_plugin.desc1",null,"开启开关后，产品支持周期性特征配置，如产品设置计费模式、计费周期等信息。")]}]}]},{domId:"level7",moduleId:"productNonstandard",title:$t("sfa.crm.setting_cmmodityproduct.nonstandard_product_module_title",null,"非标产品"),visible:!0,moduleList:[{type:"switch",title:$t("sfa.crm.setting_cmmodityproduct.nonstandard_product_title",null,"非标产品开启开关"),key:"non_standard_product",value:!1,enableClose:!1,confirmInfo:$t("确认开启{{name}}吗",{name:$t("sfa.crm.setting_cmmodityproduct.nonstandard_product_title")}),isShow:!0,describeList:[$t("sfa.crm.setting_cmmodityproduct.nonstandard_product.desc1",null,"开启开关后，支持[事先不明确具体产品]的交易，自动以特定的【默认非标产品】进行流转，可填写非标产品的描述，不进行各种业务强校验（如价格上下限）。"),$t("sfa.crm.setting_cmmodityproduct.nonstandard_product.desc2",null,"说明：非标产品的能力体现在新建编辑页布局上。如需启用非标产品，请先开启交易单据的新建编辑页布局。")],children:[{title:$t("sfa.crm.setting_nonstandard_product.not_reprice",null,"交易单据的非标品行切换为普通产品时，不重新取价"),key:"change_product_type_refresh_price",type:"switch",value:!1,isShow:!0,describeList:[{title:$t("sfa.crm.setting_nonstandard_product.not_reprice.msg1",null,"支持单据：商机、报价单、销售合同、销售订单，及配置了非标产品插件的其他对象")},{title:$t("sfa.crm.setting_nonstandard_product.not_reprice.msg2",null,"开关关闭：单据里非标品行的【产品名称】更新为普通品时，产品相关信息更新，价格信息按普通品重新取价、价格相关字段重新计算，其他与产品和价格无关的字段保持不变。开关默认关闭。")},{title:$t("sfa.crm.setting_nonstandard_product.not_reprice.msg3",null,"开关开启：单据里非标品行的【产品名称】更新为普通品时，产品相关信息更新，价格及价格相关字段保留原值，不重新取价，其他与产品和价格无关的字段保持不变")}]}]}]}];i.exports=o});