define("crm-setting/tradeconfigure/common/config",[],function(t,e,i){function r(t){return _.isUndefined(t)||!!t}i.exports={CONFIG_DATA:[{domId:"level0",moduleId:"businessBills",title:$t("crm.setting.tradeconfigure.module_business_bills",null,"业务单据配置"),moduleList:[{domId:"level0_0",moduleId:"Quote",title:$t("crm.QuoteObj",null,"报价单"),moduleList:[{title:$t("试算"),type:"switch",key:"is_test_calculate",value:!1,describeList:[{title:$t("报价单试算说明1"),list:[{title:$t("报价单试算说明2")},{title:$t("报价单试算说明3")}]},{title:$t("[试算]开关，可反复操作")}]},{title:$t("开启历史报价"),type:"switch",key:"quote_history_price",value:!1,describeList:[{title:$t("报价单历史报价说明1")},{title:$t("报价单历史报价说明2")}]}]},{domId:"level0_2",moduleId:"SalesOrder",title:$t("crm.SalesOrderObj",null,"销售订单"),moduleList:[{title:$t("整单折扣与产品"),type:"radio",key:"16",dependKeys:["23"],value:"0",radioOptions:[{label:"[".concat($t("整单折扣"),"] = [").concat($t("crm.SalesOrderObj.field.销售订单金额"),"] / [").concat($t("crm.SalesOrderObj.field.产品合计"),"]").concat($t("产品必填")),value:"0"},{label:"[".concat($t("整单折扣"),"]").concat($t("不跟随"),"[").concat($t("crm.SalesOrderObj.field.销售订单金额"),"]").concat($t("变化产品选填")),value:"1",disabled:function(t){return t[23]}},{label:"[".concat($t("整单折扣"),"]").concat($t("不跟随"),"[").concat($t("crm.销售订单金额"),"]").concat($t("变化产品必填")),value:"2",disabled:function(t){return t[23]}}]},{title:$t("订单产品开启“从历史订单”添加"),type:"switch",key:"clone_history_order_product",value:!1,describeList:[{title:$t("开启后可添加所选客户下的全部历史订单产品")}]},{title:$t("已确认的销售订单/退款，取消特定角色可编辑和可作废的权限"),type:"switch",key:"order_enlarge_edit_privilege",value:!1,describeList:[{title:$t("已确认订单说明a")},{title:$t("已确认订单说明b")},{title:$t("已确认订单说明c")},{title:$t("已确认订单说明d")}]},{title:$t("报价单转订单：收货人、收货人电话、收货人地址、仓库，默认值不覆盖映射值："),type:"switch",key:"order_to_quote_default_value_cover",value:!1,describeList:[{title:$t("开关默认关闭；订单对象中，收货人、收货人电话、收货人地址、仓库的默认值覆盖映射值")},{title:[{text:$t("若开关开启，报价单转订单后，订单对象中收货人、收货人电话、收货人地址、仓库的默认值")},{text:$t("不会覆盖"),styles:["primary"]},{text:$t("报价单转订单中此字段的映射值")}]}]},{title:$t("crm.setting.tradeconfigure.order_mobile_edit_page_summary_setting.title",null,"销售订单移动端：新建编辑页面左下角【合计】字段显示配置"),subTitle:$t("crm.setting.tradeconfigure.title_execution_object")+$t("crm.SalesOrderObj"),showTitleLine:!0,type:"MobileSelectNew",key:["order_mobile_edit_page_summary_setting"],value:[!1],options:[{label:$t("crm.setting.tradeconfigure.order_mobile_edit_page_summary_setting.options.summary",null,"显示【合计】字段"),key:"order_mobile_edit_page_summary_setting"}]},{title:$t("销售订单浮动上下限校验配置开关"),type:"radio",key:"ignore_check_ceiling_floor_price",value:"0",isShow:!0,radioOptions:[{label:$t("crm.orderrule.ignore_check_ceiling_floor_price.label0"),value:"0"},{label:$t("crm.orderrule.ignore_check_ceiling_floor_price.label1"),value:"1"}]},{title:$t("开启订单支付能力"),type:"switch",key:"is_sales_order_pay_directly_enable",value:!1,isShow:!0,describeList:[{title:$t("在企业钱包中绑定了微信或者支付宝后，才可启用线上支付能力")},{title:$t("线上支付是基于订单支付，且默认生成回款。支持业务员面对面收款，也支持经销商客户在线支付")},{title:$t("为了确保支付流水能成功生成回款，建议在回款/回款明细上尽可能不要添加必填的自定义字段，否则将无法生成回款")}],setUrl:"/EM1HNCRM/API/v1/object/pay/service/set_sales_order_pay_directly"},{title:$t("crm.SalesOrderObj.address"),type:"switch",key:"order_addr_support_search",value:!1,isShow:!0,describeList:[{title:$t("crm.SalesOrderObj.address.des1"),list:[{title:$t("crm.SalesOrderObj.address.des1.1")},{title:$t("crm.SalesOrderObj.address.des1.2")}]},{title:$t("crm.SalesOrderObj.address.des2")}]},{title:$t("crm.SalesOrderObj.close_switch"),type:"switch",key:"order_close_status",value:"0",isShow:CRM.util.isGrayScale("CRM_ORDER_CLOSE"),enableClose:!0,describeList:[],children:[{_id:"order_close",title:$t("crm.SalesOrderObj.close_switch_tip"),type:"checkbox",enableClose:!1,isShow:function(t){return t.order_close_status},key:["delivery_status","accounts_receivable_status"],value:[],options:[{key:"delivery_status",label:$t("crm.delivery_status"),disabled:function(){return!0}}],describeList:[{title:$t("sfa.crm.SalesOrderObj.close_tip1")},{title:$t("sfa.crm.SalesOrderObj.close_tip2")}]}]}]},{domId:"level0_4",moduleId:"InvoiceApplication",title:$t("crm.InvoiceApplicationObj",null,"开票申请"),moduleList:[{title:$t("crm.setting.tradeconfigure.invoice_order_binding_status_title"),key:"invoice_order_binding_status",value:!0,enableOpen:!1,describeList:[{title:$t("crm.setting.tradeconfigure.invoice_order_binding_status_desc1")},{title:$t("crm.setting.tradeconfigure.invoice_order_binding_status_desc2"),list:[{title:$t("crm.setting.tradeconfigure.invoice_order_binding_status_desc2-1")},{title:$t("crm.setting.tradeconfigure.invoice_order_binding_status_desc2-2"),list:[{title:[{text:$t("crm.setting.tradeconfigure.invoice_order_binding_status_desc2-2-1"),styles:["primary"]}]}]}]},{title:$t("crm.setting.tradeconfigure.invoice_order_binding_status_desc1")}]},{title:$t("sfa.crm.setting_tradeconfigure.invoice_lines_multi_source_title"),key:"invoice_lines_multi_source",value:!1,isShow:function(t){var e=t.invoice_order_binding_status,t=t.new_invoice;return!r(e)&&t},describeList:[$t("sfa.crm.setting_tradeconfigure.invoice_lines_multi_source_desc")],children:[{title:$t("sfa.crm.setting_tradeconfigure.invoice_lines_mapping_rule_title"),key:"invoice_lines_mapping_rule",type:"InvoiceLinesMappingRule",isShow:function(t){return!!t.invoice_lines_multi_source},desc:[$t("sfa.crm.setting_tradeconfigure.invoice_lines_mapping_rule_desc")]},{title:$t("sfa.crm.setting_tradeconfigure.invoice_lines_required_title"),key:"invoice_lines_required",type:"InvoiceLinesRequired",isShow:function(t){var e=t.invoice_order_binding_status,t=t.new_invoice;return!r(e)&&t},describeList:[$t("sfa.crm.setting_tradeconfigure.invoice_lines_required_desc")]}]},{title:$t("开票明细"),key:"new_invoice",value:!1,enableClose:!1,isShow:function(t){t=t.invoice_order_binding_status;return r(t)},describeList:[{title:$t("开票明细开启后模式变化")},{title:$t("支持多订单合并开票")},{title:$t("销售订单详情页可查看状态")},{title:$t("客户详情页可查看开票申请信息")},{title:$t("按需调整打印模板")},{title:$t("特别注意"),list:[{title:$t("确保OpenAPI已升级")},{title:$t("按客户需求配置BI分析")},{title:$t("开启中暂无法使用开票申请")}]}]},{title:$t("支持超额开票"),key:"invoice_is_allowed_overflow",value:!1,isShow:function(t){var e=t.new_invoice;return r(t.invoice_order_binding_status)&&r(e)},describeList:[{title:$t("开启可超额开票后，开票金额允许大于销售订单金额")},{title:$t("未开启/关闭时，开票金额不允许大于销售订单金额")},{title:$t("关闭后，已超额的开票不可编辑修改")}]},{title:$t("启用订单产品为开票明细"),key:"invoice_mode",enableClose:!1,isShow:function(t){var e=t.new_invoice;return r(t.invoice_order_binding_status)&&r(e)},describeList:[{title:$t("未启用时，销售订单作为开票明细")},{title:$t("启用后，订单产品作为开票明细")}]},{title:$t("开票明细快捷操作显示规则"),key:"invoice_show_quick_op_rule",value:"",type:"radio",isShow:function(t){var e=t.new_invoice;return r(t.invoice_order_binding_status)&&r(e)},radioOptions:[{label:$t("按发票金额开票"),value:"amount"},{label:$t("按待开票数量比例开票"),value:"count",disabled:function(t){return!t.invoice_mode}},{label:$t("隐藏快捷操作"),value:"none"}]},{title:$t("数据汇总显示"),key:"invoce_show_sum_data",value:!1,isShow:function(t){var e=t.new_invoice;return r(t.invoice_order_binding_status)&&r(e)},describeList:[{title:$t("数据汇总显示开启")},{title:$t("数据汇总显示关闭")},{title:$t("数据汇总开关可反复操作")}]},{title:$t("crm.setting.tradeconfigure.invoice_support_negative_and_zero_title"),key:"invoice_support_negative_and_zero",value:!1,isShow:function(t){var e=t.invoice_order_binding_status,t=t.new_invoice;return r(e)&&r(t)},describeList:[{title:$t("crm.setting.tradeconfigure.invoice_support_negative_and_zero_desc1"),list:[{title:$t("crm.setting.tradeconfigure.invoice_support_negative_and_zero_desc1-1")},{title:$t("crm.setting.tradeconfigure.invoice_support_negative_and_zero_desc1-2"),list:[{title:[{text:$t("crm.setting.tradeconfigure.invoice_support_negative_and_zero_desc1-2-1"),styles:["primary"]}]}]},{title:$t("crm.setting.tradeconfigure.invoice_support_negative_and_zero_desc1-3")}]},{title:$t("crm.setting.tradeconfigure.invoice_support_negative_and_zero_desc2")}]}]},{domId:"level0_5",moduleId:"RefundObj",title:$t("crm.RefundObj"),moduleList:[{title:$t("crm.setting.tradeconfigure.refund_order_binding_status_title"),key:"refund_order_binding_status",value:!0,describeList:[{title:$t("crm.setting.tradeconfigure.refund_order_binding_status_desc1")},{title:$t("crm.setting.tradeconfigure.refund_order_binding_status_desc2"),list:[{title:$t("crm.setting.tradeconfigure.refund_order_binding_status_desc2-1")},{title:[{text:$t("crm.setting.tradeconfigure.refund_order_binding_status_desc2-3"),styles:["primary"]}]}]}]}]},{domId:"level0_6",moduleId:"PaymentPlanObj",title:$t("crm.PaymentPlanObj"),moduleList:[{title:$t("crm.setting.tradeconfigure.payment_plan_order_binding_status_title"),key:"payment_plan_order_binding_status",value:!0,describeList:[{title:$t("crm.setting.tradeconfigure.payment_plan_order_binding_status_desc1")},{title:$t("crm.setting.tradeconfigure.payment_plan_order_binding_status_desc2"),list:[{title:$t("crm.setting.tradeconfigure.payment_plan_order_binding_status_desc2-1")},{title:[{text:$t("crm.setting.tradeconfigure.payment_plan_order_binding_status_desc2-3"),styles:["primary"]}]}]}]}]}]},{domId:"level1",moduleId:"moduleReprice",title:$t("crm.setting.tradeconfigure.module_reprice",null,"重新取价配置"),moduleList:[{title:$t("报价单转订单重新取价"),key:"get_price_when_convert",value:!1,displayCount:1,describeList:[{title:$t("报价单转订单"),list:[{title:$t("报价单转订单开关说明")},{title:$t("报价单转订单开关说明1")},{title:$t("报价单转订单开关说明2")},{title:$t("报价单转订单开关说明3")},{title:[{text:$t("报价单转订单开关注意"),styles:["primary"]}]}]},{title:$t("销售合同转订单"),list:[{title:$t("销售合同转订单开关说明")},{title:$t("销售合同转订单开关说明1")},{title:$t("销售合同转订单开关说明2")},{title:$t("销售合同转订单开关说明3")}]},{title:$t("报价单转销售合同"),list:[{title:$t("报价单转销售合同开关说明")},{title:$t("报价单转销售合同开关说明1")},{title:$t("报价单转销售合同开关说明2")},{title:$t("报价单转销售合同开关说明3")}]}]},{title:$t("订单复制、订单产品“从历史订单”添加、订单草稿转订单重新取价"),key:"get_price_when_copy",value:!1,describeList:[{title:$t("开关默认开启")},{title:$t("订单复制重新取价开关说明1"),list:[{title:$t("订单复制重新取价开关说明2")},{title:$t("订单复制重新取价开关说明3")},{title:$t("订单复制重新取价开关说明4")}]}]},{title:$t("报价单复制、报价单明细“从历史报价”添加重新取价"),key:"get_price_when_copy_quote",value:!1,describeList:[{title:$t("报价单历史添加重新取价开关说明")},{title:$t("报价单历史历史添加重新取价开关说明1")},{title:$t("报价单历史历史添加重新取价开关说明2")}]},{title:$t("销售合同复制重新取价"),key:"get_price_when_copy_contract",value:!1,describeList:[{title:$t("销售合同复制重新取价开关说明")},{title:$t("销售合同复制重新取价开关说明1")}]},{title:$t("商机2.0")+$t("复制重新取价"),key:"get_price_when_copy_newopportunity",value:!1}]},{domId:"level2",moduleId:"moduleSelectData",title:$t("crm.setting.tradeconfigure.module_select_data",null,"选数据页配置"),moduleList:[{title:$t("订单等对象选产品列表扩展字段"),key:"virtual_extension",value:!1,setConfigParam:2,displayCount:1,enableClose:!1,describeList:[{title:$t("虚拟字段显示由开关控制，开启后不支持关闭")},{title:$t("开关开启"),list:[{title:$t("选择数据页面增加扩展字段显示")},{title:$t("不显示扩展字段")},{title:$t("扩展字段如下"),list:[{title:$t("产品对象增加：价目表售价、价目表价格、可用库存虚拟字段")},{title:$t("商品对象增加：规格属性、产品编码、产品条形码、产品价格、价目表售价、价目表价格、可用库存虚拟字段")},{title:$t("价目表明细对象增加：可用库存虚拟字段")},{title:$t("注：可用库存字段是否显示，依据库存开关是否开启判断")}]},{title:$t("扩展字段，不支持筛选、搜索、排序、布局规则、验证规则")},{title:$t("Web端，通过 “场景设置→全部场景” 中配置扩展字段显示")},{title:$t("移动端，通过 “移动端摘要布局” 中配置扩展字段显示")}]}]},{title:$t("销售订单报价单选产品产品明细支持复制"),key:"43",value:!1,describeList:[{title:$t("请注意:移动端使用该功能，需升级至{{num}}以上版本",{num:"6.3.5"})},{title:$t("crm.场景举例")}],children:[{title:$t("选择数据页面，支持选择本单已选产品"),key:"tenant_whether_filter_order_select_product",type:"switch",value:!1,isShow:function(t){t=t[43];return void 0===t||t},describeList:[{title:$t("支持已选说明1")},{title:$t("支持已选说明2")},{title:$t("支持已选说明3")},{title:[{text:$t("sfa.crm.tradeconfigure.selectproduct.tip1",null,'注：如果企业要统一隐藏 "选择本单已选产品" 开关入口，则将 "销售订单、报价单、销售合同、商机2.0选产品，产品明细支持复制" 开关关闭即可'),styles:["primary"]}]}]}]},{title:$t("销售订单支持添加临时赠品"),key:"manual_gift",setConfigParam:2,enableClose:!1,value:!1,describeList:[{title:$t("开关默认关闭，若开关开启，订单产品可通过【添加临时赠品】直接增加赠品行")},{title:$t("sfa.crm.manual_gift.config_tip2")},{title:$t("临时赠品行的取价与普通产品行相同，包括【价格】、【价目表价格】等；【是否赠品】为“是”")},{title:$t("临时赠品行的计算逻辑"),list:[{title:$t("如果没有开启价格政策，【销售单价】、【小计】、【折扣】默认为0，可编辑")},{title:$t("如果开启了价格政策，视为整单促销的赠品，【销售单价】、【小计】、【折扣】强制为0")}]}]},{title:$t("订单报价单选产品产品名称搜索支持选择"),key:"product_keyword_search_mode",value:"",type:"radio",radioOptions:[{label:$t("单关键词搜索"),value:""},{label:$t("多关键词且的搜索"),warningMessage:$t("搜索结果为")+"："+$t("关键词且"),value:"and"},{label:$t("多关键词或的搜索"),warningMessage:$t("搜索结果为")+"："+$t("关键词或"),value:"or"}],describeList:[{title:$t("产品名称搜索举例")},{title:$t("单关键词搜索")+""+$t("单关键词举例")},{title:$t("多关键词且的搜索")+""+$t("多关键词且举例")},{title:$t("多关键词或的搜索")+""+$t("多关键词或举例")}]},{title:$t("新建订单移动端选产品支持输入数量"),key:"input_custom_fields",value:!1,isShow:!0,describeList:[{title:$t("启用该开关后，选择产品/商品列表不支持展开步进器，而是显示+号。用户点击后，系统根据是否有订单产品必填字段显示步进器，还是浮窗")},{title:[{text:$t("除了数量字段，还支持订单产品必填的自定义字段，字段类型仅支持")},{text:$t("单行文本、单选、多选、数字、金额、日期、时间、日期时间、手机、布尔、百分比共11种"),styles:["bold","primary"]}]},{title:$t("此能力支持：“销售订单”对象，选择数据页面，选择数据为产品的模式")},{title:$t("数据选择区域显示+图标不支持默认展开步进器效果")},{title:$t("不管新老客户，默认该参数都为关闭")},{title:$t("由于自定义字段是拉取订单产品的必填字段，尽管其他对象也有选择产品的场景，但先支持订单对象")}],children:[{title:$t("crm.setting.tradeconfigure.input_display_fields",null,"显示字段配置"),type:"MobileOrderFieldConfig",key:"input_display_fields",value:"",isShow:function(t){return!!t.input_custom_fields}}]},{title:$t("移动端页面左下角配置"),subTitle:$t("crm.setting.tradeconfigure.title_execution_object")+[$t("crm.NewOpportunityObj"),$t("crm.QuoteObj"),$t("crm.SalesOrderObj"),$t("crm.SaleContractObj")].join("、"),showTitleLine:!0,type:"MobileSelectNew",key:"mobile_bottom_summary_setting",value:[!1,!1],options:[{label:$t("显示已选总金额（已选数据的价目表价格汇总值）"),key:"mobile_bottom_summary_setting",value:"totalAmount"},{label:$t("显示已选产品的种类数"),key:"mobile_bottom_summary_setting",value:"categories"}]},{title:$t("crm.setting.tradeconfigure.use_shop_category_title"),value:!1,key:"use_shop_category",isShow:CRM.util.isGrayScale("CRM_USE_SHOP_CATEGORY"),describeList:[{title:$t("crm.setting.tradeconfigure.use_shop_category_desc1")},{title:$t("crm.setting.tradeconfigure.use_shop_category_desc2")}]}]}],KEY_CONFIG:{sale_contract:{cache_key:"sale_contract"},16:{type:"string"},is_customer_account_enable:{type:"string"},invoice_show_quick_op_rule:{type:"string"},invoice_mode:{type:"string"},product_keyword_search_mode:{type:"string"},ignore_check_ceiling_floor_price:{type:"string"},is_sales_order_pay_directly_enable:{},choose_spu:{type:"string"},mobile_bottom_summary_setting:{type:"string"},is_payment_enter_account_enable:{type:"string"},claim_upon_receipt_of_payment:{type:"string"},is_payment_with_detail_enter_account_enable:{type:"string"},input_display_fields:{type:"string"},contract_connector:{type:"string"},invoice_lines_mapping_rule:{type:"jsonstring"},invoice_lines_required:{type:"jsonstring"},sale_contract_record_type_mapping:{type:"jsonstring"}}}});
function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){var r;if(e)return"string"==typeof e?_arrayLikeToArray(e,t):"Map"===(r="Object"===(r={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:r)||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray(e,t):void 0}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function _iterableToArrayLimit(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],s=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return u}}function _arrayWithHoles(e){if(Array.isArray(e))return e}function ownKeys(t,e){var r,n=Object.keys(t);return Object.getOwnPropertySymbols&&(r=Object.getOwnPropertySymbols(t),e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)),n}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(r),!0).forEach(function(e){_defineProperty(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function _defineProperty(e,t,r){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"==_typeof(e)?e:e+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0===r)return("string"===t?String:Number)(e);r=r.call(e,t||"default");if("object"!=_typeof(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}function asyncGeneratorStep(e,t,r,n,o,i,a){try{var u=e[i](a),s=u.value}catch(e){return void r(e)}u.done?t(s):Promise.resolve(s).then(n,o)}function _asyncToGenerator(u){return function(){var e=this,a=arguments;return new Promise(function(t,r){var n=u.apply(e,a);function o(e){asyncGeneratorStep(n,t,r,o,i,"next",e)}function i(e){asyncGeneratorStep(n,t,r,o,i,"throw",e)}o(void 0)})}}define("crm-setting/tradeconfigure/tradeconfigure",["../promotionrebate/promotionrebate","./common/config"],function(e,t,r){var a=e("../promotionrebate/promotionrebate").Base,e=e("./common/config"),n=e.CONFIG_DATA,o=e.KEY_CONFIG;r.exports=a.extend({getConfigData:function(){return n},getConfigKeyData:function(){return o},beforeSetConfig:function(r,n){var o=arguments,i=this;return _asyncToGenerator(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,a.prototype.beforeSetConfig.apply(i,o);case 2:if(t=e.sent,"sale_contract"===r)return e.next=6,CRM.util.getEditLayoutStatus("SalesOrderObj");e.next=12;break;case 6:if(e.sent){e.next=9;break}throw new Error($t("请先开启{{apiName}}新建布局",{apiName:CRM.config.objDes["SalesOrderObj".toLowerCase()].name}));case 9:t.confirmInfo=$t("确定要开启销售合同吗"),e.next=31;break;case 12:if("manual_gift"!==r)e.next=24;else if("1"!=n)e.next=21;else{if(CRM._cache.promotionStatus)throw new Error($t("已开启促销，无法开启临时赠品"));e.next=18}break;case 18:t.confirmInfo=$t("确定要开启临时赠品吗");case 19:e.next=22;break;case 21:throw new Error($t("企业已开通临时赠品不允许关闭"));case 22:e.next=31;break;case 24:if("new_invoice"!==r||"1"!==n)e.next=30;else{if("2"==i.getOKeyValues(r))throw new Error($t("正在开启中，请耐心等待"));e.next=28}break;case 28:e.next=31;break;case 30:["order_close_status","delivery_status","accounts_receivable_status"].includes(r)&&i.handleOrderCloseConfig(t,r,n);case 31:return e.abrupt("return",t);case 32:case"end":return e.stop()}},e)}))()},handleOrderCloseConfig:function(e,t,r){var n=this.getOKeyValues("order_close"),n=JSON.parse(n);"order_close_status"==t?n.status=r:n[t]=r,"1"==n.status&&(n.delivery_status="1"),e.data={key:"order_close",value:JSON.stringify(n)}},transValue:function(e,r,t){var n,o,i;return"16"===e?t?((n=Array(3).fill("0")).splice(Number(r),1,1),n.join(",")):r.split(",").findIndex(function(e){return"1"===e})+"":"invoice_mode"===e?t?r?"sales_order_product":"normal":"sales_order_product"===r:"mobile_bottom_summary_setting"===e?(n=this.getConfigKeyDataByKey(e).options.map(function(e){return e.value}),r&&(t?(o={},n.forEach(function(e,t){o[e]=r[t]?"1":"0"}),JSON.stringify(o)):(i=JSON.parse(r),n.map(function(e){return"1"===i[e]})))):"input_display_fields"===e?r&&(t?JSON.stringify(r):JSON.parse(r)):r},handleModuleItemChange:function(e){var t=e.key,r=e.values;return"mobile_bottom_summary_setting"===t?_objectSpread(_objectSpread({},e),{},{value:r}):e},setConfig:function(e){this.currentChangeKey;return a.prototype.setConfig.apply(this,arguments)},afterSetConfig:function(e){var e=e.update,t=this.currentChangeKey;"new_invoice"===t?CRM.util.sendLog("setting","invoice",{operationId:"openMode2"}):"invoice_mode"===t?CRM.util.sendLog("setting","invoice",{operationId:"openMode3"}):"invoice_order_binding_status"===t?CRM.util.sendLog("setting","invoice",{operationId:"invoice_order_binding_status"}):"invoice_support_negative_and_zero"===t?CRM.util.sendLog("setting","invoice",{operationId:"invoice_support_negative_and_zero"}):"invoice_lines_multi_source"===t?e(["invoice_lines_mapping_rule","invoice_lines_required"]):"is_open_additional_contract"===t&&e(["sale_contract_record_type_mapping"])},getConfig:function(e,t){var n=this;return t?this.getConfigValues(e):Promise.all([this.getConfigValues(e),this.getChooseSPUConfig(),this.getOrderCloseOptions()]).then(function(e){var e=_slicedToArray(e,3),t=e[0],r=e[1];return n.parseOrderCloseConfig(t,e[2]),t.concat([{key:"choose_spu",value:r}])})},getOrderCloseOptions:function(){var t=["accounts_receivable_status"];return new Promise(function(n,e){CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/accounts_receivable/service/get_config",data:{keys:t},success:function(e){var t,r=[];0==(null==e||null==(t=e.Result)?void 0:t.StatusCode)&&(r=(null==(t=e.Value)?void 0:t.values)||[]),n(r)}},{errorAlertModel:1})})},parseOrderCloseConfig:function(r){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:[],t=r.find(function(e){return"order_close"==e.key});t&&(t=JSON.parse(t.value),Object.entries(t).forEach(function(e){var e=_slicedToArray(e,2),t=e[0],e=e[1];"status"!==t?r.push({key:t,value:e}):r.push({key:"order_close_status",value:e})})),"2"==(null==(t=(e||[]).find(function(e){return"accounts_receivable_status"==e.key}))?void 0:t.value)&&(e=null==(e=null==(e=null==(e=(n||[]).find(function(e){return"businessBills"===e.moduleId}))||null==(t=e.moduleList)?void 0:t.find(function(e){return"SalesOrder"===e.moduleId}))||null==(t=e.moduleList)?void 0:t.find(function(e){return"order_close_status"===e.key}))||null==(t=e.children)?void 0:t.find(function(e){return"order_close"===e._id}))&&(e.options=e.options||[],e.options.find(function(e){return"accounts_receivable_status"==e.key})||e.options.push({key:"accounts_receivable_status",label:$t("crm.accounts_receivable_status")}))},getChooseSPUConfig:function(){return this.fetch({url:"/EM1HNCRM/API/v1/object/spu_sku_choose/service/choose_spu"}).then(function(e){return"true"===e.Value.result})},setPaymentEnterAccountConfig:function(){var r=this;return _asyncToGenerator(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if("2"!==(t=r).getKeyValues("is_customer_account_enable"))throw new Error($t("该租户未启用客户账户模块请联系管理员开通"));e.next=3;break;case 3:return e.next=5,t.confirm($t("启用回款入账将在对象上新增等字段并预设按钮确定要启用吗"));case 5:return e.next=7,t.fetch({url:"/EM1HNCRM/API/v1/object/fund_account/service/payment_enter_account_init"});case 7:case"end":return e.stop()}},e)}))()},setPaymentPayEnableConfig:function(){var n=this;return _asyncToGenerator(regeneratorRuntime.mark(function e(){var t,r;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=n,e.next=3,t.fetch({url:"/EM1HNCRM/API/v1/object/payment_pay/service/query_valid_isv"}).then(function(e){return e.Value.valid_isv_list});case 3:if((r=e.sent)&&r.length)return e.next=7,t.confirm($t("确定要开启显示支付能力吗"));e.next=11;break;case 7:return e.next=9,t.fetch({url:"/EM1HNCRM/API/v1/object/payment_pay/service/enable_payment_pay"});case 9:e.next=15;break;case 11:return e.next=13,t.confirm($t("企业钱包暂未绑定支付宝或微信账号请先绑定后在启用"),{btnLabel:{confirm:$t("现在绑定"),cancel:$t("知道了")}});case 13:throw window.location.hash="#app/entwallet/wallet",new Error;case 15:case"end":return e.stop()}},e)}))()},setClaimUponReceiptOfPayment:function(){var t=this;return _asyncToGenerator(regeneratorRuntime.mark(function e(){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.fetch({url:"/EM1HNCRM/API/v1/object/payment_claim/service/claim_upon_receipt_of_payment"});case 2:case"end":return e.stop()}},e)}))()}}),r.exports.config=e});