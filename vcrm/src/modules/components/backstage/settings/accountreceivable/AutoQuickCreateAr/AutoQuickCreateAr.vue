<template>
    <div class="auto-quick-create-ar">
        <div class="auto-create-ar-container">
            <h2>设置字段映射</h2>
            <field-mapping :objOptions="objOptions" />
        </div>
        <div class="auto-create-ar-container create-rule-container">
            <h2>创建规则</h2>
            <fx-tabs v-model="activeObjName" @tab-click="handleClick">
                <fx-tab-pane v-for="item in objOptions" :key="item.value" :label="item.label" :name="item.value">
                    <create-rule :objName="item.value" />
                </fx-tab-pane>
            </fx-tabs>
        </div>
    </div>
</template>

<script>
import FieldMapping from './FieldMapping'
import CreateRule from './CreateRule'

const objOptions = [
    {
        label: '销售订单',
        value: 'SalesOrderObj',
        ruleApiName: 'rule_quickAr_salesorderobj',
    },
    {
        label: '销售合同',
        value: 'SalesContractObj',
        ruleApiName: 'rule_quickAr_salecontractobj',
        isShow: CRM._cache.sale_contract
    },
].filter(item => item.isShow ?? true)
export default {
    name: 'AutoQuickCreateAr',
    components: {
        FieldMapping,
        CreateRule
    },
    data() { 
        return {
            objOptions,
            activeObjName: objOptions[0].value
        }
    },
    methods: {
    }
}
</script>

<style lang="less">
.auto-quick-create-ar {
    display: flex;
    flex-direction: column;
    gap: 12px;
    .auto-create-ar-container {
        h2 {
            font-size: 14px;
            line-height: 20px;
            color: #181C25;
            font-weight: 700;
            margin-bottom: 8px;
        }

        &.create-rule-container {
            h2 {
                margin-bottom: 0px;
            }
        }
    }
}
</style>
