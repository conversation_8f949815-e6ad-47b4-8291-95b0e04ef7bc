<template>
    <fx-dialog
        custom-class="create-rule-form"
        :visible.sync="cVisible"
        :title="title"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :append-to-body="true"
    >
        <fx-form :model="formData" :rules="formRules" ref="rForm" label-width="120px">
            <template v-for="item in formDataConfig">
                <fx-form-item :label="item.label" :prop="item.prop">
                    <fx-input
                        v-if="item.type === 'input'"
                        v-model="formData[item.prop]"
                        :placeholder="item.placeholder"
                        v-bind="item.attrs"
                    />
                    <fx-transfer
                        :ref="'rField' + item.prop"
                        v-else-if="item.type === 'transfer'"
                        v-model="formData[item.prop]"
                        :data="reveivableFields"
                    />
                    <filtergroup
                        :ref="'rField' + item.prop"
                        v-else-if="item.type === 'filter'"
                        :value="formData[item.prop]"
                        :options="{
                            apiname: objApiName
                        }"
                    />
                    <cascader-condition
                        :ref="'rField' + item.prop"
                        v-else-if="item.type === 'cascadercondition'"
                        :value="formData[item.prop]"
                    />
                    <span v-else>{{ item.prop }}</span>
                </fx-form-item>
            </template>
        </fx-form>
        <div slot="footer" class="dialog-footer">
            <fx-button @click="handleCancel" size="mini">{{ $t('取消') }}</fx-button>
            <fx-button type="primary" @click="handleSubmit" :loading="confirmLoading" size="mini">{{ $t('确定') }}</fx-button>
        </div>
    </fx-dialog>
</template>

<script>
import Filtergroup from '@components/Filtergroup/Filtergroup.vue'
import CascaderCondition from './CreateRuleFormFieldCascaderCondition.vue'
const formDataConfig = [
    {
        label: '规则名称',
        prop: 'name',
        type: 'input',
        placeholder: '请输入规则名称'
    },
    {
        label: '适用条件',
        prop: 'conditions',
        type: 'filter',
        manualSetValue: true,
        options: {
            fields: {
                type: Object,
                default: () => ({})
            },
        }
    },
    {
        label: '最大分期数',
        prop: 'max_period_count',
        type: 'input',
        attrs: {
            type: 'number',
            max: 9999,
            decimalPlaces: 0,
            isPositiveNum: true,
        },
        placeholder: '请输入最大分期数'
    },
    {
        label: '允许最小分摊',
        prop: 'minimum_amount_condition',
        type: 'cascadercondition',
        manualSetValue: true
    },
    {
        label: '添加关键信息',
        prop: 'information_fields',
        type: 'transfer',
        options: []
    }
]
export default {
    name: 'CreateRuleForm',
    components: {
        Filtergroup,
        CascaderCondition
    },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        objApiName: {
            type: String,
            default: ''
        },
        editData: {
            type: Object,
            default: () => ({})
        },
        confirmLoading: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            title: '新建规则',
            formDataConfig,
            formData: {},
            formRules: {
                name: [
                    { required: true, message: '请输入规则名称', trigger: 'blur' }
                ],
                max_period_count: [
                    { required: true, message: '请输入最大分期数', trigger: 'blur' }
                ],
            },
            reveivableFields: []
        }
    },
    computed: {
        cVisible: {
            get() {
                return this.visible
            },
            set(val) {
                this.$emit('update:visible', val)
            }
        }
    },
    created() {
        this.fetchReveivableFieldList();
        this.formData = JSON.parse(JSON.stringify(_.pick(this.editData, ['name', 'conditions', 'max_period_count', 'minimum_amount_condition', 'information_fields', '_id'])))
    },
    methods: {
        getFieldComponent() {},
        manualSetValue(field, value) {
            const manualSetValueFields = formDataConfig.filter(item => item.manualSetValue);
            manualSetValueFields.forEach((field) => {
                const component = this.$refs[`rField${field.prop}`][0];
                if (component && component.getValue) {
                    this.formData[field.prop] = component.getValue();
                }
            });
        },
        validate() {
            this.manualSetValue();
            return new Promise((resolve, reject) => {
                this.$nextTick(() => {
                    this.$refs.rForm.validate((valid, fields) => {
                        if (valid) {
                            resolve();
                        } else {
                            console.log('Validation errors:', fields);
                            reject(fields);
                        }
                    });
                });
            });
        },
        handleCancel() {
            this.$emit('update:visible', false)
        },
        async handleSubmit() {
            await this.validate();
            this.$emit('confirm', {
                ...this.formData,
                id: this.formData._id,
                object_api_name: this.objApiName
            })
        },
        async fetchReveivableFieldList() {
            const res = await CRM.util.getDescribeLayout({
                apiname: 'AccountsReceivableNoteObj',
                include_layout: false,
                include_detail_describe: false
            })
            // 数量、文本、日期、金额
            const allowType = ['number', 'text', 'date', 'currency']
            this.reveivableFields = Object.values(res?.objectDescribe?.fields || {})
                .filter(item => item.define_type !== 'system')
                .filter(item => allowType.includes(item.type))
                .map(item => ({
                    label: item.label,
                    key: item.api_name
                }));
        }
    }
}
</script>

<style lang="less">
.create-rule-form {
}
</style>