<template>
    <div ref="reactiveFormWrapper" class="create-rule-form-field-cascader-condition"></div>
</template>

<script>
import crmRequire from '@common/require'
export default {
    name: 'CreateRuleFormFieldCascaderCondition',
    props: {
        // [
        //     {
        //         "filters": [
        //             {
        //                 "field_name": "product_id",
        //                 "operator": "EQ",
        //                 "field_values": [
        //                     "67f1f264167eba000113248c"
        //                 ]
        //             },
        //             {
        //                 "field_name": "product_id.product_status",
        //                 "operator": "EQ",
        //                 "field_values": [
        //                     "1"
        //                 ]
        //             }
        //         ],
        //         "connector": "OR"
        //     }
        // ]
        value: {
            type: Array,
            default: () => ([])
        }
    },
    data() {
        return {
            productSelectFieldOptions: [],
        }
    },
    mounted() {
        this.renderReactiveForm();
    },
    methods: {
        parseData() {
            const value = typeof this.value === 'string' ? JSON.parse(this.value) : this.value;
            return (value?.[0]?.filters || []).map(item => {
                const { field_name, field_values, min_value } = item;
                const isProductCategory = field_name.split('.').length > 1;
                return {
                    feild_type: isProductCategory ? 'product_category' : 'product_id',
                    field_value: isProductCategory ? {
                        field_name: field_values[0],
                        object_api_name: field_name.split('.')[1],
                    } : field_values[0],
                    min_value,
                }
            })
        },
        getValue() {
            const filters = this.reactiveForm.getValue().map((item) => {
                const { feild_type, field_value, min_value } = item;
                const isProductCategory = feild_type === 'product_category';
                if (isProductCategory) {
                    return {
                        field_name: 'product_id.' + field_value.object_api_name,
                        field_values: [field_value.field_name],
                        operator: 'EQ',
                        min_value,
                    }
                } else {
                    return {
                        field_name: 'product_id',
                        field_values: [field_value],
                        operator: 'EQ',
                        min_value,
                    }
                }
            })
            return JSON.stringify([{
                filters,
                connector: 'OR'
            }]);
        },
        async getColumns() {
            const productSelectFieldOptions = await this.fetchProductFieldOptions();
            return [
                {
                    label: '按',
                    type: 'text'
                },
                {
                    api_name: 'feild_type',
                    type: 'select',
                    defaultValue: 'product_id',
                    options: [
                        {
                            label: '产品名称',
                            value: 'product_id'
                        },
                        {
                            label: '产品类别',
                            value: 'product_category'
                        }
                    ],
                    children: {
                        'product_id': [
                            {
                                api_name: 'field_value',
                                type: 'select',
                                options: [
                                    {
                                        label: '产品A',
                                        value: '产品A'
                                    },
                                    {
                                        label: '产品B',
                                        value: '产品B'
                                    }
                                ]
                            },
                            {
                                type: 'text',
                                label: '不低于合约',
                            },
                            {
                                api_name: 'min_value',
                                type: 'numInput',
                                inputType: 'percent'
                            }
                        ],
                        'product_category': [
                            {
                                api_name: 'field_value',
                                type: 'cascade',
                                options: productSelectFieldOptions
                            },
                            {
                                type: 'text',
                                label: '不低于合约',
                            },
                            {
                                api_name: 'min_value',
                                type: 'numInput',
                                inputType: 'percent'
                            }
                        ]
                    },
                },
                
            ]
        },
        async renderReactiveForm() {
            const me = this;
            const ReactiveForm = await crmRequire('crm-modules/buscomponents/action_field/index').then((rst) => rst.C.ReactiveForm.ReactiveForm);
            const columns = await this.getColumns();
            const defaultValue = this.parseData();
            this.reactiveForm = new ReactiveForm(
                $(me.$refs.reactiveFormWrapper),
                columns,
                defaultValue,
                false,
            );
        },
        async fetchProductFieldOptions() {
            const res = await CRM.util.getDescribeLayout({
                apiname: 'ProductObj',
                include_layout: false,
                include_detail_describe: false
            })
            const allowType = ['select_one']
            return Object.values(res?.objectDescribe?.fields || {})
                .filter(item => item.define_type !== 'system')
                .filter(item => allowType.includes(item.type))
                .map(item => ({
                    label: item.label,
                    value: item.api_name,
                    children: item.options
                }));
        },
    }
}
</script>

<style lang="less">
.create-rule-form-field-cascader-condition {
    .crm-reactive-form {
        padding: 0;
        .crm-reactive-form-content .form-row {
            margin-bottom: 0;
        }
    }
}
</style>