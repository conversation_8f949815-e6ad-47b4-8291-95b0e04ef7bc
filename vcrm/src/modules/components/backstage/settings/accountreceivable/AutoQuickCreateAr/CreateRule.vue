<template>
    <div class="create-rule">
        <div class="create-rule-title">
            <span class="create-rule-title-tips">{{ addTips }}</span>
            <fx-button type="primary" size="mini" @click="handleAddRule">新建规则</fx-button>
        </div>
        <fx-table :data="tableData" :loading="listLoading">
            <fx-table-column
                prop="name"
                label="规则名称"
            />
            <fx-table-column
                width="100"
                prop="action"
                :label="$t('操作')"
            >
            <template slot-scope="scope">
                <fx-link type="standard" size="mini" :underline="false" @click="handleAction('edit', scope)">{{ $t('编辑') }}</fx-link>
                <fx-link type="standard" size="mini" :underline="false" @click="handleAction('delete', scope)">{{ $t('删除') }}</fx-link>
            </template>
            </fx-table-column>
        </fx-table>
        <create-rule-form
            v-if="createRuleFormVisible"
            ref="createRuleForm"
            :editData="editRuleData"
            :visible.sync="createRuleFormVisible"
            :objApiName="objName"
            :confirmLoading="updateLoading"
            @confirm="updateRuleData"
        />
    </div>
</template>

<script>
import CreateRuleForm from './CreateRuleForm'
export default {
    name: 'CreateRule',
    components: {
        CreateRuleForm
    },
    props: {
        objName: {
            type: String,
            required: true
        }
    },
    data() {
        return {
            addTips: '最多创建10条规则',
            tableData: [],
            editRuleData: {},
            createRuleFormVisible: false,
            listLoading: false,
            updateLoading: false,
        }
    },
    created() {
        this.fetchRuleList();
    },
    methods: {
        handleAddRule() {
            this.createRuleFormVisible = true
        },
        async fetchRuleList() {
            try {
                this.listLoading = true;
                const {ruleDatas = []} = await CRM.util.ajax_base('/EM1HNCRM/API/v1/object/accounts_receivable/service/ar_quick_rule_list', {objectApiName: this.objName}, null, true) || {};
                this.tableData = ruleDatas;
            } catch (error) {
            } finally {
                this.listLoading = false;
            }
        },
        async updateRuleData(data) {
            try {
                this.updateLoading = true;
                await CRM.util.ajax_base('/EM1HNCRM/API/v1/object/accounts_receivable/service/ar_quick_rule_create_or_update', data, null, true)
                this.$message.success($t('操作成功'));
                this.createRuleFormVisible = false;
                this.fetchRuleList();
            } catch (error) {
            } finally {
                this.updateLoading = false;
            }
        },
        async deleteRule(id) {
            try {
                await CRM.util.ajax_base('/EM1HNCRM/API/v1/object/accounts_receivable/service/ar_quick_rule_delete', {id})
                this.fetchRuleList();
            } catch (error) {
            } finally {
                this.$message.success($t('操作成功'));
            }
        },
        handleAction(type, scope) {
            if (type === 'edit') {
                this.createRuleFormVisible = true;
                this.editRuleData = scope.row;
            } else if (type === 'delete') {
                this.$confirm('确定删除该规则吗？', $t('提示'), {
                    type: 'warning'
                }).then(() => {
                    this.deleteRule(scope.row._id);
                });
            }
        }
    }
}
</script>

<style lang="less">
.create-rule {
    .create-rule-title {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        .create-rule-title-tips {
            margin-right: 8px;
        }
    }
}
</style>