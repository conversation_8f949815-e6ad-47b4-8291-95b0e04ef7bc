/**
 * @Desc: bom模式2
 * <AUTHOR>
 * @date 2024/8/24
 */
define(function (require, exports, module) {
	var PickSelfObject = require('crm-modules/components/pickselfobject/pickselfobject');
	var DraggableContainer = require('crm-modules/common/draggablecontainer/draggablecontainer');
	var util = CRM.util;
	var mixin = require('crm-modules/buscomponents/pickself_bom_util/pickself_bom_util');
	var SelectedAttr = require('./selected_attr_value');
    var ShoppingCartModel = require('./shoppingCart');
	var QuoterTrial = require('crm-modules/buscomponents/quoter_trial/index').default;
	//

	/**
	 * @param opt
	 * 	{
	 * 		btnName:'按钮 1',
	 * 		type: 'primary',
	 * 		btnAction: 'add',
	 * 		btnClick: ({btnAction, api} = {}) => {
	 * 			console.log('按钮 1', btnAction, api);
	 * 		}
	 * 	}
	 */
	var pickSelf_bom2 = function (opt) {
		this.options = _.extend({
			forceHideCategory: false,
			hideMoney: false,  		// 隐藏金额
			hideQuantity: false,	// 隐藏修改数量
			defaultWidth_left: 400, // 左侧默认宽度
			otherBtns: [],			// 其他按钮
            _renderAttributeTable: CRM._cache.openAttribute || CRM._cache.openNsAttribute,

			changeAttrHook: null,  // 处理属性展示勾子
			changeAttrConstraintHook: null,		// fn 切换属性级联勾子
			renderAfterHook: null,				// fn 渲染后勾子
			changeAttrAfterHook: null,			// fn 切换属性后勾子
		}, opt);
	};

	_.extend(pickSelf_bom2.prototype, PickSelfObject.prototype, {
		...mixin,

		// getTable() {
		// 	return Table;
		// },

		// getTableConfig(){
		// 	return 'PTable'
		// },

		// 查移动端摘要布局，默认展示布局第一个字段；
		async fetchMobileColumns(obj){
			let me = this;
			await util.fetchColumns({
				apiname: obj.apiname,
				layout_agent_type: "mobile",
			},null,(res) => {
				me._showTableField = res.Value.layout.components[0].include_fields[0].api_name;
			})
		},

		// 查移动端产品摘要布局，默认展示布局第一个字段；
		async fetchMobileProductColumns(obj){
			if(this.mobileProductColumns) return;
			let me = this;
			await util.fetchColumns({
				apiname: 'ProductObj',
				layout_agent_type: "mobile",
			},null,(res) => {
				me.mobileProductColumns = res.Value.layout.components[0].include_fields;
				console.log('me.mobileProductColumns', me.mobileProductColumns)
			})
		},

		async render(obj){
            obj.isMultiple = obj.hideShoppingCart || obj.pluginContext?.hideShoppingCart ? obj.isMultiple : false;
            obj.dialogOptions = {
                btnName: {
                    save: $t('crm.sfa.bom.style2.product.order_current', null, '当前产品下单'), // confirm
                    cancel: $t('取消'),
                },
                ...obj.dialogOptions,
            }
			await this.fetchMobileColumns(obj);
			PickSelfObject.prototype.render.apply(this, arguments);
		},

		 beforeRender() {
			this.firstRender = true;
			this.options.notShowAttribute = true;
			this.options.isHideCPQIcon = true;
			this._cacheError = {};	// 缓存错误行标记
		},

		afterRenderTableComplete(){
			let api = this.getApi();
			this.options.getApiHook && this.options.getApiHook(api)
		},

		async getAllCheckedData(){
			let r1 = await this.updateBomInfo();
			if(!r1) return false;
            let data = this.table.getValue(true);
			return data ? this.options.isMultiple ? data : [data] : data;
		},

		getApi(){
			return {
				getAllCheckedData: this.getAllCheckedData.bind(this), // 获取左侧table选中产品
				deleteSelectedData: this.deleteSelectedData.bind(this), // 删除已选数据，包括已选子件

			}
		},

		// 删除已选产品组合内的子件
		deleteChildren(list){
			let selectData = this.table.getValue(true);
			list = Array.isArray(list) ? list : [list];
			function fn(rowId, all) {
                all = Array.isArray(all) ? all : [all];
				let len = all.length;
				while (len--){
					let item = all[len];
					if(!item) return;
					if(item.rowId === rowId){
						all.splice(len--, 1);
						return true
					}else if(item.children?.length){
						fn(rowId, item.children)
					}
				}
			}
			list.forEach(item => {
				fn(item.rowId, selectData);
				this.selectBom?.treeTable.setRowCheckedByRowId(item.rowId, false);
			});
		},

		// 删除已选数据，包括已选子件
		deleteSelectedData(list){
			list = Array.isArray(list) ? list : [list];
			list.forEach(item => {
				if(item.pid){
					this.deleteChildren(item);
				}else{
					this.table.reduceRemberData([item], true);
					this.table.$el.find('tr[data-id="'+ item._id +'"]').find('.tb-checkbox-selected').removeClass('tb-checkbox-selected');
				}
			})
		},

		afterRender: function () {
			this.$el = this.dialog.element;
            let masterData = this.options.master_data;
            this._mcCurrency = masterData.mc_currency;
			this.changeDom();
			if (this.options.apiname !== 'PriceBookProductObj') {
				this.renderTable(this.options);
				return;
			}
			let _this = this;
			this.currentCheckedData = [];
			this.priceList = [];
			
			if (masterData.price_book_id) {
				this.priceList = [{
					label: masterData.price_book_id__r,
					value: masterData.price_book_id,
					id: masterData.price_book_id,
				}];
				this.renderPriceBook();
			} else {
				const {source_api_name: dApiName, pluginContext, details: optDetails} = _this.options;
				const details = optDetails || pluginContext?.dataGetter.getDetails() || {};
				const priceBookDesc = pluginContext && pluginContext.dataGetter.getDescribe()?.fields?.price_book_id;

				CRM.util.getPriceBookList(this.formatPriceBookListParam({
					accountId: masterData.account_id,
					partnerId: masterData.partner_id || '',
					enableIsValidorder: false,
					object_data: masterData || {},
					object_api_name: masterData.object_describe_api_name,   // 主对象apiname
					detail_object_api_name: dApiName, // 从对象apiname
					details: CRM.util.parsePriceBookDataRangeDetails(details, priceBookDesc, dApiName)
				}, this.options)).then(function (res) {
					_this.priceList = _this.parseDataToSelect(res.priceBookDataList || []);
					_this.renderPriceBook();
				});
			}
		},

		// 改 dom 结构，分左右
		changeDom() {
			let dialogElement = this.dialog.element;
			let dialogMain = dialogElement.find('.dialog-con-wrap');
			let dialogScroll = dialogElement.find('.dialog-scroll');
			let dialogBtns = dialogElement.find('.dialog-btns');
			let tableHeight = this.$twrap.height() - 52;
			let newContainer = `<div class="cpq_style2_container crm-dialog-selectbom">
								<div class="cpq_style2_container_left" ></div>
								<div class="cpq_style2_container_right" ></div>
							</div>`;
			dialogMain.append(newContainer);
			this.initDraggableContainer(dialogMain.find('.cpq_style2_container_left'), 600);
			dialogElement.find('.cpq_style2_container_right').append(dialogScroll);
			dialogElement.find('.cpq_style2_container_right').append(dialogBtns);
			let displayNone = this.options.hideMoney ? 'displayNone': '';
			let totalPriceBox = `<div class="cpq_style2_totalPriceBox ${displayNone}"><span>${$t('总金额')}：</span><span class="cpq_style2_totalPrice">0</span></div>`;	
			dialogElement.find('.draggable-container__main').append(`<div class="cpq_style2_container_table ${this.options.isMultiple ? '' : 'cpq_style2_container_table_single'}"></div>${totalPriceBox}`);

			// sfa.bom.style2.productInfo: 已选产品信息
			// sfa.bom.style2.attrInfo: 已选属性
			let rangeElement = `<div class="cpq_style2_container_right_box"> <div class="cpq_style2_container_right_header">
										<span class="cpq_style2_container_right_title">${$t('sfa.bom.style2.productInfo')}	</span>
										<span class="cpq_style2_container_right_detail">${$t('详情')}</span>
										<span class="cpq_style2_container_right_version"></span>
										<span class="cpq_style2_container_right_productInfoBtn_wrap">
											<span class="cpq_style2_container_right_productInfo_name">${$t('产品信息')}</span>
											<span class="fx-icon-list-collapse cpq_style2_container_right_productInfoBtn"></span>
										</span>
									</div>
									<div class="cpq_style2_container_right_rootAttr"></div>
									<div class="cpq_style2_container_right_main cpq_style2_attrRange " style="${tableHeight}px;">
										<div class="cpq_style2_container_right_attrContract"></div>
										<div class="cpq_style2_container_right_attrRange"></div>
										<div class="crm_style2_bomAttr_noData">${$t('请先选择产品')}</div> 
										<div class="cpq_style2_container_right_table"></div>
									</div>
								</div>
								<div class="cpq_style2_container_right_productInfo">
									<div class="crm_style2_bomAttr_noData_productInfo">${$t('请先选择产品')}</div> 
									<div class="cpq_style2_container_right_productInfo_header">
										<span class="cpq_style2_container_right_productInfo_title">${$t('sfa.bom.style2.productInfo')}</span> 
										<span class="cpq_style2_container_right_productInfo_tip"></span>
									</div>
									<div class="cpq_style2_container_right_productInfo_content">
									
									</div>
									<div class="cpq_style2_container_right_productInfo_attribute">
										<div class="cpq_style2_container_right_productInfo_attribute_header">
											<span class="cpq_style2_container_right_productInfo_attribute_title">${$t('sfa.bom.style2.attrInfo')}</span>
										</div>
										<div class="cpq_style2_container_right_productInfo_attribute_content">
											<div class="cpq_style2_container_right_productInfo_attribute_noData">${$t('无')}</div>
										</div>
									</div>
								</div>
								`;
			// bom.module2.msg1
			// 产品未关联属性，如需配置可在【产品详情】-【配置属性】中设置。
			dialogElement.find('.t-wrap').append(rangeElement);
		},

		// 渲染已选产品信息
		renderProductInfo: function (row) {
			if(CRM.util.isBom(row).isPackage){
				this.$el.find('.cpq_style2_container_right_productInfoBtn_wrap').hide();
				this.hideProductInfo();
				return;
			}
			let productInfo = row.product_id__ro || row;
			console.log('productInfo', productInfo)
			this.$el.find('.cpq_style2_container_right_productInfoBtn_wrap').show();
			this.$el.find('.cpq_style2_container_right_productInfo_header').show();
			// if(this.ProductInfo){
			// 	this.ProductInfo.productInfo = productInfo;
			// 	this.renderSelectedAttrValue(row);
			// 	return;
			// }
			let me = this;
			let el = this.$el.find('.cpq_style2_container_right_productInfo_content')[0];
			this.ProductInfo && this.ProductInfo.destroy();
			this.ProductInfo = FxUI.create({
				wrapper: el,
				template: `
					<div class="cpq_style2_container_right_productInfo_content_box">
						<div class="cpq_style2_container_right_productInfo_content_image" v-if="productInfo.picture_path?.length">
							<img class="cpq_style2_container_right_productInfo_content_image_img" :src="productInfo.picture_path[0].signedUrl" alt="" @click="showImage(productInfo.picture_path[0].signedUrl)"	>
						</div>
						<ul class="cpq_style2_container_right_productInfo_content_info">
							<li class="cpq_style2_container_right_productInfo_content_info_item" v-for="item in mobileProductColumns">
								<span class="cpq_style2_container_right_productInfo_content_info_name">
									{{item.label}}
								</span>
								<span class="cpq_style2_container_right_productInfo_content_info_value" :title="productValue(item.api_name)">
									{{productValue(item.api_name)}}
								</span>
							</li>
						</ul>
					</div>
				`,
				data() {
					return {
						mobileProductColumns: me.mobileProductColumns || [],
						productInfo: productInfo || {},
					}
				},

				methods: {
					productValue(apiName){
						let value = productInfo.hasOwnProperty(apiName + '__r') ? productInfo[apiName + '__r'] : productInfo[apiName];
						value = util.hasValue(value) ? value : '--';
						return value;
					},

					showImage(url){
						me.showImage(url);
					}
				},
			})
			this.renderSelectedAttrValue(row);
		},

		// 展示图片
		showImage(url){
			CRM.api.preview_image({
				datas: [{
					smallUrl: url,
					bigUrl: url
				}],
				index: 0,
				isNew: true,
			})
		},
		// 清除产品信息
		clearProductInfo(){
			this.ProductInfo && this.ProductInfo.destroy();
			this.ProductInfo = null;
			this.$el.find('.cpq_style2_container_right_productInfo_header').hide();

		},

		formatPriceBookListParam(data, opts) {
			if (this.options.formatPriceBookListParam) {
				return this.options.formatPriceBookListParam(data, opts);
			}
			return data;
		},

		parseDataToSelect: function (data) {
			data.forEach(item => {
				item.label = item.name;
				item.value = item._id;
				item.id = item._id;
			});
			return data;
		},

		// 渲染价目表tree
		renderPriceBook: function () {
			this.addFilterPrice(this.options);
			this.options.priceList = this.priceList;
			this.currentPriceBook = this.priceList[0] || '';
			this.options.pricebookId = this.currentPriceBook.id;
			this.$el.find('.cpq_style2_container_left').addClass('cpq_style2_hasPriceBook');
			this.renderTable(this.options);
		},

		// 更改表格头部布局；
		changeTableBatchTerm() {
			if (!this.firstRender) {
				return;
			}
			this.firstRender = false;
			let filter = this.table.$el.find('.filter-btn-wrap');
			let search = this.table.$el.find('.j-dt-sc-box');
			let category = this.table.$el.find('.category-wrap');
			let config = this.table.$el.find('.dt-control-btns');
			let batchTermBox = this.table.$el.find('.batch-term');
			batchTermBox.append(category);
			batchTermBox.append(search);
			batchTermBox.append(`<div class="cpq_style2_attrFilter" title="${$t('属性')}">${$t('属性')}</div>`);
			batchTermBox.append(config);
			batchTermBox.append(filter);
			batchTermBox.show();
			if (this.options.apiname === 'PriceBookProductObj') {
				search.addClass('search-float-left')
			}
		},

		tableInitComplete() {
			this.changeTableBatchTerm();
			this.initPriceBookSelect(this.priceList);
			this.options.renderAfterHook && this.options.renderAfterHook({
				api: this.getApi(),
				$footer: this.$el.find('.dialog-btns').find('.cpq_style2_footer_extend'),
			});
		},

		// 加载产品包价目表组件
		initPriceBookSelect(selectOpt) {
			if (!selectOpt || this.PriceBookSelect) return;
			var _this = this;
			this.table.$el.find('.batch-term').prepend(`<div class="bom_style2_pricebook"></div>`);
			this.PriceBookSelect = FxUI.create({
				wrapper: this.table.$el.find('.bom_style2_pricebook')[0],
				template: `<fx-select
								ref="el1"
								size="micro"
								v-model="value"
								:el-style="selectStyle"
								:options="options"
								:before-change="beforeChange"
								@change="change"
								@focus="focus"
								:disabled="disabled"
							  >${$t('请选择') + $t('价目表')}</fx-select>`,
				data() {
					return {
						value: selectOpt[0].id,
						selectStyle: {
							// width: '100px',
							// height: '24px'
						},
						options: selectOpt,
					}
				},
				methods: {
					 beforeChange(val, oldVal) {
						console.log('beforeChange...', val, oldVal)
					},
					focus(event) {
						console.log('focus:', event);
					},
					change(val) {
						let data = this.options.find(item => item.value == val);
						_this.afterPriceBookChange(val, data)
					},
				}
			})
		},

		afterPriceBookChange(priceBookId) {
            this.table.clearRemberData();
            this.clearCurrentRow();
			this.clearRight();
			this.refreshTable(priceBookId);
		},
        clearCurrentRow() {
            if (this.currentRow) {
                this.setRowQuantity(this.currentRow, null);
                this.currentRow = null;
            }
        },
		// 更新过滤条件，价目表id字段，刷新表格；
		refreshTable: function (priceBookId) {
			_.each(this.options.filters || [], function (item) {
				if (item.field_name === 'pricebook_id') item.field_values = [priceBookId];
			});
			this.options.object_data.pricebook_id = priceBookId;
			this.table.options.object_data.pricebook_id = priceBookId;
			this.table.pricebookId = priceBookId;
			this.table.options.pricebookId = priceBookId;
			// this.setTableCheckedData(this.currentCheckedData, this.table.table);
			this.table?.setParam({}, true);
		},

		// 添加价目表id字段，用于过滤
		addFilterPrice: function (options) {
			let me = this;
			let pbid = me.priceList[0] ? me.priceList[0].id : '';
			if (pbid) {
				_.each(options.filters, function (item) {
					if (item.field_name === "pricebook_id") item.field_values = [pbid];
				});
				options.object_data.pricebook_id = pbid;
			}
		},

		afterRenderHook() {
            if (!(this.options.isMultiple || this.options.hideShoppingCart || this.options.pluginContext?.hideShoppingCart)) {
                this.renderShoppingCart();
            }
			this.renderOtherBtns();
			this.dialog.element.addClass('pickselfobject_cpq_style2');
			let displayNone = this.options.hideMoney ? 'displayNone': '';
			let displayNone_qt = this.options.hideQuantity ? 'displayNone': '';

			this.$el.find('.dialog-btns').prepend(`<div class="cpq_style2_footer_extend"></div>`);
			this.$el.find('.dialog-btns').prepend(`<div class="cpq_style2_quantityBox" style="display: none">
									<span class="cpq_style2_quantityTitle ${displayNone_qt}">${$t('购买') + $t('数量')}：</span>
									<div class="cpq_style2_quantity ${displayNone_qt}"></div>
									<span class="cpq_style2_priceTitle ${displayNone}">${$t('金额')}</span><span class="cpq_style2_price ${displayNone}">0</span>
									<div class="cpq_style2_quoterTrial"></div>
								</div>`);
			this.initQuantity();
			this.initProductInfoTip();
		},

		// 初始化产品信息提示
		initProductInfoTip(){
			let msg = $t('sfa.bom.style2.productInfoTip', null, '展示【产品】对象【移动端摘要布局】中配置的字段，支持的字段类型：文本、数字、金额、日期、lookup 、单选、多选、布尔类型等，其它类型的字段可能无法正常显示！');
			this.productInfoTip = FxUI.create({
				wrapper: this.$el.find('.cpq_style2_container_right_productInfo_tip')[0],
				template: `<fx-tooltip class="item" :effect="effectType" :content="content" placement="bottom" transition="">
     				<span class="fx-icon-f-jingshi cpq_style2_container_right_productInfo_tip_icon"></span>
    </fx-tooltip>`,
				data() {
					return {
						content: msg,
						effectType: 'dark',
					}
				},
			})
		},

		// 渲染其他自定义按钮
		renderOtherBtns(){
			if(!this.options.otherBtns) return;
			let me = this;
			this.$dialogBtns.prepend(`<div class="cpq_style2_otherBtnBox"></div>`);
			FxUI.create({
				wrapper: this.$dialogBtns.find('.cpq_style2_otherBtnBox')[0],
				template: `<div class="cpq_style2_otherBtn">
					<fx-button v-for="item in otherBtns"
						size="small"
						:type="item.type"
						@click="btnClick(item)"
						>{{item.btnName}}
						</fx-button>
				</div>`,
				data() {
					return {
						otherBtns: me.options.otherBtns,
					}
				},
				methods: {
					btnClick(item) {
						// console.log('btnClick...', item.btnAction, item);
						if(item.btnClick) item.btnClick({btnAction: item.btnAction, api: me.getApi()});
					}
				}
			})
		},
        renderShoppingCart(){
            let me = this;
            this.$dialogBtns.prepend(`<div class="cpq_style2_shoppingCart_btnBox"></div>`);
            this.ShoppingCart = new ShoppingCartModel({
                wrapper: this.$dialogBtns.find('.cpq_style2_shoppingCart_btnBox')[0],
                hideMoney: me.options.hideMoney,
                hideQuantity: me.options.hideQuantity,
                _mcCurrency: me._mcCurrency,
                _calculateTotalPrice: this._calculateTotalPrice.bind(this),
                getProductName: this.getProductName.bind(this),
                getProductId: this.getProductId.bind(this),
                isOpenTieredPrice: this.isOpenTieredPrice.bind(this),
                getRealPrice: this.getRealPrice.bind(this),
            });
            this.ShoppingCart.$on('handleAddBtn', async () => {
                let r1 = await this.checkBomAndAttrValue();
                if(!r1) return;
                let value = await this.getCurrentData();
                if (!value) return;
                this.ShoppingCart.addToShoppingCart(value[0]);
            })
            this.ShoppingCart.$on('submit', async (dataList) => {
                let value = await this.validData(dataList);
                if (!value) return;
                this.ShoppingCart.closeDialog();
                me.parseValue(value, function (res) {
                    me.handelConfirm(res);
                });
            })

        },
		// 记录数量；
		_formatData(obj){
			if(this.table){
				let rememberData = this.table.getRemberData();
				if(!rememberData.length) return;
				let data = obj.dataList;
				rememberData.forEach(item => {
					let f = data.find(c => c._id === item._id);
					if(!f) return;
					f._selfQuantity = item._selfQuantity;
				})
                if (!data.length) {
                    this.table.clearRemberData();
                    this.clearCurrentRow();
                    this.clearRight();
                }
			}
		},

		changeParam(param) {
			this.checkedData = {};
			this._cacheBomCore = {};
			this.addEvents();
			param.hideAdd = true;
			param.el = this.dialog.element.find('.cpq_style2_container_table');
			param.formatColumns = this._formatColumns.bind(this);
			param.formatData = this._formatData.bind(this);
			param.tableOptions = {
				forbidSernialnumber: true,
				noCancelChecked: true,
				searchDefaultKeyword: this._searchDefault || '',
				// checkIntercept: this.checkIntercept.bind(this),
				hideSizeConfig: true,
				hideTdLineConfig: true,

				// showMoreBtn: false,
				settingClassName: 'bom_style2_settingClass',
			};
			let h = this.$('.t-wrap').height();
			param.el.height(h);
			param.formatTableOption = (opt) => {
				let o = {
					height: h - 53 - 76,
					showOutFilter: false,
					ignoreBatchOperate: true, // 忽略统计区域
					scrollLoad: false,
					resetFixDomPos: true,

				};
				return Object.assign(opt, o)
			}

			param.trclickHandle = this.trclickHandle.bind(this);
			// param.beforeRequest = this.beforeRequest.bind(this);
			// param.extendColumnsParam = {
			// 	layout_agent_type: "mobile",
			// }
		},

		addEvents() {
			$('body').delegate('.cpq_style2_container_right_version', 'click', (e) => {
				if($(e.currentTarget).hasClass('cpq_style2_versionDisabled')) return;
				this.changeVersion();
			});

			$('body').delegate('.cpq_style2_container_right_detail', 'click', (e) => {
				if (!this.currentRow) {
					return;
				}
				this._showDetail(this.currentRow._id, this.options.apiname);
			});

			$('body').delegate('.cpq_style2_attrFilter', 'click', (e) => {
				this._initAttributeFilter();

			});

			$('body').delegate('.cpq_style2_container_right_productInfoBtn_wrap', 'click', (e) => {
				// 切换展开收起
				let btn = $(e.currentTarget).find('.cpq_style2_container_right_productInfoBtn');
				if(btn.hasClass('fx-icon-list-collapse')){
					btn.removeClass('fx-icon-list-collapse');
					btn.addClass('fx-icon-list-expand');
					this.showProductInfo();
				}else{
					btn.removeClass('fx-icon-list-expand');
					btn.addClass('fx-icon-list-collapse');
					this.hideProductInfo();
				}
			});
		},

		initDraggableContainer(el, maxWidth) {
			this.dragContainer = new DraggableContainer({
				el,
				showCloseBtn: true,
				id: 'bom_module2_left',
				maxWidth,
				width: this.options.defaultWidth_left,
			});
			this.dragContainer.on('toggle', _.debounce(e => {
				this.table.resize();
			}, 200));
			this.dragContainer.render();
		},

		// 更改列展示
		_formatColumns(columns) {
			columns.forEach((item, index) => {
				item.fixed = false;
				item.showLookupText = true;
				if (item.api_name === this._showTableField) {
					this._searchDefault = item.api_name;
					item.render = (val, item, row) => {
						let q = row._selfQuantity ? '&times; ' + row._selfQuantity : '';
						let bomIcon = CRM.util.isBom(row).isPackage ? '<span class="fx-icon-f-app2 cpq_style2_bomIcon"></span>' : '';
						return bomIcon + `<span>${val}</span><span class="cpq_style2_tableRender " data-id="${row._id}">${q}</span>`
					};
					item.isHidden = false;
				}else{
					item.isHidden = true;
				}
			});
			return columns;
		},

		// 展示行数量
		setRowQuantity(data, val) {
			if(!data) return;
			data._selfQuantity = val;
			let el = this.table.$el.find('.cpq_style2_tableRender[data-id=' + data._id + ']');
			if (val) {
				el.html('&times; ' + val);
			} else {
                el.html('');
            }
		},

		getDataId(data) {
			return util.isBom(data).productId;
		},

		// 行点击；如果是已选行，切换并刷新右侧区域
		trclickHandle(row, $tr, $target, $trs) {
			if (this.currentRow && this.currentRow._id === row._id) return;
			let tr = this.table.$el.find('.fix-start-b').find('.tr[data-id=' + row._id + ']');
			let checkbox = tr.find('.checkbox-item');
			if (!checkbox.hasClass('tb-checkbox-selected')) return;
			this.fetchMobileProductColumns().then(async () => {
				this._clickTr = $tr;
				// if(this.currentRow && this.currentRow._id !== row._id){
				// 	let r1 = await this.checkBomAndAttrValue();
				// 	if(!r1) return;
				// }
				this.initRowAttr(row);
				this.refreshRight(row);
				this.renderProductInfo(row);
			});
		},

		// 初始化行的属性和已选属性
		initRowAttr(row){
			util.setDefaultAttr(row);
			let rememberData = this.table.getRemberData();
			if(rememberData?.length){
				let f = rememberData.find(item => item._id === row._id);
				if(!f) return;
				row.selectedAttr = f.selectedAttr;
				row.nsAttr = f.nsAttr;
			}
		},

		// 勾选行事件
		afterCheckboxClick(status, $target, table) {
            this.clearCurrentRow();
			let index = $target.parents('.tr').data("index");
			let data = table.getCurData().data[index];
			this.afterCheckboxClick_before(data, status);
			if (!status) return;
			if (this.currentRow && this.currentRow._id === data._id) return;
			this.setRowQuantity(data, data._selfQuantity);
			util.setDefaultAttr(data);
			this.refreshRight(data);
			this.afterCheckboxClick_after(data, status);
			this.fetchMobileProductColumns().then(() => {
				this.renderProductInfo(data);
			});
		},

		// 刷新右侧区域
		refreshRight(data){
			this.$el.find('.crm_style2_bomAttr_noData').hide();
			this.$el.find('.crm_style2_bomAttr_noData_productInfo').hide();
			this.table.$el.find('.tr-selected').removeClass('tr-current');
			this.table.$el.find('.tr[data-id=' + data._id + ']').addClass('tr-current');
			this.currentRow = data;
			if (util.isBom(data).isPackage) {
				this.$el.find('.dialog_selectbom_attrRange').hide();
				this.$el.find('.dialog_selectbom_attrConstraint').hide();
				let id = this.getDataId(data);
				let bomcore = this._cacheBomCore[id];
				if(bomcore){
					this._bomCoreData = bomcore;
					this.renderBom();
				}else{
					this.renderBomCoreObj(id, '');
				}
			} else {
                this.$el.find('.cpq_style2_container_right_version').hide();
				this.showAttrRange(data);
			}
            this.refreshHeader(data);
			this.$el.find('.cpq_style2_quantityBox').show();
			this.resetQuantity(data._selfQuantity)
		},

		addAttrConstId(data){
			if(data.product_id__ro){
				data.attribute_constraint_id = data.product_id__ro.attribute_constraint_id;
			}
		},

		showAttrRange(data){
			this.addAttrConstId(data);
			this.updatePrice(data);
			if(data.attribute_constraint_id){		// 属性级联
				this.$el.find('.cpq_style2_container_right_table').hide();
				// this.$el.find('.dialog_selectbom_attrRange').hide();
				this.initConstraint(data);
			}else{									// 属性范围
				this.$el.find('.cpq_style2_container_right_table').hide();
				this.$el.find('.dialog_selectbom_attrConstraint').hide();
				this.initAttrRange(data);
			}
		},

		// 勾选时，设置行数量展示，初始化为 1
		afterCheckboxClick_before(row, status) {
			if (!row._selfQuantity || !status) row._selfQuantity = this._getQuantityDefValue();
			if (status) this.setRowQuantity(row, row._selfQuantity);

			if (!status) {
				let pid = this.dialog.element.find('.cpq_style2_container_right_title').attr('data-id');
				if (this.getProductId(row) === pid) {
					this.clearRight();
				}
				this.removeError(this.getProductId(row));
				this.currentRow = null;
				this.updateTotalPrice();
			}
		},

		clearRight() {
			this.refreshHeader();
			this.showNoDataMsg(true);
			this.$el.find('.dialog_selectbom_attrRange').hide();
			this.$el.find('.dialog_selectbom_attrConstraint').hide();
			this.$el.find('.cpq_style2_container_right_table').hide();
			this.$el.find('.cpq_style2_container_right_rootAttr').empty();
			this.$el.find('.cpq_style2_container_right_version').hide();
			this.$el.find('.cpq_style2_price').html(0);
			this.$el.find('.cpq_style2_quantityBox').hide();
			this.table.$el.find('.tr-selected').removeClass('tr-current');
			this.clearSelectedAttrValue();
			this.clearProductInfo();
		},

		//
		afterCheckboxClick_after(data) {
			// this.resetQuantity(data._selfQuantity);
		},

		renderBomCoreObj(id, from) {
			let me = this;
			this._extendRootData(this.checkedData[id]?.subBomData?.newRootData, this.currentRow);
			// 打开配置，传报价器参数
			const quoterParam = this.getQuoterParam();
			// 打开配置
			require.async('crm-modules/buscomponents/pickselfobject_bomcore/pickselfobject_bomcore', async (bomCore) => {
				let config = {
					product_id: id,
					currentRow: me.currentRow,
					apiname: me.apiname,
					selectBomParams: {
						...quoterParam
					},
					bomCoreParams: {
						dataId: this._bomCoreData?._id,
						...quoterParam
					},
					noButton: true,
					notShowBom: true,
					noRender: true,
					from,
					...me.options,
					dialogEnter: me.bomCoreEnter.bind(me, id),
					dialogCancel: me.bomCoreCancel.bind(me, from),
					bomCoreGetVersionAfter: me.bomCoreGetVersionAfter.bind(me),
					dialogOptions:{
						closeTpl: '',
					}
				};
				me.pickBomCore = await bomCore(config);
				if (me.pickBomCore) {
					me.pickBomCore.on('afterCheckboxClick', me.pickSelect.bind(me));
				}
			});
		},

		bomCoreCancel(from) {
			if(from === 'change') return;
			let id = this.currentRow._id;
			this.currentRow = null;
			this._setUnCheckedRow(id);
			this.clearRight();
		},

		pickSelect(data) {
			this.cacheBomCore(data);
		},

		// 单个版本
		bomCoreGetVersionAfter(data, count) {
			this.cacheBomCore(data);
			this._onlyOneVersion = count === 1;
			this.renderBom();
		},

		cacheBomCore(data) {
			this._bomCoreData = data;
			let pId = this.getProductId(this.currentRow);
			this._cacheBomCore[pId] = data;
		},

		renderBom() {
			let bomCoreData = this._bomCoreData;
			this.updateVersion(bomCoreData);
			let param = this.__parseParam(bomCoreData, this.currentRow, this.options);
			this.$el.find('.cpq_style2_container_right_table').show();
			this.$el.find('.cpq_style2_container_right_version').show();
			this.renderSelectBom(bomCoreData, param);
		},

		bomCoreEnter(productId, checkedBomCoreList) {
			this.renderBom();
		},

		__parseParam(data, p_currentRow, p_options) {
			let bomEl = this.$twrap.find('.cpq_style2_container_right_table');
			let rootAttrElement = this.$twrap.find('.cpq_style2_container_right_rootAttr');
			let totalMoneyElement = this.dialog.element.find('.dialog-btns');
			let ruleElement = this.dialog.element.find('.cpq_style2_container_right_header');
			let bomParam = {
				selfElement: bomEl,
				rootAttrElement,
				notShowRootAttr: true,
				totalMoneyElement,
				ruleElement,
				hideChildren: this.notShowBom(),
			};
			let {product_id, core_version, category, _id: bomCoreId, name: bomCoreName} = data;
			// let { p_currentRow, p_options } = this.options;
			const {
				source_api_name, master_data, object_data, pluginContext, apiname, accountId,
				isHideTotalMoney, __price_decimal_places, fieldMapping, isSupportPeriod, extendBomParam
			} = p_options;
			const {display_name, name, product_id__r, pricebook_id} = p_currentRow;
			let _fieldMapping = Object.assign({}, {
				form_account_id: 'account_id',
				form_partner_id: 'partner_id',
				form_mc_currency: 'mc_currency',
			}, fieldMapping || {});
			const masterData = master_data || object_data;
			const pName = CRM._cache.openPriceList ? display_name || product_id__r || name || '' : display_name || name || product_id__r || '';
			const details = pluginContext?.dataGetter.getDetails();
			const priceBookDesc = pluginContext?.dataGetter.getDescribe()?.fields?.price_book_id;

			const quoterParam = this.getQuoterParam();
			return {
				// dialogEnterHandle,
				title: `${$t('配置产品')}：${pName}（${bomCoreName}）`,
				bom_core_id: bomCoreId,
				bom_type: category,
				bom_version: core_version,
				rootId: product_id,
				apinameFrom: apiname,
				isFrom: 'pickself',
				accountId: masterData[_fieldMapping.form_account_id] || accountId,
				partnerId: masterData[_fieldMapping.form_partner_id] || '',
				mcCurrency: masterData[_fieldMapping.form_mc_currency] || '',
				object_data: object_data,
				decimal_places: __price_decimal_places,
				priceBookId: pricebook_id || '',
				attribute: this.getAttribute(p_currentRow),
				masterData: p_options.pluginContext?.dataGetter.getMasterData() || p_options.master_data,
				details: CRM.util.parsePriceBookDataRangeDetails(details, priceBookDesc, p_options.source_api_name),
				source_api_name,
				// onSelect: dialogEnterHandle,
				...bomParam,
				...extendBomParam,
				...quoterParam,
				changeHeightHook: this.changeHeightHook.bind(this),
				// initDataEndHook: this.initDataEndHook.bind(this),
				totalMoneyChangeHook: this.totalMoneyChangeHook.bind(this),
				styleType: 3,
				isHideTotalMoney: true,

				extendData: this.checkedData[product_id]?.data,
				extendRootData: this.checkedData[product_id]?.newRootData || this.currentRow,
				isSupportPeriod,
			}
		},

		// 获取属性信息
		getAttribute(data) {
			let res = {};
			if (data.selectedAttr) {
				_.each(data.selectedAttr, (val, key) => {
					res[key] = val.value_ids[0].id;
				})
			} else if (data.attribute) {
				_.each(data.attribute, item => {
					let key = item.id;
					let d = _.find(item.attribute_values, a => a.is_default == '1');
					if (d) res[key] = d.id;
				})
			}
			return res;
		},

		renderSelectBom: _.debounce(function (bomcoreData, params) {
			let _this = this;
			this.selectBom && this.selectBom.destroy();
			this.selectBom = null;

			this.getSelectBomComponent(bomcoreData).then((SelectBOM) => {

				_this.selectBom = new SelectBOM({
					...params,
				});
				_this.selectBom.on('rootAttrChange', _this.rootAttrChangeHook.bind(this));
				_this.selectBom.on('rootNsAttrChange', _this.rootNsAttrChangeHook.bind(this));
			})
		}, 200),

		// 根节点属性变化勾子
		rootAttrChangeHook(attrInfo){
			this.currentRow.selectedAttr = attrInfo;
		},

		// 根节点属性变化勾子
		rootNsAttrChangeHook(attrInfo){
			this.currentRow.nsAttr = attrInfo;
		},

		getSelectBomComponent(data) {
			return new Promise((resolve, reject) => {
				require.async('crm-modules/components/dialog_selectbom/dialog_selectbom', (SelectBOM) => {
					let gsc = this.options.getSelectBomComponent;
					if (gsc) {
						Promise.resolve(gsc(data, SelectBOM)).then((comp) => {
							resolve(comp);
						})
					} else {
						resolve(SelectBOM);
					}
				});
			})
		},

		// 改内容区高度
		changeHeightHook(height) {
			let attrHeight = this.dialog.element.find('.cpq_style2_container_right_rootAttr').height();
			let h = this.$twrap.height() - attrHeight - 51;
			this.dialog.element.find('.cpq_style2_container_right_main').height(h);
			return h;
		},

		// 更新表头展示
		refreshHeader(data) {
			let title = this.dialog.element.find('.cpq_style2_container_right_title');
			let detail = this.dialog.element.find('.cpq_style2_container_right_detail');
			if (!data) {
				title.hide();
				detail.hide();
				return;
			}
			let productName = data.product_id__r || data.name;
			let pid = this.getProductId(data);
			title.attr('data-id', pid);
			title.html(productName);
			title.show();
			this.dialog.element.find('.cpq_style2_container_right_detail').show();
		},

		// 更新版本
		updateVersion(data) {
			let version = this.dialog.element.find('.cpq_style2_container_right_version');
			let versionDisabled = 'cpq_style2_versionDisabled';
			version.removeClass(versionDisabled);
			if(this._onlyOneVersion) version.addClass(versionDisabled);
			this._onlyOneVersion = false;
			version.html($t('切换') + $t('版本') + '：' + data.core_version + ` <span class="fx-icon-switch"></span>`);
		},

		// 格式化数据 to 属性范围组件
		getDefAttrValueForRange(data) {
			let res = {};
			if (data.selectedAttr) {
				_.each(data.selectedAttr, (val, key) => {
					res[key] = {
						value: val.value_ids.map(c => c.id)
					}
				});
			}
			if (data.nsAttr) {
				_.each(data.nsAttr, (val, key) => {
					res[key] = {
						value: val.value
					};
				});
			}
			return res;
		},

		getAttrRange(data) {
			let allAttr = [];
			if (data.attribute) {
				data.attribute.forEach(c => {
					c.isStandardAttribute = true;
				});
				allAttr = allAttr.concat(data.attribute)
			}
			if (data.nonstandardAttribute) {
				let nsAttr = data.nonstandardAttribute.map(item => {
					if (item.containerDocument) item.containerDocument.id = item.containerDocument._id;
					return item.containerDocument || item;
				});
				allAttr = allAttr.concat(nsAttr)
			}
			if (data.attr_range) {
				data.attr_range = data.attr_range.filter(item => {
					item.isStandardAttribute = item.apiName === 'AttributeObj';
					return true;
				});
				return data.attr_range;
			} else {
				return allAttr;
			}
		},

		showNoDataMsg(noData){
			let el = this.$el.find('.crm_style2_bomAttr_noData');
			// 产品未关联属性，如需配置可在【产品详情】-【配置属性】中设置
			let msg = noData ? $t('请先选择产品') : $t('sfa.bom.style2.noAttr');
			el.html(msg);
			el.show();
			if(noData){
				this.$el.find('.crm_style2_bomAttr_noData_productInfo').show();
			}
		},

		// 过滤报价器已有的属性
		filterAttrByQuoter(attr) {
			let quoter = this.PriceQuoter;
			if (!quoter) return attr;
			let attrMap = quoter.getAllAttrMap();
			return attr.filter(item => {
				return !attrMap[item.id];
			})
		},

		// 属性范围
		initAttrRange(data) {
			let me = this;
			if (!data.attribute && !data.nonstandardAttribute?.length) {
				if(!data.attribute_constraint_id) this.showNoDataMsg();
				this.$el.find('.dialog_selectbom_attrRange').hide();
				return;
			}
			let el = this.$el.find('.cpq_style2_container_right_attrRange');
			this.selectBom && this.selectBom.destroy();
			let attr = this.getAttrRange(data);
			attr = this.filterAttrByQuoter(attr);
			if(!attr.length) {
				if(!data.attribute_constraint_id) this.showNoDataMsg();
				this.$el.find('.dialog_selectbom_attrRange').hide();
				return;
			}
			let groupList = util.sortAttrByGroup(attr);
			console.log('groupList', groupList);
			let defValue = this.getDefAttrValueForRange(data);

			// 处理属性展示勾子，设置隐藏某个属性，添加字段 isHide: true
			this.options.changeAttrHook && this.options.changeAttrHook(groupList);

			if (this.AttrRange) {
				this.AttrRange.initData(groupList, defValue);
				this.$el.find('.dialog_selectbom_attrRange').show();
				return
			}
			this.$el.find('.dialog_selectbom_attrRange').show();

			require.async('vcrm/sdk', (sdk) => {
				sdk.getComponent('attributeRangeRender').then((AttributeRangeRender) => {
					AttributeRangeRender = AttributeRangeRender.default;
					let AttrRange = new Vue({
						el: el[0],
						template: `<div class="dialog_selectbom_attrRange">
								<div class="attrRange_back">
										<div class="dialog_selectbom_attrTitle">{{title}}</div>
										<AttributeRangeRender
											:groupList="groupList"
											:value="defValue"
											@change="change"
										></AttributeRangeRender>
								</div>
                          </div>`,
						components: {
							AttributeRangeRender
						},
						data() {
							return {
								groupList,
								defValue,
								title: $t('请选择属性')

								// groupList:[{
								// 	groupId:1,
								// 	groupNo:1,
								// 	groupName:'未分组',
								// 	options: [{
								// 		id: "65d4635dedb9f50001015881",
								// 		name: "颜色" ,
								// 		isStandardAttribute: true,
								// 		children: [
								// 			{
								// 				"id": "65d4635dedb9f50001015889",
								// 				"code": "1-8",
								// 				"name": "橙色",
								// 				"is_default": "0"
								// 			}
								// 		]}]
								// }],
							}
						},
						mounted() {
						},
						methods: {
							change(val, attrInfo) {
								me.attrRangeChange(val, attrInfo)
							}
						}
					});
					me.AttrRange = AttrRange.$children[0];
				});
			});
		},

		// 更新已选属性值
		attrRangeChange(val, attrInfo) {
			console.log(attrInfo);
			if (attrInfo.isStandardAttribute) {
				let f = this.currentRow.attribute?.find(a => a.id === attrInfo.id);
				if(!f) return;
				this.currentRow.selectedAttr[attrInfo.id] = {
					name: attrInfo.name,
					value_ids: attrInfo.value,
				};
				this.updatePrice(this.currentRow);
			} else {
				let f = this.currentRow.nonstandardAttribute?.find(a => a.id === attrInfo.id);
				if(!f) return;
				this.currentRow.nsAttr[attrInfo.id] = {
					name: attrInfo.name,
					value: attrInfo.value,
				};
			}
			this.renderSelectedAttrValue(this.currentRow);
			// 切换属性勾子
			this.options.changeAttrAfterHook && this.options.changeAttrAfterHook({
				data: this.currentRow,
			});
		},

		getRealPriceParam(data) {
			let attrs = {};
			if (data.selectedAttr) {
				for (let key in data.selectedAttr) {
					attrs[key] = data.selectedAttr[key].value_ids[0].id
				}
			}
			return [{
				"productId": data.product_id || data._id,
				"priceBookId": data.pricebook_id || '',
				"attrMap": attrs,
				"rowId": data._id,
				"amount": data._selfQuantity ?? 1,

			}];
		},

		// 初始化属性级联组件
		async initConstraint(row){
			util.showLoading_tip();
			let me = this;
			this.selectBom && this.selectBom.destroy();
			let data = await CRM.util.queryAttributeConstraintById({id: row.attribute_constraint_id});
			console.log(data)
			if(this.PriceQuoter){
				await me.PriceQuoter.init(data?.dataList);
				this.$el.find('.dialog_selectbom_attrConstraint').show();
				util.hideLoading_tip();
				me.initAttrRange(row);
				return
			}
			let el = this.$el.find('.cpq_style2_container_right_attrContract');
			let helpMsg = $t('sfa.bom.style3.helpMsg1'); // 设置属性及联方案场景下，属性选择规则：每次选择属性时，仅更新本次选择的属性值，其它属性自动还原为默认值；选配属性后，如需要修改子件属性，建议在选完所有属性后，再在子件上修改，否则会被覆盖！
			require.async('vcrm/sdk', (sdk) => {
				sdk.getComponent('priceQuoterRender').then( async (PriceQuoterRender) => {
					PriceQuoterRender = PriceQuoterRender.default;
					const PriceQuoter = new Vue({
						el: el[0],
						template: `<div class="dialog_selectbom_attrConstraint">
								<div class="attrConstraint_back">
										<div class="dialog_selectbom_attrTitle">{{title}} <em class="crm-ui-help crm-ui-title" data-title="${helpMsg}" data-pos="bottom">?</em></div>
										<PriceQuoterRender
											:showSubmitBtn="showSubmitBtn"
											@changeAfter="change"
											@submit="submit"
											sortByGroup="true"
										></PriceQuoterRender>
								</div>
                          </div>`,
						components: {
							PriceQuoterRender
						},
						data() {
							return {
								showSubmitBtn: false,
								title: $t('bom.module3.msg1'), // 属性级联
							}
						},
						mounted() {
						},
						methods: {
							change(val, attrInfo, oldVal) {
								console.log(val)
								if(Array.isArray(val) && !val.length) { 		// 反选属性
									return;
								}
								me.attrConstraintChangeAfter(attrInfo);
							},
							submit(val, attrInfo) {
								console.log(val)
							},
						}
					});
					me.PriceQuoter = PriceQuoter.$children[0];
					await me.PriceQuoter.init(data?.dataList);
					util.hideLoading_tip();
					me.initAttrRange(row);
				});
			});
		},

		// 获取属性级联结果
		_getQuoterRes(){
			let res = this.PriceQuoter.submitSync();
			if(!res) return;
			let result = {};
			let {_attrParam, _nsAttrParam} = res;
			if(_attrParam.length){
				result.attrRes = _attrParam.map(attr => {
					let r = {
						"attrId": attr.attribute_id,
						"name": attr.label,
						"attrValue": {
							id: attr.selected_values[0]._id,
							name: attr.selected_values[0].name,
						},
						selectedAttrValue: attr.field_values,
					};
					return r;
				});
			}
			if(_nsAttrParam.length){
				result.nonAttrRes = _nsAttrParam.map(attr => {
					return {
						"attrId": attr.attribute_id,
						"name": attr.label,
						"attrValue": {
							name: attr.label,
							value: attr.field_values[0],
						}
					}
				});
			}
			return result;
		},

		/**
		 * @desc 属性级联 change
		 * @param attrInfo 更改属性信息
		 * @returns {Promise<void>}
		 */
		async attrConstraintChangeAfter(attrInfo ){
			let res = this._getQuoterRes();
			if(!res) return;
			let row = this.currentRow;
			let {attrRes, nonAttrRes} = res;
			if(attrRes){
				attrRes.forEach((item) => {
					let f = row.attribute?.find(a => a.id === item.attrId);
					if(!f) return;
					let key = item.attrId;
					let val = item.attrValue;
					row.selectedAttr[key] = {
						name: item.name,
						value_ids: [val]
					};
				});
				this.updatePrice(row);
			}
			if(nonAttrRes){
				nonAttrRes.forEach(item => {
					let f = row.nonstandardAttribute?.find(a => a.id === item.attrId);
					if(!f) return;
					let key = item.attrId;
					let val = item.attrValue.value;
					row.nsAttr[key] = {
						name: item.name,
						value: val,
					};
				});
			}
			this.renderSelectedAttrValue(row);
			// 切换属性勾子
			this.options.changeAttrConstraintHook && this.options.changeAttrConstraintHook({
				data: row,
			});
		},

		showLoading(){
			let loading = `<div class="cpq_style2_loading"><div class="el-icon-loading"></div></div>`;
			let p = this.$el.find('.cpq_style2_price');
			let tp = this.$el.find('.cpq_style2_totalPrice');
			p.html('');
			tp.html('');
			p.append(loading);
			tp.append(loading);
		},

		hideLoading(){
			this.$el.find('.cpq_style2_loading').remove();
		},

		// 取价
		async updatePrice(data) {
			if(this.options.hideMoney) return;
			this.showLoading();
			this.getRealPrice(data).then(([newPrice, mcCurrency, result]) => {
				// this._mcCurrency = mcCurrency;
				this.updatePriceElement(newPrice);
				this.hideLoading();
            })
		},
        getRealPrice(data) {
            return new Promise((resolve, reject) => {
                let param = this.getRealPriceParam(data);
                this._getRealPrice(param, (res) => {
                    let newPrice = res[0].selling_price;
                    resolve([newPrice, res[0].mc_currency, res[0]]);
				})
			});
        },
		totalMoneyChangeHook(price) {
			this.updatePriceElement(price);
		},

		// 更新价格展示
		updatePriceElement(price) {
			if (this.currentRow) this.currentRow._selfPrice = price;
			price = CRM.util.formatMoneyForCurrency(price, this._mcCurrency);
			this.$el.find('.cpq_style2_price').html(price);
			this.updateTotalPrice();
		},

		// 更新总金额
		updateTotalPrice() {
			let allData = this.table.getRemberData();
			let price = this._calculateTotalPrice(allData);
			console.log(price);
			price = CRM.util.formatMoneyForCurrency(price, this._mcCurrency);
			this.$el.find('.cpq_style2_totalPrice').html(price);
			this.updateQuoterTrialComp();
		},
        _calculateTotalPrice(allData) {
            let price = 0;
            allData.forEach(item => {
                price += util.multiplicational(item._selfQuantity || 1, item._selfPrice || 0);
            });
            return price;
        },
		_getRealPrice: _.debounce(function (fullData = [], cb) {
			let {fieldMapping} = this.options;
			let _fieldMapping = Object.assign({}, {
				form_account_id: 'account_id',
				form_partner_id: 'partner_id',
				form_mc_currency: 'mc_currency',
			}, fieldMapping || {});
			let {form_account_id, form_partner_id} = _fieldMapping;
			const masterData = this.options.master_data || this.options.object_data;
			if (!fullData.length) return;
			CRM.util.replacePriceForPriceBook({
				accountId: masterData[form_account_id],
				partnerId: masterData[form_partner_id],
				fullProductList: fullData,
                mcCurrency: masterData.mc_currency,
				object_data: masterData || {},
				object_api_name: masterData?.object_describe_api_name,
			}).then(res => {
				if (res.applicablePriceSystem && res.newRst && res.newRst.length >= 1) {
					cb && cb(res.newRst)
				}
			}, err => {
				util.hideLoading_tip();
			});
		}, 200),

		// 获取数量默认值
		_getQuantityDefValue(){
			if(this.options.quantityDes?.default_value){
				if(Number(this.options.quantityDes.default_value)){
					return this.options.quantityDes.default_value
				}
			}
			return 1;
		},

		// 渲染数量组件；
		initQuantity: function () {
			if(this.options.hideQuantity) return;
			let me = this;
			this.cpqQuantity && this.cpqQuantity.destroy && this.cpqQuantity.destroy();
			let defVal = this._getQuantityDefValue();
			let disabled = this.options?.quantityLayout?.is_readonly;
			this.cpqQuantity = FxUI.create({
				wrapper: '.cpq_style2_quantity',
				template: `<fx-input-number v-model="num" size="micro"  :disabled="disabled" @change="handleChange" :min="1"  label=""></fx-input-number>`,
				data() {
					return {
						num: defVal,
						disabled,
					}
				},
				methods: {
					handleChange(val, node, tree) {
						me.changeQuantity(val);
					},
				}
			})
		},

		// 开了阶梯价
		isOpenTieredPrice(){
			return CRM.util.getConfigStatusByKey('price_book_product_tiered_price') === '1';
		},

		// 改数量
		changeQuantity(val) {
			console.log(val);
			if (!this.currentRow) return;
			let id = this.currentRow._id;
			this.setRowQuantity(this.currentRow, val || 1);
			if (val) {
				this._setCheckedRow(id);
			} else {
				this._setUnCheckedRow(id);
				this.currentRow = null;
				this.clearRight();
			}
			if(this.currentRow && this.isOpenTieredPrice() && !util.isBom(this.currentRow).isPackage){
				this.updatePrice(this.currentRow);
			}else{
				this.updateTotalPrice();
			}
		},

		// 重置数量，默认为 1
		resetQuantity(num) {
			if(this.cpqQuantity){
				this.cpqQuantity._data.num = num || 1;
			}
		},

		// 切换版本
		changeVersion() {
			console.log('version')
			let id = this.getDataId(this.currentRow);
			this.renderBomCoreObj(id, 'change')
		},

		getProductId(data) {
			return data.product_id || data._id;
		},

		getProductName(data){
			const {display_name, name, product_id__r,} = data;
			return display_name || product_id__r || name || '';
		},

		addError(id){
			this._cacheError[id] = 1;
		},

		removeError(id){
			delete this._cacheError[id];
		},

		// 校验普通产品属性和非标属性是否都有值；
		checkAttrHasValue(row){
			if(!row) return true;
			let msg = '';
			let r1 = util.validAttribute(row);
			let r2 = util.validNsAttr(row);
			if(!r1){
				msg += this.getProductName(row) + "：" + $t('属性值不能为空') + '<br/>';
			}
			if(!r2){
				msg += this.getProductName(row) + "：" + $t('非标属性值不能为空');
			}
			if(msg) util.showError_fx(msg, 3000);
			return r1 && r2;
		},

		// 拦截勾选，校验 bom 规则
		async checkIntercept(target, cb, isChecked) {
			if (isChecked) {
				let r1 = await this.checkBomAndAttrValue();
				if (!r1) {
					this.addError(this.getProductId(this.currentRow));
					// 回滚点击行背景色
					if(this._clickTr) {
					 	this._clickTr.removeClass('tr-selected');
					 	let pid = this._clickTr.attr('data-id');
						let fixedTr = this.table.$el.find('.fix-start-b').find('.tr[data-id=' + pid + ']');
						fixedTr.removeClass('tr-selected');
					}
					return;
				}
			}
			cb(true);
		},

		// 校验 bom 规则，校验普通产品属性 非标属性；
		async checkBomAndAttrValue(){
			let r1 = await this.updateBomInfo();
			if(!r1) return false;
			let r2 = this.checkAttrHasValue(this.currentRow);
			return r2;
		},

		// 更新挂载的 bom 信息；
		async updateBomInfo() {
			if (this.currentRow && this.selectBom && util.isBom(this.currentRow).isPackage) {
				let subBomData = await this.selectBom.getResult();
				if (!subBomData) return false;
				let productId = this.getProductId(this.currentRow);
				this.checkedData[productId] = subBomData;
				this.updatePricebookId(subBomData, this.currentRow); // 可能切换了价目表，更新当前产品的价目表id等信息
				this.updateRootAttr(subBomData.newRootData || subBomData.rootData, this.currentRow); // 更新产品的 属性 和 非标属性
				this.supplementAttrToData(this.currentRow);// 属性产品，补全默认选中属性值
				this.addChildrenToCurrentRow(subBomData, this.currentRow, this._bomCoreData);
			}
			return true
		},

		addChildrenToCurrentRow(subBomData, currentRow, bomCoreData) {
			let {_id, name, category, sale_strategy} = bomCoreData;
			currentRow.core_id = _id;
			currentRow.core_id__r = name;
			currentRow.node_bom_core_type = category;
			currentRow.sale_strategy = sale_strategy;
			currentRow._updateRootPrice = true;
			if(subBomData){
				let {totalMoney, rootDataBomId, data} = subBomData;
				currentRow.newestPrice = totalMoney;
				currentRow.bom_id = rootDataBomId;
				currentRow.children = data;
			}
		},

        // 当前选中产品 立即下单
        async confirm(e) {
            var me = this;
            // 开启cpq || 开启固定搭配，且不是退货单对象，需要补齐产品包的子产品
            if (
                (CRM._cache.fixedCollocationOpenStatus || CRM._cache.cpqStatus) &&
                me.options.source_api_name !== "ReturnedGoodsInvoiceProductObj"
            ) {
                let r1 = await this.updateBomInfo();
                if(!r1) return;
            }
            let value = await this.validData(this.getCurrentData());
            if (!value) return;
            me.parseValue(value, function (res) {
				me.handelConfirm(res, e)
			});
        },

        getCurrentData() {
            var me = this;
            var value = me.table.getValue(true);
            if (!value) {
                util.alert($t("请选择一条数据") + '!');
                return;
            }

            //属性值选择有错误
            if (value == "attributeError") {
                return;
            }
            value = Array.isArray(value) ? value : [value];
            return value;
        },
		async validData(value) {
            var me = this;
            if (!value) return;
			// 校验产品约束关系
			let r = await me.validSelectValueBySelf(value, me.table);
			if(!r) return;

			// pwc 插件自定义校验
			if (this.options.validResult) {
				let r1 = await me.options.validResult({data: value});
				if (r1 && !r1.result) return;
			}
            return value;
		},

		parseValue: function (data, cb) {
			let p_dataList = data;
			let isArr = true;
			let me = this;
			let bomList = [];
			let ids = [];
			let noBomCoreId = [];
			if (!_.isArray(data)) {
				isArr = false;
				p_dataList = [data]
			}
			_.each(p_dataList, function (proData, index) {
				let id = proData.product_id || proData._id;
				let isPackage = util.isBom(proData).isPackage;
				if (!proData.rowId) proData.rowId = util.uniqueCode();
				if(!proData.hasOwnProperty('newestPrice')) proData.newestPrice = proData.pricebook_sellingprice || proData.price;

				// 将子产品添加到对应的产品包 checkedData = {id: bomCheckedData}
				if (isPackage && !proData.children) {
					if (me.checkedData[id]) {
						let {data} = me.checkedData[id] || {};
						me.addChildrenToCurrentRow(data, proData, me._cacheBomCore[id]);
					} else {
						me.addChildrenToCurrentRow(null, proData, me._cacheBomCore[id]);
						bomList.push(proData);
						ids.push(id);
					}
				}
			});
			if (noBomCoreId.length && !this.options.noCoreVersion) {
				util.alert($t(`${noBomCoreId.join(',')}${$t('未选择BOM版本')}`));
				return;
			}
			let r1 = this.checkAttrHasValue(p_dataList);
			if(!r1) return;
			if (bomList.length && !this.options.noFetchBom) {
				// 批量查选中的bom产品默认选中项
				me.fetchAllBomList(ids, bomList, function () {
					let r = me.checkNsAttr(bomList);
					if (!r) return;
					// 不进配置页，需要校验子件属性是否都有值
					let r2 = util.validAttribute(bomList);
					if (!r2) {
						CRM.util.alert($t('子件') + $t("每个属性至少选择一个属性值"));
						return;
					}
					let r3 = me.checkBOMVersion(bomList);
					if (!r3) return;
					cb && cb(isArr ? p_dataList : p_dataList[0])
				});
				return
			}
			cb && cb(isArr ? p_dataList : p_dataList[0])
		},

        handelConfirm(res, e) {
            var me = this;
        	// 触发父组件监听的产品组件选中事件，返回选中项
        	me.trigger('select', res, res);
        	if (me.submit) {
        		me.submit(res, me, e);
        		return;
        	}
        	me.destroy();
        },

		// 配置完，设置行勾选;
		_setCheckedRow: function (id) {
			// 选产品table
			let checked = this.table.getRemberData() || [];
			let findPb = _.find(checked, item => item._id === id);
			if (!findPb) {
				this.table.setCheckedRow('_id', [{'_id': id}]);
			}
		},

		// 配置完，设置行勾选;
		_setUnCheckedRow: function (id) {
			this.table.setUncheckedRow('_id', [{'_id': id}]);
		},

		// 不进配置页
		notShowBom(){
			// return false;
		 return CRM.util.isOpenNotShowBom() // 未灰不展示选配页面
			 || CRM._cache.fixedCollocationOpenStatus // 未开固定搭配开关
			 || this.options.p_options?.notShowBom;
		},

		/*
       * 初始化属性筛选组件
       */
		_initAttributeFilter() {
			const me = this;
			require.async('vcrm/sdk',function(sdk){
				sdk.getComponent('categoryAttrSetting').then((CategoryAttrSetting) => {
					CategoryAttrSetting = CategoryAttrSetting.default;
					me.openSelector(CategoryAttrSetting);
				})
			});
		},

		// 分类变化
		_categoryIsChange(){
			let carId = this.table._cateGoryData?.CategoryID || '';
			return this._carId !== carId;
		},

		// 属性筛选
		openSelector(CategoryAttrSetting) {
			let me = this;
			if(this.dialog_attrFilter && !this._categoryIsChange()){
				this.dialog_attrFilter.selectorDialogVisible = true;
				return;
			}
			this.dialog_attrFilter?.destroy();
			this.dialog_attrFilter = null;
			this.dialog_attrFilter = FxUI.create({
				template: `<fx-dialog
								:visible.sync="selectorDialogVisible"
								custom-class="cpq-style2-attrFilter"
								ref="dialog1_2"
								:append-to-body="true"
								:title="title"
								width="70%"
								max-height="500px"
							>
							<div class="">
								<CategoryAttrSetting
                                    :unFlod="true"
									ref="AttrFilter"
								></CategoryAttrSetting>	
							</div>
							<div slot="footer" class="dialog-footer">
								<fx-button size="small" type="primary" @click="clickFinishHandle"  >${$t('筛选')}</fx-button>
								<fx-button size="small" @click="cancelHandle" >${$t('取消')}</fx-button>
							</div>
							</fx-dialog>`,
				data() {
					return {
						title: $t('属性') + $t('筛选'),
						selectorDialogVisible: true,
					}
				},
				components: {
					CategoryAttrSetting
				},
				methods: {
					cancelHandle() {
						this.selectorDialogVisible = false;
					},
					clickFinishHandle() {
						this.selectorDialogVisible = false;
						let conditions = this.$refs.AttrFilter.getParam();
						console.log(conditions);
						me.table._attrConditions = conditions;
						me.table.setParam({}, true, true);
					}
				},
				mounted() {
					let carId = me.table._cateGoryData?.CategoryID || '';
					me._carId = carId;
					this.$refs.AttrFilter.setCategory(carId, me.table._cateGoryData, []);
				}
			});
		},

		/**
		 * 缓存筛选时，希望默认选中的属性属性值Map
		 * 只在属性值唯一时做特殊默认选中
		 * 支持场景：/来自报价器的筛选，缓存filters,否则清空
		 */
		cacheTargetAttr(oriParam) {
			this.curQuoterFilter = null;
			if (oriParam?.extraData?.is_from_quoter) {
				this.curQuoterFilter=CRM.util.getQuoterParam(oriParam.extraData);
			}
		},

		//添加属性筛选条件
		// parseParam: function (param) {
		// 	let oriParam = param,
		// 		sq = JSON.parse(oriParam.search_query_info),
		// 		condition = _.filter(sq.filters, function (item) {
		// 			return item.field_name.slice(0,20)!=="product_id.attribute"&&item.field_name.slice(0,9)!=="attribute";
		// 		});
		//
		// 	// this.cacheTargetAttr(oriParam);
		//
		// 	if (this._attrConditions && this._attrConditions.length >= 1) {
		// 		if (this.table.options.searchFiled && this.table.options.searchFiled.api_name == "product_id") {
		// 			this._attrConditions.forEach(a => {
		// 				if (a.field_name.slice(0, 10) !== "product_id") {
		// 					a.field_name = "product_id." + a.field_name;
		// 				}
		// 				a.is_master_field = true;
		// 			})
		// 		}
		// 		condition = condition.concat(this._attrConditions);
		// 	}
		//
		// 	oriParam.search_query_info = JSON.stringify(Object.assign(sq, {
		// 		filters: condition
		// 	}));
		// 	return oriParam
		// },

		// 表格请求数据前，改请求参数
		// beforeRequest(param){
		// 	if(this._categoryIsChange()){
		// 		this.table._attrConditions = null;
		// 	}
		// 	let p = this.parseParam(param);
		// 	if(this.options.beforeRequest_bom){
		// 		p = this.options.beforeRequest_bom(p);
		// 	}
		// 	return p
		// },

		// 显示已选产品信息
		showProductInfo(){
			// let id = this.getDataId(this.currentRow);
			this.$el.find('.cpq_style2_container_right_productInfo').show();
		},

		// 隐藏已选产品信息
		hideProductInfo(){
			this.$el.find('.cpq_style2_container_right_productInfo').hide();
			let btn = this.$el.find('.cpq_style2_container_right_productInfoBtn');
			btn.removeClass('fx-icon-list-expand');
			btn.addClass('fx-icon-list-collapse');
		},

		renderSelectedAttrValue(data){
			this.$el.find('.cpq_style2_container_right_productInfo_attribute_header').show();
			if (!data.attribute && !data.nonstandardAttribute?.length) {
				if(this.selectedAttrValue) this.selectedAttrValue.refresh(null, null);
				this.$el.find('.cpq_style2_container_right_productInfo_attribute_noData').show();
				return;
			}
			this.$el.find('.cpq_style2_container_right_productInfo_attribute_noData').hide();
			let el = this.$el.find('.cpq_style2_container_right_productInfo_attribute_content')[0];
			let attr = this.getAttrRange(data);
			let groupList = util.sortAttrByGroup(attr);
			console.log('groupList', groupList);

			let selectedAttrValue = this.getDefAttrValueForRange(data);
			if(this.selectedAttrValue){
				this.selectedAttrValue.refresh(groupList, selectedAttrValue);
				return;
			}
			this.selectedAttrValue && this.selectedAttrValue.destroy();

			this.selectedAttrValue = new SelectedAttr({
				groupList: groupList,
				selectedAttr: selectedAttrValue,
				el: el
			})
			this.selectedAttrValue.render();
		},

		clearSelectedAttrValue(){
			this.selectedAttrValue && this.selectedAttrValue.refresh(null, null);
			this.$el.find('.cpq_style2_container_right_productInfo_attribute_header').hide();
			this.$el.find('.cpq_style2_container_right_productInfo_attribute_noData').hide();
		},

		//高级公式试算
		async updateQuoterTrialComp(){
			const isQuoterEnabled = this.getQuoterConfig();
			if(!isQuoterEnabled){
				return;
			}
			if(!this.quoterTrial){
				const fields = this.options.pluginContext?.dataGetter?.getDescribe()?.fields||{};
				this.quoterTrial = new QuoterTrial({
					container: $('.cpq_style2_quoterTrial',this.$el),
					options: {
						detailApiName: this.options.source_api_name,
						fields: fields,
						masterData: this.options.master_data,
					},
					getCurData: this.getCurData.bind(this),
					getCheckedBomData: this.getCheckedBomData.bind(this)
				});
			}
			const result = await this.quoterTrial.fetchFormulaConfig();
			console.log('result', result);
		},

		getCurData: function() {
			return this.currentRow;
		},

		getCheckedBomData: function() {
			return this.selectBom && this.selectBom.treeTable?.getCheckedData();
		},

		getQuoterConfig:function(){
			if(this._isQuoterEnabled === undefined){
				const configStr = CRM.util.getConfigStatusByKey("is_open_quoter");
				this._isQuoterEnabled = configStr == '1';
			}
			return this._isQuoterEnabled;
		},

		destroy() {
			$('body').undelegate('.cpq_style2_container_right_version', 'click');
			$('body').undelegate('.cpq_style2_container_right_detail', 'click');
			$('body').undelegate('.cpq_style2_attrFilter', 'click');
			$('body').undelegate('.cpq_style2_container_right_productInfoBtn_wrap', 'click');
            this.ShoppingCart && this.ShoppingCart.destroy();
			this.dragContainer && this.dragContainer.destroy();
			this.pickBomCore && this.pickBomCore.destroy();
			this.AttrRange && this.AttrRange.$destroy();
			this.cpqQuantity && this.cpqQuantity.destroy();
			this.selectBom && this.selectBom.destroy();
			this.PriceBookSelect && this.PriceBookSelect.destroy();
			this.selectedAttrValue && this.selectedAttrValue.destroy();
			this.ProductInfo && this.ProductInfo.destroy();
			this.productInfoTip && this.productInfoTip.destroy();
			this.PriceBookSelect = this.selectBom = this.cpqQuantity = this.AttrRange = this.pickBomCore = this.dragContainer = this.selectedAttrValue = this.ProductInfo = this.ShoppingCart = this.productInfoTip = null;
			PickSelfObject.prototype.destroy.apply(this, arguments);
		}

	});

	module.exports = pickSelf_bom2;
});
