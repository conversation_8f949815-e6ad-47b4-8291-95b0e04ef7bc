/**
 *@desc 数据多语
 这个文件SFA在其他地方有使用，修改相关参数时确认下@zhongleiyun
 */
define(function (require, exports, module) {
    return {
        renderMultilineActionbar(benchmarker, callback) {
            const field = this.fieldAttr;

            // 没有启用多语配置
            if (!field.enable_multi_lang) {
                return;
            }

            const support_languages = field.support_languages;
            if (!support_languages || !support_languages.length) {
                return;
            }

            this._renderMultilineActionbar(benchmarker, support_languages);

            callback && callback();
        },

        disableMultilineActionbar() {
            this.$$multilineActionbar && (this.$$multilineActionbar.dDisable = true);
        },

        enableMultilineActionbar() {
            this.$$multilineActionbar && (this.$$multilineActionbar.dDisable = false);
        },

        validateMultilineActionbar() {
            const multilineActionbar = this.$$multilineActionbar;
            if (!multilineActionbar || !multilineActionbar.dVisiable) {
                return;
            }

            if (multilineActionbar.cRequire) {
                return $t('请填写默认值');
            }

            return;
        },

        getMultilangContainer() {
            return this.$el;
        },

        _renderMultilineActionbar($benchmarker, support_languages) {
            const me = this;
            const __r = me.apiname + '__lang';
            const width = me.$el.width();
            const multiLangValue = this.getData(__r) || {};
            const $container = $(
                '<div class="crm-action-field-multilang">'
            ).appendTo(me.getMultilangContainer());
            
            if ($benchmarker) {
                $benchmarker.css('padding-right', '34px');
            }

            const isAutoFanyi = CRM.util.getUserAttribute('paasAutoFanyi');
            
            me.$$multilineActionbar = FxUI.create({
                wrapper: $container[0],
                template: `
                <fx-popover
                    ref="popper"
                    placement="bottom-end"
                    :visible-arrow="false"
                    width="${width < 328 ? 328 : width}"
                    popper-class="crm-action-field-lmultilang__content"
                    trigger="manual"
                    v-model="dVisiable"
                >
                    <div
                        @click="handleCLick" 
                        slot="reference"
                        class="lang-btn"
                        data-apiname="${me.apiname}"
                        :title="[!dTransing ? $t('multilingual.configuration', null, '多语言配置') : $t('paas.crm.autofanyi')]"
                        :class="[dVisiable || cZn ? 'lang-btn__focus' : '', dDisable ? 'is-disabled' : '']"
                    >
                        <span :class="[dTransing ? 'el-icon-loading' : 'fx-icon-app46']"></span>
                    </div>
                    <div v-if="dVisiable">
                        <div class="s-tip">
                            <span class="fx-icon-question"></span>
                            <div>
                                <p>${$t('crm.ai.mulilang.tip')}</p>
                                <p v-if="dShowAiTrans">${$t('crm.ai.mulilang.tip2')}</p>
                            </div>
                        </div>
                        <div v-loading="dLoading">
                            <div class="lang-item">
                                <label class="lc2">${$t('默认值')}<span v-show="dShowAiTrans" @click="aiTranslate" class="fx-icon-AIgongju">${$t('uipaas.notice.translate')}</span></label>
                                <fx-input v-model="dValue" @blur="inputComplete" :maxlength="dMaxLength" :autosize="dAutoSize" placeholder="${$t('请输入')}" resize="none" type="textarea" class="f-fxui-textarea" />
                            </div>
                            <p v-if="cRequire"><span class="crm-ico-error" style="padding-left: 0px; color: rgb(245, 113, 95);">${$t('请填写默认值')}</span></p>
                            <p class="c7"></p>
                            <div :key="item.value" class="lang-item" v-for="item in dOptions">
                                <label>{{item.label}}</label>
                                <fx-input v-model="item.text" @blur="handleBlur" :autosize="dAutoSize" resize="none" type="textarea" placeholder="${$t('请输入')}" class="f-fxui-textarea" />
                            </div>
                        </div>
                    </div>
                </fx-popover>
              `,
                data() {
                    return {
                        dVisiable: false,
                        dEnter: false,
                        dValue: '',
                        dShowRequire: false,
                        dDisable: me.fieldAttr.is_readonly,
                        dAutoSize: {minRows: 1, maxRows: 4},
                        dMaxLength: me.fieldAttr.max_length || 500,
                        dLoading: false,
                        dOptions: _.map(support_languages, a => {
                            return {
                                label: a.label,
                                value: a.code,
                                text: multiLangValue[a.code] || ''
                            }
                        }),
                        dShowAiTrans: !CRM.util.isZsyGrayControl(),
                        dTransing: false
                    }
                },
                computed: {
                    cRequire() {
                        return this.cZn && !this.dValue;
                    },
                    cZn() {
                        return _.find(this.dOptions, a => a.text);
                    },
                },
                watch: {
                    dVisiable(n) {
                        if (!n) {
                            this.hideErrorIfNeeded();
                        } else {
                            this.dValue = me.getData() || '';
                        }
                    },

                    dTransing() {
                        me.model._autoFanyiing = this.dTransing;
                    }
                },
                mounted() {
                    $(document.body).on('click', this._bodyFn = (e) => {
                        if (!this.dVisiable || this.cRequire || this.dConfirm) return;
                        const $target = $(e.target);
                        const $wrapper = $target.closest('.j-comp-wrap');

                        const apiname = $wrapper.data('apiname');
                        if (apiname && apiname === me.apiname) {
                            return;
                        }
                        
                        if (!$target.closest('.crm-action-field-lang__result').length) {
                            this.dVisiable = false;
                        }
                    });

                    isAutoFanyi && this._renderAutoFanyi();
                },
                methods: {
                    bindChannelIfNeeded() {
                        if (me.$ipt && this.dVisiable && !this.__setupIptBlurHandler) {
                            this.__setupIptBlurHandler = () => {
                                if (this.dVisiable) {
                                    this.dValue = me.$ipt.val();
                                }
                            };
                            me.$ipt.on('blur', this.__setupIptBlurHandler);
                        }
                    },

                    //默认值输入完成
                    inputComplete() {
                        let preValue = me.getData();
                        if(CRM.util.trim(this.dValue) === preValue) return;

                        if(!this.dValue) {
                            _.each(this.dOptions, a => a.text = '');
                            this.handleBlur();
                            return;
                        }

                        this.handleBlur();

                        if(!isAutoFanyi || !this.dShowAiTrans) return;//不自动翻译

                        this.dTransing = true;

                        this._request(this.dValue).then((res) => {
                            this.dTransing = false;
                            this._setDOptions(res).then(() => {
                                this.handleBlur();
                            })
                        }).catch(message => {
                            this.dTransing = false;
                            FxUI.Message.error({message, isMiddler: true});
                        })
                    },

                    aiTranslate() {
                        if(this.dTransing) {
                            FxUI.Message.warning({message: $t('paas.crm.autofanyi'), isMiddler: true});
                            return;
                        };
                        
                        let transEntry = CRM.util.trim(this.dValue);
                        if(!transEntry) {
                            FxUI.Message.warning({message: $t('请填写{{label}}', {label: $t('默认值')}), isMiddler: true});
                            return;
                        }

                        this.dLoading = true;
                        this._request(transEntry).then((res) => {
                            this.dLoading = false;
                            this._setDOptions(res, true).then(() => {
                                this.handleBlur();
                            })
                        }).catch(message => {
                            this.dLoading = false;
                            FxUI.Message.error({message, isMiddler: true});
                        })
                    },

                    _request(transEntry) {
                        return new Promise((resolve, reject) => {
                            this.reqAjax && this.reqAjax.abort();
                            this.reqAjax = CRM.util.FHHApi({
                                url: '/EM1HNCRM/API/v1/object/objectAi/service/chatTranslate',
                                data: {
                                    transEntrys: [transEntry],
                                    languages: _.pluck(support_languages, 'code')
                                },
                                success: (res) => {
                                    this.reqAjax = null;
                                    if(res.Result.StatusCode === 0) {
                                        resolve(res);
                                    } else {
                                        reject(res.Result.FailureMessage);
                                        this._log(res.Result.FailureMessage);
                                    }
                                },
                                error: (e) => {
                                    this.reqAjax = null;
                                    if(e && e.statusText === 'abort') return;

                                    this.dLoading = false;
                                    reject($t('请求失败，请检查网络状况！'));

                                    this._log();
                                }
                            }, {
                                errorAlertModel: 1
                            })
                        })
                    },

                    _log(message) {
                        window.logger && window.logger.log({
                            eventId: 'paasAufoFanyiErrorLog',
                            eventData: {
                                fieldName: me.apiname,
                                message: message || ''
                            }
                        }, {
                            immediate: true
                        });
                    },

                    _renderAutoFanyi() {
                        if(!this.dShowAiTrans) return;//招商局不支持Ai翻译
                        if(!$benchmarker) return;
            
                        try {
                            let vv;
                            $benchmarker.on('focus', () => {
                                vv = CRM.util.trim($benchmarker.val());
                            })
                            $benchmarker.on('blur', () => {
                                const value = CRM.util.trim($benchmarker.val());
                                if(vv === value) return;

                                if(!value) {//无需翻译
                                    _.each(this.dOptions, a => a.text = '');
                                    me._setMultiLang(this.getMultiLangValue());
                                    return;
                                }
            
                                this.dTransing = true;

                                this._request(value).then((res) => {
                                    this.dTransing = false;
                                    this._setDOptions(res).then(() => {
                                        me._setMultiLang(this.getMultiLangValue());
                                    })
                                }).catch(message => {
                                    this.dTransing = false;
                                    FxUI.Message.error({message, isMiddler: true});
                                })
                            })
                        } catch(e) {
                            
                        }
                    },

                    _setDOptions(res, isTip) {
                        return new Promise((resolve) => {
                            let flag = isTip && _.find(this.dOptions, a => a.text);
                            let inner = () => {
                                _.each(res.Value.result.results[0], (content, language) => {
                                    let t = _.find(this.dOptions, a => a.value == language);
                                    t && (t.text = content);
                                })
                            }

                            if(!flag) {
                                inner();
                                return resolve();
                            }

                            this.dConfirm = true;
                            let isRR;
                            let confirm = CRM.util.confirm($t('fx.inputTranslate.override'), $t('提示'), () => {
                                isRR = true;
                                confirm.hide();
                            }, {
                                zIndex: 10000,
                                hideFn: () => {
                                    this.dConfirm = false;
                                    if(isRR) {
                                        inner();//自动覆盖所有数据
                                    } else {//仅覆盖没有值的数据
                                        _.each(res.Value.result.results[0], (content, language) => {
                                            let t = _.find(this.dOptions, a => a.value == language);
                                            t && !t.text && (t.text = content);
                                        })
                                    }
                                    resolve();
                                }
                            })
                        })
                    },

                    getMultiLangValue() {
                        const tmp = {};
                        _.each(this.dOptions, a => tmp[a.value] = a.text);
                        return tmp;
                    },

                    handleCLick(event) {
                        if(this.dTransing) return;
                        
                        if (this.dVisiable && this.cRequire) {
                            this.dShowRequire = false;
                            return;
                        }

                        this.dVisiable = !this.dVisiable;

                        if (this.dVisiable) {
                            try {
                                window.logger.action({
                                    eventId: 's-paasobj_form_fields_multilang',
                                    objectApiName: me.fieldAttr.describe_api_name,
                                    apiName: me.fieldAttr.describe_api_name,
                                    msg: me.apiname
                                });
                            } catch(e) {}
                        }

                        this.bindChannelIfNeeded();
                    },

                    handleBlur() {
                        me._setMultiData(this.dValue, this.getMultiLangValue());
                    },

                    hideErrorIfNeeded() {
                        if (!this.cRequire) {
                            me.hideError();
                        }
                    }
                },
                beforeDestroy() {
                    this._bodyFn && ($(document.body).off('click', this._bodyFn), this._bodyFn = null);
                }
            });

            me.$el.append('<div></div>');
        },

        _setMultiLang(lang) {
            this.model.setData({
                apiname: this.apiname + '__lang',
                value: lang
            }, true, null, true);
        },
        _setMultiData(value, lang, noSetIpt) {
            this.setData(CRM.util.trim(value), null, null, true);
            this.model.set('formDataChange', true);
            this._setMultiLang(lang);

            noSetIpt || (this.$ipt && this.$ipt.val(value));

            this.hideError();
        },

        destroyMultilineActionbar() {
            this.$$multilineActionbar && (this.$$multilineActionbar.destroy(), this.$$multilineActionbar = null);
        }
    };
})