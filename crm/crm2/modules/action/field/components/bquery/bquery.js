/**
 *@desc 工商查询组件
 *<AUTHOR>
 */
define(function (require, exports, module) {

    var util = require('crm-modules/common/util');
    var Base = require('../base');
    var iconfig = require('./config');
    var hook = require('./hook');
    var multilangHook = require('./multilangHook');
    let iconTpl = require('./icon-html');

    module.exports = Base.extend(_.extend({
        events: {
            'click .j-b-detail': 'showBusinessDetail',
            'click .j-b-toadvance': 'showAdvanceBquery',
        },

        template: require('./tpl-html'),

        iconfig: iconfig,

        render: function () {
            const me = this;
            this.obj_apiname = this.fieldAttr && this.fieldAttr.describe_api_name;

            this.bizGetCusConfig ? this.bizGetCusConfig() : this.getBackfillConfig();
            // 设为true时强制不走自动回填功能
            this.notAutoBackfill = false;
            // 隐藏的industry_ext字段需要提交
            let _masterNeedSubmit = this.get('_masterNeedSubmit') || [];
            this.industryExtFieldApiName = this.options.industryExtFieldApiName || 'industry_ext';
            this.bizRegNameFieldApiName = this.options.bizRegNameFieldApiName || 'biz_reg_name';
            if (_.indexOf(_masterNeedSubmit, this.industryExtFieldApiName) == -1) {
                _masterNeedSubmit.push(this.industryExtFieldApiName);
                this.set('_masterNeedSubmit', _masterNeedSubmit);
            }
            this.getBqueryRights().then(async () => {
                await me.doRender();
                // 适配平台多语级翻译
                await me.renderMultilineActionbar();
				const support_languages = this.fieldAttr.support_languages;
				if (!support_languages || !support_languages?.length) {
					$(".crm-action-bquery .crm-action-bquery-search input").css("padding-right", '0px');
				}else{
					$(".crm-action-bquery .crm-action-bquery-search input").css("padding-right", '33px');
				}
            })

        },

        async doRender() {
            this.iconTpl = iconTpl;
            this.bizBeforeRender && this.bizBeforeRender();

            if(!this.$el) return;
            this.$el.html(this.template({
                iconTpl: this.iconTpl(),
            }));
            // 都没有权限时不显示工字
            !this.finallyRights && this.$('.j-b-detail').hide();
            // 没有邓白氏的权限不显示邓白氏
            !this.dengbaishiRights && this.$('.j-b-toadvance').remove();
            await this.renderSearch(this.$('.j-b-search'));  // 处理线索一转三回填问题
            let data = this.get('data');
            let _name = data[this.apiname];
            let industry_ext = this.getIndustry_ext(data);
            let valueObj = _.extend({
                value: _name,
                id: industry_ext.companyId,
                datasource: industry_ext.datasource,
            }, industry_ext);
            this.bobj = data || {};
            this.asyncCheckNameByBusiness(valueObj, this.initialAfterCheckNameByBusiness && this.initialAfterCheckNameByBusiness.bind(this));
            this.bizAfterRender && this.bizAfterRender();
        },

        getIndustry_ext(data) {
            let key = this.industryExtFieldApiName;
            let industry_ext = CRM.util.getIndustry_ext(data, key);
            let _v = {
                id: industry_ext.companyId,
                datasource: industry_ext.datasource,
            }
            return _v;
        },

        getBqueryRights() {
            const me = this;
            return new Promise((resolve) => {
                if (this.bizGetBqueryRights) {
                    return this.bizGetBqueryRights(me.obj_apiname).then((rights) => {
                        me.bqueryRights = rights;
                        // 必须返回basicRights
                        me.basicRights = rights.basicRights;
                        // 所有权限的合集，都没有权限为false，有一个即为true;
                        me.finallyRights = me.getFinallyRights(rights);
                        resolve(rights);
                    });
                }
                return CRM.util.getBqueryRights(me.obj_apiname).then((rights) => {
                    me.bqueryRights = rights;
                    // 所有权限的合集，都没有权限为false，有一个即为true;
                    me.finallyRights = me.getFinallyRights(rights);
                    me.basicRights = rights.basicRights;
                    me.dengbaishiRights = rights.dengbaishiRights;
                    me.tianyanchaRights = rights.tianyanchaRights;
                    me.qichachaRights = rights.qichachaRights;
                    resolve(rights);
                })
            })
        },
        // 获取权限合集，都没有权限为false，有一个即为true
        getFinallyRights(rights) {
            let _rights = false;
            _.each(rights, (r) => {
                if (r) {
                    _rights = true;
                }
            })
            return _rights;
        },
        // 获取datasource对应权限
        getOneRights(rights, datasource) {
            datasource = datasource || 'basic';
            return rights[datasource.toLowerCase() + 'Rights'];
        },

        // 获取配置
        getBackfillConfig: function () {
            var me = this;
            CRM.util.getConfigValue('change_business_type').then(function(value) {
                me.isAutoBackfill = (value === '1');
            })
        },

        getSupplySearchHtml() {
            if (this.bizGetSupplySearchHtml) {
                return this.bizGetSupplySearchHtml()
            }else{
                return `<a class="" title="${$t("查不到想要的企业？点击这里")}" data-from="supply" href="javascript:;">${$t("查不到想要的企业？点击这里")}</a>`
            }
        },

        async getSearch() {
            const me = this;
            let Comp = await require('crm-modules/components/search/search');
            let _ex = {
                _iptFocusHandle: function() {
                    // 只要有一个权限就能出下拉框（排除掉邓白氏）
                    _.each(me.bqueryRights, (right, key) => {
                        if (right && key != 'dengbaishiRights') {
                            this.searchable = true;
                        }
                    })
                    this.fetchAjax && this.fetchAjax.abort();
                    var obj = this.getValue();
                    this.trigger('focus', obj.value);
                    obj.id || obj.value && this.fetch(obj.value);
                },
                _itemClickHandle:function(e){
                    if($(e.target).hasClass('b-btn')){
                        this.noBubbling = true
                        this.$ipt[0].blur();
                        var obj = this.datas[$(e.currentTarget).index()];
                        this.trigger('showBusinessDetail',obj)
                        return
                    }
                    this.noBubbling = false
                    Comp.prototype._itemClickHandle.apply(this, arguments)
                },
                _iptBlurHandle:function(){
                    if(this.noBubbling) {
                        this.hideResult();
                        this.noBubbling = false;
                        return
                    }
                    Comp.prototype._iptBlurHandle.apply(this, arguments)
                },
            }
            if (me.basicRights || me.tianyanchaRights || me.qichachaRights) {
                _ex = _.extend(_ex, {
                    events: _.extend({}, Comp.prototype.events, {
                        'click .j-search-advance': 'toAdvanceBquery',
                    }),
                    render(){
                        const _me = this;
                        Comp.prototype.render.apply(this, arguments);
                        if(this.options.appendBody) {
                            this.$ul.on('click', '.j-search-advance', function(e) {
                                _me.toAdvanceBquery(e);
                            })
                        }
                    },
                    renderResult(data) {
                        if(!this.searchable) return;
                        let val = this.getInputValue();
                        this.datas = data || (data = []);
                        this._index = -1;
                        this.triggerEvent();
                        if (!val && !data.length) {
                            this.hideResult();
                            return;
                        }
                        this.$ul.html(this.getAdvanceHtml(data, val));
                        this.$ul.show().scrollTop(0);
                        if(this.options.appendBody) {
                            var pos = this.$el.offset();
                            this.$ul.css({
                                top: pos.top + this.$el.height() + 'px',
                                left: pos.left + 'px'
                            })
                        }
                    },
                    getAdvanceHtml(data, val) {
                        let html = '';
                        let _wrapli1 = `<li class="crm-comp-search-advance j-search-advance">`;
                        let _wrapli2 = `</li>`;
                        let _advance = `<a class="" title="${$t("高级工商查询")}" data-from="advance" href="javascript:;">${$t("高级工商查询")}></a>`;
                        // let _supply = `<a class="" title="${$t("查不到想要的企业？点击这里")}" data-from="supply" href="javascript:;">${$t("查不到想要的企业？点击这里")}</a>`;
                        let _supply = me.getSupplySearchHtml();
                        let _noData = `<li class="crm-comp-search-nodata">${$t("暂无数据")}</li>`;
                        if (!data.length) {
                            html += _noData;
                        } else {
                            html += this.getListHtml(data);
                        }
                        if (CRM.util.isGrayScale('CRM_SUPPLY_BQUERY') && me.basicRights && (!data.length || (data.length && val !== data[0].value))) {  // 开启灰度并查不到匹配数据显示补充工商查询
                            let con = _supply;
                            if (me.tianyanchaRights || me.qichachaRights) {
                                con = _advance + _supply;
                                con = con.replace(/class=""/g, 'class="cwidth"');
                            }
                            if(con.length) {
                                html += (_wrapli1 + con + _wrapli2);
                            }
                        } else if (me.tianyanchaRights || me.qichachaRights) {  // 存在高级查询权限，显示高级查询
                            html += (_wrapli1 + _advance + _wrapli2);
                        }
                        return html;
                    },
                    toAdvanceBquery(e) {
                        let $tg = $(e.target);
                        let from = $tg.data('from');
                        if (!from) return;
                        let supplyTag = (from === 'supply') ? 1 : 0;
                        this.$ipt.blur();
                        let sourceOptions = me.getSourceOptions(supplyTag);
                        let title = supplyTag == 1 ? $t('工商信息补充查询') : (sourceOptions.length > 1 ? $t('高级工商查询') : sourceOptions[0].label + $t('高级工商查询'));
                        
                        me.showAdvanceBquery(e, {
                            title: title,
                            sourceOptions: sourceOptions,
                            supplyTag: supplyTag,
                        });
                    }
                })
            }
            let Search = Comp.extend(_ex);
            this.bizGetSearch && (Search = this.bizGetSearch(Search));
            return Search;
        },

        getSourceOptions(supplyTag) {
            const me = this;
            let _opt = [];
            if (me.tianyanchaRights || supplyTag) {
                _opt.push({
                    datasource: 'TianYanCha',
                    label: $t("天眼查"),
                })
            }
            if (me.qichachaRights || supplyTag) {
                _opt.push({
                    datasource: 'QiChaCha',
                    label: $t("企查查"),
                })
            }
            return _opt;
        },

        async renderSearch(el) {
            const me = this;
            let Search = await this.getSearch();
            let searchParam = {
                el: el,
                placeholder: me.fieldAttr.help_text || $t("请输入"),
                url: '/EM1HDataptIndustry/industryFcpService/getCompanyByName',
                delay: 500,
                straightway: true,
                appendBody: this.fieldAttr.appendBody,
                parseData: function (obj) {
                    if (!me.basicRights) {
                        return [{
                            value: $t('暂无权限'),
                            errorCode: '3',
                        }]
                    }
                    if (obj.companyEsObjects && obj.companyEsObjects.length) {
                        me.doLog({
                            operationId: 'Search'
                        });
                        // 区分对象的工商搜索埋点
                        CRM.util.sendLog(me.obj_apiname, 'newpage', {
                            operationId: 'BusinessInquiry',
                            eventType: 'cl',
                        });
                    }

                    if(_.contains(['1', '2'], obj.errorCode)){
                        return [{
                            value: obj.message,
                            errorCode: obj.errorCode
                        }]
                    }

                    return _.map(obj.companyEsObjects || [], function (a) {
                        return {
                            id: a.KeyNo,
                            value: a.Name,
                            status: a.Status,
                            operName: a.OperName,
                            creditCode: a.CreditCode,
                            errorCode: obj.errorCode
                        }
                    })
                },
                // callback: function (res) {
                //     if (res.Result.StatusCode !== 0) {
                //         me.showBusinessQueryMsg(res);
                //     }
                //     else {
                //         me.hideBusinessQueryMsg();
                //     }
                // },
                tpl: require('./item-html')
            };
            me.bizParseSearchParam && (searchParam = me.bizParseSearchParam(searchParam));
            me.search = new Search(searchParam)
            //me.search.on('change', me.changeHandle, me);
            me.search.on('focus', me.hideError, me);
            me.search.on('blur', me.changeHandle, me);
            me.search.on('showBusinessDetail',(obj) => { 
                me.showBusinessDetail('',obj,() => { 
                    me.search.$ipt.val(obj.value);
                    me.changeHandle(obj)                                          
                })
            })
            me.search.render();
            me.afterRenderSearch();
        },

        afterRenderSearch() {
            let data = this.get('data');
            let _name = data[this.apiname];
            let industry_ext = this.getIndustry_ext(data);
            if (_name) {
                this.setValue(_.extend({
                    value: _name || '',
                }, industry_ext));
            }
            this.setStatus();
        },

        // 工商回填接口
        backfill: function (obj) {
            var me = this;
            var obj = obj || me.search.getValue();
            if (obj && !obj.id) {
                return
            }
            // CRM.util.waiting($t('工商数据回填中'));
            var url = me.iconfig.getBackfillUrl(me.obj_apiname, me.options.mappingRuleApiname);
            let param = {
                url: url,
                data: {
                    objectDataId: obj.id,
                    industryDatasource: obj.datasource,
                    industrySupplyTag: obj.supplyTag,
                },
                success: function (res) {
                    // CRM.util.waiting(false);
                    if (res.Result.StatusCode === 0) {
                        var data = res.Value.objectData;
                        me.backfillSuccess(data, obj);
                        return;
                    }
                    util.alert(res.Result.FailureMessage || $t("操作失败"));
                },
                error(res) {
                    // CRM.util.waiting(false);
                    util.alert(res.statusText || $t("系统异常"));
                }
            }
            this.bizParseBackfillParam && (param = this.bizParseBackfillParam(param));
            util.FHHApi(param, {
                errorAlertModel: 1,
            });

            util.sendLog('BizQueryObj', 'Detail', {
                operationId: 'WriteBack',
                eventType: 'cl',
            });

            // 区分对象的工商回填埋点
            util.sendLog(this.obj_apiname, 'newpage', {
                operationId: obj.datasource ? obj.datasource + (obj.supplyTag ? 'Supply': '') + 'WriteBack' : 'BusinessWriteBack',
                eventType: 'cl',
            });
        },

        backfillSuccess(data, obj = {}) {
            const me = this;
            // 负责人不回填
            data.owner = void 0;
            // 邓白氏和天眼查需要重新设置值
            if(obj.datasource) {
                // 如果是补充查询返回的数据，后续无需存储datasource
                if(obj.supplyTag === 1) {
                    delete obj.datasource;
                }
                // 无论是否补充查询后续均无需传supplyTag，删除
                delete obj.supplyTag;

                // 由于邓白氏数据查询列表和映射接口返回的名称不一样，需要等接口返回再手动设置一次
                obj.value = data[this.apiname];
                me.setValue(obj);
                // changeHandle中有调用backfill，此时需要强制不回填，不走backfill，防止造成死循环
                me.notAutoBackfill = true;
                me.changeHandle(obj);
                // 如果是从高级查询回填需要关闭高级查询
                if (me.advanceBquery) {
                    me.advanceBquery.destroy && me.advanceBquery.destroy();
                    me.advanceBquery = null;
                }
            } else {
                data[this.apiname] == obj.value && (data[this.apiname] = void 0);
                data.object_describe_api_name = void 0;
            }

            me.parseBackfillData && (data = me.parseBackfillData(data));
            me.bizParseBackfillData && (data = me.bizParseBackfillData(data));
            // 回填数据
            me.model.newBatchUpdate(data);
        },

        showBusinessQueryMsg: function (res) {
            if (res.Result.FailureMessage && !CRM.getLocal('no_business_query_quota')) {
                CRM.util.alert(res.Result.FailureMessage, function () {
                    CRM.setLocal('no_business_query_quota', true);
                });
            }
        },

        hideBusinessQueryMsg: function () {
            CRM.setLocal('no_business_query_quota', false);
		},

        changeHandle: function (obj, notrigger) {
            this.asyncCheckCustomerName(obj.value);
            // 有工商，但是没有该数据权限, 则不修改数据不回填不点亮（编辑、复制而来的初始数据存在这种情况）
            // 正常由输入得来的数据有工商肯定有权限，则正常更新
            // 如果数据没有工商，则正常更新
            if (obj.id && !this.getOneRights(this.bqueryRights, obj.datasource)) return;
            this.updateVal(obj, notrigger);
            this.execBackFill(obj);
            this.bizAfterChange && this.bizAfterChange(obj);
        },

        execBackFill: function (obj) {
            // 强制不回填的情况
            if (this.notAutoBackfill) {
                this.notAutoBackfill = false;
                return;
            }
            // 自定义回填方法
            // if (this.bizCusExecBackFill) {
            //     this.bizCusExecBackFill(obj);
            //     return;
            // }
            // 默认回填逻辑
            if (this.isAutoBackfill) {
                this.backfill(obj);
            } 
        },

        updateVal: function (obj, notrigger) {
            obj = obj || this.search.getValue();
            if (obj.value !== (this.getData() || '')) {
                this.setData(obj.value, null, notrigger, true);
            }
            this.afterUpdate(obj);
        },

        afterUpdate(obj) {
            if (this.bizAfterUpdate) {
                this.bizAfterUpdate(obj)
                return;
            }
            let _obj = {};
            _obj[this.industryExtFieldApiName] = !obj.id ? '' :JSON.stringify({
                datasource: obj.datasource, 
                companyId: obj.id
            });
            _obj[this.bizRegNameFieldApiName] = !!obj.id;
            this.model.onlyUpdateValue(_obj);
            obj.id ? this.lightBtn() : this.shutBtn();
        },

        getCheckDuplicateUrl: function(ObjectApiname) {
            var map = {
                'AccountObj': '/EM1HNCRM/API/v1/object/sfa_duplicate_search/service/duplicate_check',
                'PartnerObj': '/EM1HNCRM/API/v1/object/sfa_duplicate_search/service/duplicate_check',
                'AccountMainDataObj': '/EM1HNCRM/API/v1/object/sfa_duplicate_search/service/duplicate_check',
            }
            return map[ObjectApiname];
        },

		//根据apiName获取参数对象
        getCheckDuplicateParam: function (apiName, name) {
            var me = this;
            var obj = null;
                obj = {
                    api_name: apiName,
                    object_data: {
                        name: name,
                        _id: me.bobj._id || ''
                    }
                }
            return obj
        },

        asyncCheckCustomerName: function (name) {
            var me = this;
            var urlStr = me.getCheckDuplicateUrl(me.obj_apiname);

            if (!name) {
                me.hideTips();
                me.fieldAttr.is_required && me.showError();
                return;
            }
            if (me.obj_apiname === 'AccountObj' && me.bobj._id && (me.bobj.name === name)) { //编辑时名称和初始值相同不进行判重
                me.hideTips();
                return;
            };

            me.hideError();

            if (!urlStr) {
                return;
            }

            me.checkAjax && me.checkAjax.abort();

            me.checkAjax = util.FHHApi({
                url: urlStr,
                data: me.getCheckDuplicateParam(me.obj_apiname, name),
                success: function (res) {
                    if (me.obj_apiname === 'PartnerObj') {
                        me.setPartnerObjResult(res)
                    } else if (me.obj_apiname === 'AccountObj') {
                        me.setCustomerObjResult(res)
                    } else {
                        me.afterDuplicateCheck(res);
                    }
                },
                complete: function () {
                    me.checkAjax = null;
                }
            }, {
                errorAlertModel: 1
            });
        },

        // 通用查重处理方法
        afterDuplicateCheck(res) {
            var me = this;
            if (res && res.Result && res.Result.StatusCode === 0) {
                if (res.Value.duplicated) {
                    me.showTips($t("该{{displayName}}已存在", {displayName: me.fieldAttr.label}));
                } else {
                    me.hideTips();
                }
            } else {
                me.hideTips();
            }
        },

        // 设置合作伙伴对象成功回调结果
        setPartnerObjResult: function (res) {
            var me = this;
            if (res && res.Result && res.Result.StatusCode === 0) {
                if (res.Value.duplicated) {
                    me.showTips($t("该合作伙伴已存在"));
                } else {
                    me.hideTips();
                }
            } else {
                me.hideTips();
            }
        },

        // 设置客户对象成功回调结果
        setCustomerObjResult: function (res) {
            var me = this;
            if (res && res.Result && res.Result.StatusCode === 0) {
                if (res.Value.duplicated) {
                    // var text = $t("该客户已存在") + (res.Value.Status == 99 ? '(' + $t("已作废") + ')' : '');
                    var text = $t("该客户已存在")
                    me.showTips(text);
                } else {
                    me.hideTips();
                }
            }
        },

        asyncCheckNameByBusiness: function (valueObj, cb) {
            var me = this;
            if((_.isString(valueObj) && !valueObj) || (_.isObject(valueObj) && !valueObj.value)){
                return;
            }
            if (_.isString(valueObj)) {
                let data = this.get('data');
                let industry_ext = this.getIndustry_ext(data);
                valueObj = _.extend({
                    value: valueObj,
                }, industry_ext)
            }
            // 没有当前工商datasource对应的功能权限，则不查询不点亮
            if (!this.getOneRights(this.bqueryRights, valueObj.datasource)){
                // 只回填字符串
                let _v = {value: valueObj.value};
                me.setValue(_v);
                if (cb) {
                    cb({value: valueObj.value}, valueObj);
                }
                return;
            } 
            if (valueObj.id) {
                me.hideBusinessQueryMsg();
                me.setValue(valueObj);
                if (cb) {
                    cb(valueObj);
                    return;
                }
                me.updateVal(valueObj);
                return;
            }
            let name = valueObj.value;
            let param = {
                url: '/EM1HDataptIndustry/industryFcpService/CheckCustomerByBusinessQuery',
                data: {
                    KeyWord: name
                },
                success: function (res) {
                    if (res.Result.StatusCode === 0 && res.Value.KeyNo) {
                        let _data = {
                            value: name,
                            id: res.Value.KeyNo
                        }
                        // me.hideBusinessQueryMsg();
                        me.setValue(_data)
                        if (cb) {
                            cb(_data)
                            return;
                        }
                        me.updateVal(_data)
                    } else {
                        // me.showBusinessQueryMsg(res);
                        let _d = {value: name};
                        me.setValue(_d);
                        if (cb) {
                            cb(_d);
                            return;
                        }
                        me.updateVal(_d);
                    }
                },
                complete: function () {
                    me.businessAjax = null;
                }
            }
            me.bizParseCheckNameParam && (param = me.bizParseCheckNameParam(param, valueObj));
            me.businessAjax && me.businessAjax.abort();
            me.businessAjax = util.FHHApi(param, {
                errorAlertModel: 1
            });
        },

        doLog: function (data, subM) {
            subM = subM || 'list';

            CRM.util.uploadLog('BizQueryObj', subM, _.extend({
                eventType: 'cl'
            }, data || {}));
        },

        showBusinessDetail: function (e, obj, fun) {
            var me = this;

            if (me.bizCusShowBusinessDetail) {
                me.bizCusShowBusinessDetail(obj);
                return;
            }

            var obj = obj || me.search.getValue();

            // 没有id则返回
            if(!obj.id) return;

            // 有数据id没有权限则提示无权限查看（补充查询进详情无需判断权限）
            if (obj.supplyTag != 1 && !this.getOneRights(this.bqueryRights, obj.datasource)){
                let map = {
                    QiChaCha: $t("企查查"),
                    DengBaiShi: $t('邓白氏'),
                    TianYanCha: $t('天眼查'),
                }
                let datasourceText = map[obj.datasource] || $t('普通');
                CRM.util.alert($t('不具备{{datasourceText}}工商查询功能权限', {
                    datasourceText: datasourceText,
                }))
                return;
            }

            me.doLog({
                operationId: 'BusinessInquiryDetail'
            });

            // 区分对象的工商详情埋点
            CRM.util.sendLog(me.obj_apiname, 'newpage', {
                operationId: obj.datasource ? obj.datasource + (obj.supplyTag ? 'Supply': '') + 'Detail' : 'BusinessDetail',
                eventType: 'cl',
            });

            CRM.api.business_detail({
                apiname: me.obj_apiname,
                id: obj.id,
                name: obj.value,
                datasource: obj.datasource,
                supplyTag: obj.supplyTag,
                zIndex: CRM.util.getzIndex(me.get('zIndex')) + 10,
                isShowBtn: true,
                mappingRuleApiname: me.options.mappingRuleApiname,
                success: function (res) {
                    if (!res) return;
                    if(fun && typeof fun === 'function') fun()
                    me.backfillSuccess(res, obj);
                }
            });
            
        },

        showAdvanceBquery(e, options = {}) {
            const me = this;
            require.async('vcrm/sdk', (sdk) => {
                sdk.getComponent('advanceBquery').then((res) => {
                    let Comp = res.default;
                    let obj = me.search.getValue();
                    me.advanceBquery = new Comp(_.extend({
                        title: $t("邓白氏全球企业信息查询"),
                        inputValue: obj && obj.value,
                        objectApiname: me.obj_apiname,
                        apiname: me.apiname,
                    }, options));
                    me.advanceBquery.$on('showDetail', (data, datasource) => {
                        me.showBusinessDetail(null, {
                            id: data.KeyNo,
                            value: data.Name,
                            datasource: datasource,
                            supplyTag: options.supplyTag,
                        })
                    })
                    me.advanceBquery.$on('change', (data, datasource) => {
                        let _data = {
                            id: data.KeyNo,
                            value: data.Name,
                            datasource: datasource,
                            supplyTag: options.supplyTag,
                        }
                        me.backfill(_data);
                    })
                })
            })
        },

        getValue: function () {
            var obj = this.search.getValue();
            if (this.fieldAttr.is_required && !obj.value) {
                this.showError();
            }
            return CRM.util.trim(obj.value);
        },

        setValue: function (obj) {
            this.hideError();
            if (_.isString(obj)) {
                obj = { value: obj };
            }
            this.search && this.search.setValue(obj);
        },

        disable: function () {
            this.search && this.search.disable();
            // 只读的时候不展示邓白氏
            this.$('.j-b-toadvance').hide();
        },

        enable: function () {
            this.isReadonly() || this.search && this.search.enable();
            // 可编辑的时候展示邓白氏
            this.isReadonly() || this.$('.j-b-toadvance').show();
        },

        setStatus: function() {
            var isSetEditStatus = this.iconfig.isSetEditStatus[this.obj_apiname];
            if (isSetEditStatus && this.get('isEdit')) {
                this.setEditStatus();
            } else {
                this.super.setStatus.apply(this, arguments);
            }
        },

		/**
         * 设置字段编辑时初始状态
         */
        setEditStatus: function() {
            var me = this;
            // 客户  【from=3客户管理页面；以及报备中的客户】 直接返回，可编辑客户名称
            if (me.obj_apiname === 'AccountObj' && (me.get('from') == 3 || me.get('status') == 1)) return false;

            // 默认设置为不可编辑
            me.disable();

            // 客户和合作伙伴根据配置设置是否允许修改字段
            // 1 可以修改  0 不可修改
            var map = {
                PartnerObj: '48',
                AccountObj: '11',
            }
            var configKey = map[me.obj_apiname];
            if (!configKey) return;
            util.getConfigValue(configKey).then(function(value){
                var conf = +value ? true : false;
                conf && me.enable(); //汇聚配置可编辑 并且客户的负责人是当前用户可编辑
            }, function(msg){
                me.enable();
            });
        },

        lightBtn: function () {
            this.$('.j-b-detail').addClass('light-btn').attr('title', $t("查看工商信息"));
        },

        shutBtn: function () {
            this.$('.j-b-detail').removeClass('light-btn').attr('title', '');
        },

        destroy: function () {
            this.checkAjax && this.checkAjax.abort();
            this.businessAjax && this.businessAjax.abort();
            this.search && this.search.destroy();
            this.bdetail && this.bdetail.destroy();
            this.search = this.checkAjax = this.businessAjax = this.bdetail = null;
            this.advanceBquery && this.advanceBquery.destroy && this.advanceBquery.destroy();
            this.super.destroy.apply(this, arguments);
        }
    }, hook, require('../multilang/multilang'), multilangHook))
})
