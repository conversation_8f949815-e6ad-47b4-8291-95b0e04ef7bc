/**
 * @description 表单插件相关逻辑
 * <AUTHOR>
 */
 define(function(require, exports, module) {
    let modelPlugin = require('./plugin/index');
    const BaseComponnets = require('../components/components');
    const calService = require('./calservice');
    return {
        getTriggerInfo() {
            let tt = this.get('__trigger_info');
            return tt || {
                trigger_page: this.get('isEdit') === true ? 'Edit' : 'Add'
            }
        },
        assertUsePlugin() {
            return this.get('pluginService') && this.get('pluginService').getHookHandler && !!this.get('pluginService').getHookHandler().length;
        },
        assertInEvents(fieldName, apiName) {
            let events = this.get('uiEvents');
            if(!events) return;
            return (events[apiName || this.get('apiname')] || {})[fieldName];
        },
        getDetailLayout(objectApiName, recordType) {
            let dom = _.findWhere(this.get('detailObjectList'), {objectApiName});
            return dom &&  _.findWhere(dom.layoutList, {record_type: recordType});
        },
        getObjectLayouts(apiName, recordType) {
            let objectLayouts = [];
            if(!apiName || apiName === this.get('apiname')) {
                if(this.get('allLayouts')) {
                    _.each(this.get('allLayouts'), b => objectLayouts.push({field_name: b.api_name, is_readonly: b.is_readonly, is_required: b.is_required, render_type: b.render_type}));
                } else {
                    _.each(this.get('layout'), a => {
                        Array.prototype.push.apply(objectLayouts, _.map(a.components, b => ({field_name: b.api_name, is_readonly: b.is_readonly, is_required: b.is_required, render_type: b.render_type})));
                    });
                }
            } else {
                let ds = this.get('__describeLayout');
                _.find(ds && ds.detailObjectList, a => {
                    if(a.objectApiName === apiName) {
                        _.find(a.layoutList, b => {
                            if(b.record_type === recordType) {
                                _.each(b.detail_layout.components, c => {
                                    _.each(c.field_section, d => Array.prototype.push.apply(objectLayouts, d.form_fields));
                                })
                                return true;
                            }
                        })
                        return true;
                    }
                })
            }
            return objectLayouts;
        },
        getObjectLayoutFields(apiName, recordType) {
            return _.pluck(this.getObjectLayouts(apiName, recordType), 'field_name');
        },
        _checkNoNeedPluginService(eventKey) {
            if(!eventKey) return;
            
            let ps = this.get('pluginService');
            if(!ps) return;

            _.isString(eventKey) && (eventKey = [eventKey])

            return !_.find(ps.getHookHandler() || [], item => !!_.find(eventKey, k => item[k]));
        },
        //获取插件的通用参数
        //通过插件访问的以及更新的数据是临时数据
        //任何需要更新数据的地方，必须调用插件参数提供的方法，不允许私自改数据，否则不生效
        getPluginService(opts = {}) {
            if(this._checkNoNeedPluginService(opts.__eventKey)) return {run() {return Promise.resolve({})}, end() {}}

            let me = this;
            let masterObjApiName = me.get('apiname');
            let masterData, details;
            let _updatedData = {}, _delDatas = [], __addStatus, _insertDatas = [], _moveDatas = [], _masterUpdate = {}, _fieldAttribute = {}, _detailFieldAttribute = {}, detailRowIdFieldAttribute = {}, uiEventAttr = {};
            let _rowId = {};
            let relatdDatas;
            
            let dataGetter = {
                getRequestId() {
                    return me.get('requestId');
                },
                //获取当前选中的数据
                getCheckedDatas(apiName, recordType) {
                    let checkDatas;
                    if(!recordType) {
                        checkDatas = [];
                        let layoutList = (_.findWhere(dataGetter.getDescribeLayout().detailObjectList, {objectApiName: apiName}) || {}).layoutList;
                        layoutList && _.each(_.pluck(layoutList, 'record_type'), recordType => {
                            me.trigger(`getCheckedData_${apiName}_${recordType}`, datas => {
                                [].push.apply(checkDatas, datas || []);
                            });
                        })

                        me.trigger('getCheckedDatasHandle', (datas) => {
                            checkDatas = _.filter(datas, a => a.object_describe_api_name === apiName);
                        });

                        return checkDatas;
                    }

                    me.trigger(`getCheckedData_${apiName}_${recordType}`, datas => checkDatas = datas);
                    me.trigger('getCheckedDatasHandle', (datas) => {
                        checkDatas = _.filter(datas, a => a.object_describe_api_name === apiName && a.record_type === recordType);
                    });

                    return checkDatas || [];
                },
                //获取单条从对象的原始数据
                getMDOriginData(rowId, keys) {
                    let data = {};
                    _.find(me.getDetails(), arr => {
                        let tt = _.findWhere(arr, {rowId});
                        if(tt) {
                            data = _.pick(tt, keys);
                            return true;
                        }
                    })

                    return data;
                },
                getMasterData() {
                    return masterData || (masterData = _.extend({}, opts.masterData || me.get('data')))
                },
                getDescribe(apiName) {
                    return me.getDescribeByObjApiName(apiName || pluginService.objApiName);
                },
                getLayoutFields(apiName, rt) {
                    return me.getObjectLayoutFields(apiName, rt);
                },
                getFieldLayoutAttr(fieldName, apiName, rt) {
                    return _.findWhere(me.getObjectLayouts(apiName, rt), {field_name: fieldName});
                },
                getData(apiName, rowId) {
                    if(rowId) {
                        let data;
                        _.find(dataGetter.getDetails(), arr => data = _.findWhere(arr, {rowId: rowId}));
                        return data;
                    }
                    return dataGetter.getMasterData();
                },
                getDetail(apiName) {
                    return dataGetter.getDetails()[apiName || pluginService.objApiName]
                },
                getDetails() {
                    if(!details) {
                        details = {};
                        _.each(opts.details || me.getDetails(), (arr, apiname) => {
                            details[apiname] = _.map(arr, (a) => {
                                return _.extend({}, a);
                            })
                        })
                    }
                    return details;
                },

                //获取关联对象数据
                getRelateDatas() {
                    if(!relatdDatas) {
                        relatdDatas = {};
                        let relateDataList = me.get('related_data_list'); //{objApiName: [{relatedFieldName: 'xxx', dataList: [{...}]}]}
                        _.each(relateDataList, (arr, objApiName) => {
                            relatdDatas[objApiName] = _.map(arr, a => {
                                return {
                                    relatedFieldName: a.relatedFieldName,
                                    dataList: _.map(a.dataList, b => ({...b}))
                                }
                            })
                        })
                    }

                    return relatdDatas;
                },

                getFieldAttr(fieldName, apiName) {
                    let ds = dataGetter.getDescribe(apiName || pluginService.objApiName);
                    return ds && ds.fields[fieldName];
                },
                getDescribeLayout() {//透传describeLayout接口完整返回值
                    return me.get('__describeLayout');
                },

                /**
                 * @desc 获取从对象，某些字段的计算字段；
                 * @param fields ['price', 'discount']
                 * @param includeSelf 包含来源字段
                 */
                 getCalculateFieldsByFieldName(fields, includeSelf, apiname = '') {
                    fields = _.isArray(fields) ? fields : [fields];
                    apiname = apiname || pluginService.objApiName;
                    let tmp = {};
                    let allFields = this.getDescribe(apiname).fields || {};
                    _.each(fields, (f) => {
                        let a = allFields[f] || {};
                        a.calculate_relation && _.each(a.calculate_relation.calculate_fields, (arr, k) => {
                            [].push.apply(tmp[k] || (tmp[k] = []), arr);
                        })
                    });

                    if(includeSelf){
                        if(!tmp[apiname]) tmp[apiname] = [];
                        tmp[apiname] = tmp[apiname].concat(fields);
                    }

                    _.each(tmp, (arr, k) => {
                        tmp[k] = _.union(arr);
                    });

                    return tmp;
                },

                /**
                 * @desc 给计算字段追加order
                 */
                getCalculateFieldsOrder(calFields) {
                    return me._getCalculateFieldOrder(calFields);
                },

                /**
                 * @desc 判断从对象数据是否是插件执行过程中新增的
                 */
                getDataIsNew(rowId) {
                    return !_.find(me.getDetails(), arr => _.find(arr, item => {
                        return (_rowId[item.rowId] || item.rowId) == rowId
                    }));
                },

                getOptions(key) {
                    return me.get(key);
                },
                
                __getUpdatedData() {
                    return _updatedData;
                },
                __getDelData() {
                    return _delDatas;
                },

                __getSort() {
                    let sortRowIds = {};
                    _.each(pluginService.__sort, (rts, objApiName) => {
                        let arr = dataGetter.getDetail(objApiName);
                        _.each(rts, rt => {
                            let narr = _.where(arr, {record_type: rt});
                            if(narr.length > 1) {
                                (sortRowIds[objApiName] || (sortRowIds[objApiName] = {}))[rt] = _.pluck(narr, 'rowId');
                            }
                        })
                    })

                    return _.isEmpty(sortRowIds) ? void 0 : sortRowIds;
                }
            }
            let dataUpdater = {
                //过滤旧的数据
                __filterOldAddDatas(rowIds, apiName) {
                    let detail = dataGetter.getDetail(apiName);
                    let newList = _.filter(detail, a => !_.contains(rowIds, a.rowId));
                    detail.splice(0, detail.length, ...newList);
                },
                //添加关联对象数据
                addRelateDatas(opts) {
                    __addStatus = true;
                    let relatdDatas = dataGetter.getRelateDatas();
                    let {objApiName, recordType, relatedFieldName} = opts;
                    let dataList = _.map(opts.dataList, a => {
                        return {
                            ...a,
                            object_describe_api_name: objApiName,
                            record_type: recordType,
                            rowId: me.getRowUniqueId()
                        }
                    })
                    if(relatdDatas[objApiName]) {
                        let tt = _.findWhere(relatdDatas, {relatedFieldName});
                        if(!tt) {
                            relatdDatas[objApiName].push({
                                relatedFieldName,
                                dataList
                            })
                        } else {
                            [].push.apply(tt.dataList, dataList);
                        }
                    } else {
                        relatdDatas[objApiName] = [{
                            relatedFieldName,
                            dataList
                        }]
                    }

                    return _.findWhere(relatdDatas[objApiName], {relatedFieldName});
                },

                //删除关联对象数据
                delRelateDatas(opts) {
                    __addStatus = true;
                    let relatdDatas = dataGetter.getRelateDatas();
                    let {objApiName, recordType, relatedFieldName} = opts;

                    if(relatdDatas[objApiName]) {
                        let tt = _.findWhere(relatdDatas[objApiName], {relatedFieldName});
                        if(tt) {
                            tt.dataList = _.filter(tt.dataList, a => a.record_type !== recordType);
                        }
                    }
                },

                add(data, noMixBasicData) {//添加从对象数据
                    __addStatus = true;
                    return _.map(data, a => {
                        let apiname = a.object_describe_api_name || a.objApiName;
                        let rt = a.recordType || a.record_type;
                        let item = a;
                        if(apiname && rt) {
                            let details = dataGetter.getDetails();
                            item = _.extend({}, noMixBasicData || a.isFake ? {} : me.getRowBasicData(apiname, rt), a);
                            (details[apiname] || (details[apiname] = [])).push(item);
                        }

                        return item;
                    })
                },
                del(apiName, rowId) {//删除指定的从对象数据 rowId 数据唯一索引
                    let details = dataGetter.getDetails();
                    let arr = details[apiName];
                    if(arr) {
                        details[apiName] = _.filter(arr, data => {
                            if(data.rowId !== rowId) {
                                return true;
                            } else {
                                _delDatas.push(data);
                            }
                        })
                    }
                },
                delDetail(apiName) {
                    const details = dataGetter.getDetails();
                    const datas = details[apiName || pluginService.objApiName] || [];
                    _delDatas.push(...datas);
                    details[apiName || pluginService.objApiName] = [];
                    return datas;
                },
                delDetailAndTrigger(objApiName) {
                    let delDatas = dataUpdater.delDetail(objApiName);
                    return delDatas.length ? pluginService.triggerCalAndUIEvent({delDatas, objApiName}) : Promise.resolve({});
                },
                delDetailRecordType(objApiName, recordType = pluginService.recordType) {
                    const details = dataGetter.getDetails();
                    const datas = details[objApiName || pluginService.objApiName] || [];
                    const delRowIds = [];
                    details[objApiName || pluginService.objApiName] = _.filter(datas, item => {
                        return item.record_type === recordType ? (delRowIds.push(item.rowId), _delDatas.push(item), false) : true;
                    })
                    return delRowIds;
                },
                sort(objApiName, recordType, fn) {
                    let detail = dataGetter.getDetail(objApiName);
                    let arr1 = [];
                    let arr2 = [];
                    let indexs = [];
                    _.each(detail, (a, index) => {
                        if(a.record_type === recordType) {
                            arr1.push(a);
                            indexs.push(index);
                            return;
                        } else {
                            arr2.push(a);
                        }
                    })

                    let length = arr1.length;
                    if(length < 2) return;

                    let narr = fn(arr1);
                    if(narr.length !== length) return;

                    let tmp = (pluginService.__sort || (pluginService.__sort = {}));
                    (tmp[objApiName] || (tmp[objApiName]  = [])).push(recordType);

                    if(indexs[0] + length === indexs[length - 1] + 1) {//连续的
                        detail.splice(indexs[0], length, ...narr);
                    } else {
                        arr1 = _.union(arr2, narr);
                        detail.splice(0, detail.length, ...arr1);
                    }
                },
                insert(objApiName, insertRowId, datas, isBefore) {
                    let arr = dataGetter.getDetail(objApiName);
                    let item = dataGetter.getData('', insertRowId);
                    let index = _.indexOf(arr, item);
                    if(index > -1) {
                        _.each(datas, a => {
                            let aindex = _.indexOf(arr, a);
                            if(aindex > -1) {
                                arr.splice(aindex, 1);//移除
                            }
                        })
                        index = _.indexOf(arr, item);
                        arr.splice(index + (isBefore ? 0: 1), 0, ...datas);
                        _insertDatas.push({
                            isBefore,
                            insertRow: insertRowId,
                            rowIds: _.pluck(datas, 'rowId')
                        })
                    } else {
                        arr.push(...datas);
                        let narr = _.union(arr);
                        if(narr.length !== arr.length) {
                            arr.splice(0, arr.length, ...narr);
                        }
                    }
                },
                nodeMoveTo(insertedRowId, nodeRowId, isBefore) {
                    let datas = [dataGetter.getData('', nodeRowId)];
                    let list = dataGetter.getDetail('object_80bM8__c') || [];
                    let _inner = function(rowId, item) {
                        item && datas.push(item);
                        let children = _.where(list, {parent_rowId: rowId});
                        children && children.length && _.each(children, a => _inner(a.rowId, a))
                    }

                    _inner(nodeRowId);

                    dataUpdater.moveTo(insertedRowId, datas, isBefore);
                },
                moveTo(insertRowId, datas, isBefore) {
                    let t = _.find(_moveDatas, a => a.insertRow.rowId == insertRowId && a.isBefore === isBefore);
                    if(t) {
                        _.each(datas, a => {
                            let tt = _.findWhere(t.datas, {rowId: a.rowId});
                            tt ? _.extend(tt, a) : t.datas.push(a);
                        })
                    } else {
                        _moveDatas.push({
                            insertRow: dataGetter.getData('', insertRowId),
                            datas: datas.slice(0),
                            isBefore
                        })
                    }
                },
                //更新rowId仅只能在md.render.before之前用，建议尽量不要用
                updateRowId(newRowId, oldRowId) {
                    if(newRowId == oldRowId) return;
                    
                    let u1 = _updatedData[oldRowId];
                    if(u1) {
                        _updatedData[newRowId] = u1;
                        u1.rowId && (u1.rowId = newRowId);
                        delete _updatedData[oldRowId]
                    }
                    
                    u1 = _.findWhere(_delDatas, {rowId: oldRowId});
                    if(u1) {
                        u1.rowId = newRowId;
                    }

                    u1 = detailRowIdFieldAttribute[oldRowId];
                    if(u1) {
                        detailRowIdFieldAttribute[newRowId] = u1;
                        delete detailRowIdFieldAttribute[oldRowId]
                    }
                    _.each(_insertDatas, a => {
                        if(a.insertRow === oldRowId) {
                            a.insertRow = newRowId
                        } else {
                            let pp =  _.indexOf(a.rowIds || [], oldRowId)
                            pp > -1 && a.rowIds.splice(pp, 1, newRowId);
                        }
                        
                    })

                    _.find(dataGetter.getDetails(), arr => {
                        u1 = _.findWhere(arr, {rowId: oldRowId});
                        if(u1) {
                            u1.rowId = newRowId;
                            return true;
                        }
                    })

                    let pos = _.indexOf(pluginService.dataIndex || [], oldRowId);
                    if(pos > -1) {
                        pluginService.dataIndex.splice(pos, 1, newRowId);
                    }
                    pos = _.indexOf(pluginService.newDataIndexs || [], oldRowId);
                    if(pos > -1) {
                        pluginService.newDataIndexs.splice(pos, 1, newRowId);
                    }

                    _rowId[oldRowId] = newRowId;
                },
                updateMaster(obj) {
                    let tt = dataGetter.getMasterData();
                    let tmp = {};
                    _.each(obj, (v, k) => {
                        if(tt[k + '__encrypt'] && obj[k + '__encrypt'] === void 0) {
                            tmp[k + '__encrypt'] = '';
                        }
                        if(tt[k + '__s'] && obj[k + '__s'] === void 0) {
                            tmp[k + '__s'] = '';
                        }
                    })
                    _.extend(obj, tmp);
                    _.extend(_masterUpdate, obj);
                    _.extend(tt, obj);
                },
                updateMasterExt(obj) {
                    dataUpdater.updateMaster(obj);
                    let _masterNeedSubmit = me.get('_masterNeedSubmit') || [];
                    [].push.apply(_masterNeedSubmit, _.keys(obj));
                    me.set('_masterNeedSubmit', _.union(_masterNeedSubmit));
                },
                updateDetail(apiName, rowId, objData) {
                    let obj = dataGetter.getData(apiName || pluginService.objApiName, rowId);
                    if(obj && !_.isEmpty(objData)) {
                        let tmp = {};
                        _.each(objData, (v, k) => {
                            if(obj[k + '__encrypt'] && objData[k + '__encrypt'] === void 0) {
                                tmp[k + '__encrypt'] = '';
                            }
                            if(obj[k + '__s'] && objData[k + '__s'] === void 0) {
                                tmp[k + '__s'] = '';
                            }
                        })
                        _.extend(objData, tmp);
                        _.extend(obj, objData);
                        let tt = _updatedData[rowId];
                        if(tt) {
                            _.extend(tt, objData);
                        } else {
                            _updatedData[rowId] = _.extend(_.pick(obj, ['object_describe_api_name', 'record_type', 'rowId']), objData) 
                        }
                    }
                },
                updateData(apiName, rowId, objData) {
                    apiName === masterObjApiName ? dataUpdater.updateMaster(objData) : dataUpdater.updateDetail(apiName, rowId, objData);
                },
                updateByCalResult(calResult) {
                    _.each(calResult, (item, apiName) => {
                        _.each(item, (obj, rowId) => {
                            dataUpdater.updateData(apiName, rowId, obj);
                        })
                    })

                    dataUpdater.__setFieldCheckCalculationConfig({calResult});
                },

                //存贮字段是否被计算过，或手动变更过，方便给保存接口传相关参数，告诉server那些字段需要重新验算
                __setFieldCheckCalculationConfig(opts) {
                    let fcc = pluginService.__fieldCheckCalculationConfig;
                    if(!fcc) {
                        fcc = pluginService.__fieldCheckCalculationConfig = {master: {}};
                    }
                    _.each(opts.changeFields, (keys, rowId) => {
                        _.each(keys, key => {
                            if(rowId === 'master') {
                                fcc.master[key] = 0;
                                return;
                            }
                            let tm = fcc[rowId];
                            if(!tm) {
                                tm = fcc[rowId] = {}
                            }
                            tm[key] = 0;
                        })
                    })
                    _.each(opts.calResult, (item, objApiName) => {
                        if(objApiName === masterObjApiName) {
                            _.each(item[0], (v, k) => {
                                fcc.master[k] = 1;
                            })
                        } else {
                            _.each(item, (upDateTrData, rowId) => {
                                let tm = fcc[rowId];
                                if(!tm) {
                                    tm = fcc[rowId] = {};
                                }
                                _.each(upDateTrData, (v, k) => tm[k] = 1);
                            })
                        }
                    })
                    _.each(opts.uiResult, (item, objApiName) => {
                        if(objApiName === masterObjApiName) {
                            _.each(item, (v, k) => fcc.master[k] = 0);
                        } else {
                            _.each(item.u, (upDateTrData, rowId) => {
                                let tm = fcc[rowId];
                                if(!tm) {
                                    tm = fcc[rowId] = {}
                                }
                                _.each(upDateTrData, (v, k) => tm[k] = 0);
                            })
                        }
                    })
                },

                setReadOnly(opts) {
                    let fieldName = [];
                    if(opts.whiteFieldName) {//['name', '__c'] __c 代表所有的自定义字段
                        fieldName = _.difference(pluginService.dataGetter.getLayoutFields(opts.objApiName, opts.recordType), opts.whiteFieldName);
                        _.contains(opts.whiteFieldName, '__c') && (fieldName = _.filter(fieldName, a => !/__c$/.test(a)));
                    }
                    if(opts.fieldName) {
                        [].push.apply(fieldName, _.isArray(opts.fieldName) ? opts.fieldName : [opts.fieldName]);
                    }

                    _.find(fieldName, a => {
                        if(a === '__c') { //把__c替换为所有的自定义字段
                            _.each(pluginService.dataGetter.getLayoutFields(opts.objApiName, opts.recordType), a => {
                                /__c$/.test(a) && fieldName.push(a);
                            });
                            return true;
                        }
                    })
                    fieldName = _.filter(_.union(fieldName), a => a !== '__c');

     
                    if(!opts.dataIndex) {//主对象
                        let fieldAttribute = {};
                        fieldName = dataUpdater.__hackReadOnlyFieldName(opts, fieldName, 'master');
                        _.each(fieldName, name => fieldAttribute[name] = {readOnly: !!opts.status});
                        dataUpdater.__setFieldAttrbute(fieldAttribute);
                    } else if(opts.dataIndex === 'all') {
                        let tmp = {};
                        _.each(fieldName, name => {
                            tmp[name] = {forceReadOnly: !!opts.status};
                        })
                        dataUpdater.__setDetailFieldAttribute({[opts.objApiName]: tmp});
                    } else {
                        _.each(_.isArray(opts.dataIndex) ? opts.dataIndex : [opts.dataIndex], rowId => {
                            let tmp = (detailRowIdFieldAttribute[rowId] || (detailRowIdFieldAttribute[rowId] = {}));
                            tmp.readOnly || (tmp.readOnly = {});
                            _.each(dataUpdater.__hackReadOnlyFieldName(opts, fieldName, rowId), name => {
                                tmp.readOnly[name] = !!opts.status;
                            })
                        })
                    }
                },

                __hackReadOnlyFieldName(opts, fieldName, rowId) {
                    if(opts.operateId) {
                        let attr = me.get('_pluginSetReadOnlyAttrs') || {};
                        let _fl = attr[opts.operateId + rowId] || [];
                        if(!opts.status) {
                            let myF;
                            if(rowId === 'master') {
                                myF = (me.excuteMasterRule(dataGetter.getMasterData())).readonly_field || [];
                            } else {
                                myF = (me.excuteMDRule({objectApiName: opts.objApiName, recordType: opts.recordType, data: dataGetter.getData('', rowId)}) || {}).readonly_field || [];
                                _.each((me.get('mdFieldAttrStatus') || {})[opts.objApiName], (cc, f) => cc.readOnly && myF.push(f));
                            }
                            myF = _.union(myF, (me.get('_pluginCommonSetReadOnlyAttrs') || {})[rowId] || []);
                            fieldName = _.difference(_fl, myF);
                        } else {
                            attr[opts.operateId + rowId] = _.union(_fl, fieldName);
                            me.set('_pluginSetReadOnlyAttrs', attr);
                        }
                    } else {
                        let cattr = me.get('_pluginCommonSetReadOnlyAttrs') || {};
                        if(opts.status) {
                            cattr[rowId] = _.union(fieldName, cattr[rowId] || []);
                        } else if(cattr[rowId]) {
                            cattr[rowId] = _.difference(cattr[rowId], fieldName);
                        }
                        me.set('_pluginCommonSetReadOnlyAttrs', fieldName)
                    }

                    return fieldName;
                },

                setRequired(opts) {
                    dataUpdater.__setFieldStatus(opts, 'required');
                },

                setHidden(opts) {
                    dataUpdater.__setFieldStatus(opts, 'hidden');
                },

                // todo 好丽友专用，他人慎用，此处务必优化 @wujing
                setOptions(key, value) {
                    me.set(key, value);
                    me.change(key, value);
                },

                __setFieldStatus(opts, status) {
                    let fieldName = opts.fieldName;
                    if(!opts.dataIndex) {//主对象
                        let fieldAttribute = {};
                        _.each(fieldName, a => fieldAttribute[a] = {[status]: !!opts.status});
                        dataUpdater.__setFieldAttrbute(fieldAttribute);
                        return;
                    }

                    _.each(_.isArray(opts.dataIndex) ? opts.dataIndex : [opts.dataIndex], rowId => {
                        let tmp = (detailRowIdFieldAttribute[rowId] || (detailRowIdFieldAttribute[rowId] = {}));
                        tmp[status] || (tmp[status] = {});
                        _.each(fieldName, name => {
                            tmp[status][name] = !!opts.status;
                        })
                    })
                },

                __setFieldAttrbute(fieldAttribute) {
                    _.each(fieldAttribute, (a, fieldName) => (_fieldAttribute[fieldName] = _.extend(_fieldAttribute[fieldName] || {}, a)));
                },

                __setDetailFieldAttribute(detailFieldAttribute) {
                    _.each(detailFieldAttribute, (a, objApiName) => {
                        let tmp = _detailFieldAttribute[objApiName];
                        if(tmp) {
                            _.each(a, (b, fieldName) => {
                                tmp[fieldName] = _.extend(tmp[fieldName] || {}, b);
                            })
                        } else {
                            _detailFieldAttribute[objApiName] = {...a};
                        }
                    })
                },

                /**
                 * 
                 * @param {*} // uiResult = {
                //     masterObjectApiName: {....},//要更新的主对象数据
                //     mdApiname1: {
                //         a: [],
                //         //根据rowId找到对应数据进行更新 ，如果u里找不到rowId代表删除 比如mdApiname1对象有三条数据[{rowId: 'rowId1',...}, {rowId: 'rowId2',...}, {rowId: 'rowId3',...}]
                //         //此时u对应的仅有 rowId1 rowId2 , 则代表需要删除 rowId3这条数据 如果u:{} 则代表删除全部数据
                //         u: {rowId1: {....}, rowId2:{....}}
                //     },
                //     mdApiname2: {...}
		        // } 
                 */
                updateByUIEventResult(uiResult) {
                    if(!uiResult) return;
                    dataUpdater.__setFieldCheckCalculationConfig({uiResult});

                    uiResult.data &&(_.each(uiResult.data, (data, apiName) => { //合并ui事件的数据
                        if (apiName === masterObjApiName) {
                            dataUpdater.updateMaster(data);
                        } else {
                            _.each(data.a, b => {
                                b.object_describe_api_name = apiName;
                            })
                        
                            const u = data.u;
                            u && _.each(dataGetter.getDetail(apiName), item => {
                                const rowId = item.rowId;
                                if (u[rowId]) {
                                    dataUpdater.updateDetail(apiName, rowId, u[rowId]);
                                } else {
                                    dataUpdater.del(apiName, rowId);
                                }
                            })

                            dataUpdater.add(data.a);
                        }
                    }), delete uiResult.data);

                    uiResult.detailFieldAttribute && (dataUpdater.__setDetailFieldAttribute(uiResult.detailFieldAttribute), delete uiResult.detailFieldAttribute);
                    uiResult.fieldAttribute && (dataUpdater.__setFieldAttrbute(uiResult.fieldAttribute), delete uiResult.fieldAttribute);
                    uiResult.detailButton && (pluginService.UI.toggleDetailButton(uiResult.detailButton), delete uiResult.detailButton);

                    uiEventAttr = _.extend(uiEventAttr || {}, uiResult);
                },

                updateTreeExpend(status, objApiName) {
                    me.updateTreeConfig({expend: status}, objApiName);
                }
            }
            let bizApi = {
                triggerMasterButton(action) {
                    me.toggleCommonButton([action], 'trigger');
                },
                toggleMasterButton(actions, status) {
                    me.toggleCommonButton(actions, status);
                },
                toggleDetailButton(detailButton) {
                    if(pluginService.__detailButton) {
                       _.each(detailButton, (buttons, objApiName) => {
                            let tt = pluginService.__detailButton[objApiName];
                            if(tt) {
                                _.each(buttons, btn => {
                                    let ttt = _.findWhere(tt, {record_type: btn.record_type});
                                    if(ttt) {
                                        [].push.apply(ttt.buttons, btn.buttons);
                                    } else {
                                        tt.push(btn);
                                    }
                                })
                            } else {
                                pluginService.__detailButton[objApiName] = buttons;
                            }
                       }) 
                    } else {
                        pluginService.__detailButton = detailButton;
                    }
                },
                hideDetailsComp(objApiName, recordType = 'all') {
                    let tt = pluginService.__detailRecordType || (pluginService.__detailRecordType = {});
                    (tt[objApiName] || (tt[objApiName] = {}))[recordType] = {hidden: true};
                },
                showDetailsComp(objApiName, recordType = 'all') {
                    let tt = pluginService.__detailRecordType || (pluginService.__detailRecordType = {});
                    (tt[objApiName] || (tt[objApiName] = {}))[recordType] = {hidden: false};
                },
                _tc(fieldNames, objApiName, recordType = '', status) {
                    let tt = {};
                    _.each(fieldNames, fieldName => {
                        tt[recordType + fieldName] = {
                            customHidden: status
                        }
                    })
                    dataUpdater.__setDetailFieldAttribute({[objApiName]: tt});
                },
                hideColumns(fieldNames, objApiName, recordType) {
                    bizApi._tc(fieldNames, objApiName, recordType, true);
                },

                showColumns(fieldNames, objApiName, recordType) {
                    bizApi._tc(fieldNames, objApiName, recordType, false);
                },
                //隐藏/显示指定从对象
                toggleMDStatus(objApiName, status) {
                    me._toggleMDStatus({[objApiName]: { hidden: !status }});
                },

                setTrsCss(rowId, css) {
                    let tt = dataGetter.getData('', rowId);
                    if(tt) {
                        let __trCss = tt.__trCss;
                        if(css) {
                            __trCss = _.extend(__trCss || {}, css)
                        } else {
                            _.each(__trCss, (a, k) => __trCss[k] = '');
                        }
                        __trCss && dataUpdater.updateDetail(tt.object_describe_api_name, rowId, {__trCss});
                    }
                },

                runMDDelService(rowIds) {
                    let delDatas = [];
                    _.each(rowIds, rowId => {
                        let item = dataGetter.getData('', rowId);
                        item && delDatas.push(item);
                    });
                    me.runMDDelService({
                        objApiName: delDatas[0] && delDatas[0].object_describe_api_name,
                        delDatas
                    }, pluginService)
                },

                runMDEditService(param) {
                    me.runMDEditService(param);
                },

                batchPickData(opts) {
                    let {fieldName, objApiName, recordType} = opts;
                    let field = dataGetter.getFieldAttr(fieldName, objApiName);
                    pluginService.objApiName = objApiName;
                    pluginService.recordType = recordType;
                    pluginService.lookupField = field;
                    pluginService.finallyCallback = opts.finallyCallback;
                    
                    pluginService.pickData = opts.pickData || function() {
                        return {
                            then(resolve) {
                                let masterData = dataGetter.getMasterData();
                                var masterFields = dataGetter.getDescribe(me.get('apiname')).fields;
                                let hideAdd;
                                let formFillData = {};
                                let objectData = _.extend({}, me.getLookupData(), me.getRowBasicData(objApiName, recordType));
                                _.find(field.wheres, (a) => {
                                    _.find(a.filters, (b) => {
                                        if(b.value_type == 8) { //五角关系屏蔽新建按钮
                                            hideAdd = true;
                                            return hideAdd;
                                        }
                                        let fv = b.field_values[0];
                                        let arr = fv && fv.split && fv.split('__r.');
                                        if(!arr || !arr[1]) return;
                                        let tf = arr[1].replace('$', '');
                                        if(masterFields[tf]) { //是四角关系，带入值
                                            formFillData[b.field_name] = masterData[tf];
                                            formFillData[b.field_name + '__r'] = masterData[tf + '__r'];
                                        }
                                    })
                        
                                    return hideAdd;
                                })
                        
                                CRM.api.pick_data(_.extend({
                                    hideAdd,
                                    formFillData,
                                    filters: opts.filters,
                                    apiName: field.target_api_name,
                                    target_related_list_name: field.target_related_list_name,
                                    single: false,
                                    zIndex: (me.get('fullZIndex') || me.get('zIndex') || 1000) + 1,
                                    methods: {
                                        select(res) {
                                            resolve({
                                                addDatas: _.map(res.selected, (a) => {
                                                    return {
                                                        [field.api_name]: a.id,
                                                        [field.api_name + '__r']: field.is_open_display_name ? a.display_name || '--' : a.name
                                                    }
                                                }),
                                                lookupData: res.selected 
                                            })
                                        }
                                    },
									pluginContext: pluginService
                                }, pluginService.pickConfig, {
                                    beforeRequest(rq) {
                                        var sq = JSON.parse(rq.search_query_info);
                                        sq.wheres = field.wheres;
                                        rq.search_query_info = JSON.stringify(sq);
                        
                                        rq.master_data = masterData;
                                        rq.object_data = _.extend(objectData, rq.object_data);
                                        rq.details = me.getDetailsLookup(objApiName, objectData);
                                        
                                        if(pluginService.pickConfig && pluginService.pickConfig.beforeRequest) {
                                            return pluginService.pickConfig.beforeRequest(rq);
                                        }
                                        return rq;
                                    }
                                }))
                            }
                        }
                    }
                    me.runBatchAddService({
                        pluginContext: pluginService
                    })
                },

                triggerUIEvent(opts = {}) {
                    return new Promise(resolve => {
                        if(opts.skip) return resolve();

                        let param = {
                            noLoading: opts.noLoading,
                            loadingText: opts.loadingText,
                            noRetry: opts.noRetry,
                            triggerField: opts.triggerField || pluginService.fieldName,
                            beTriggerFields: opts.beTriggerFields || pluginService.changeFields,
                            delDatas: opts.delDatas || pluginService.delDatas,
                            newRowId: opts.newDataIndexs || pluginService.newDataIndexs,
                            editRowId: opts.dataIndex || (pluginService.dataIndex && pluginService.dataIndex[0]),
                            objApiName: opts.objApiName || pluginService.objApiName,
                            masterData: opts.masterData || dataGetter.getMasterData(),
                            details: opts.details || dataGetter.getDetails(),
                            event: opts.event,
                            pluginService: pluginService
                        };

                        if(opts.operateType === 'mdEdit') {
                            param.newRowId = param.delDatas = void 0;
                        }
                        if(opts.operateType === 'mdDel') {
                            param.newRowId = void 0;
                        }

                        me.uiEventService(param).then((res = {}) => {
                            if(res.StatusCode) {
                                pluginService.__errorStatus = true;
                            }
                            opts.noMerge || (res.Value && dataUpdater.updateByUIEventResult(_.extend({}, res.Value)));
    
                            resolve(res);
                        })
                    })
                },

                //触发计算
                /*ps.triggerCal({
                    noLoading: true,//计算时不显示loading,一般不用传，这是底层阻断用的，去掉可能会有数据并发问题
                    loadingText： 'xxxx',//底层计算时会默认增加loaing文字 '计算中..',如果传了，可以替换为你的文字提示
                    noRetry： false，//一般不用传，当计算接口超时时，底层会根据此参数决定是否让用户重试，当为true时，重试的机制会被取消
                    changeFields: ['price', 'num'],//表示此次计算是因为修改了price 和 num引起的，必须传，底层需要根据它去寻找那些字段需要计算
                    filterFields： {SaleOrderProductObj: ['price']},//一般不用传//底层根据changeFields计算出需要计算的字段，此参数用于对需要计算的字段进行过滤 //过滤掉订单产品的price字段 代表此次不计算
                    extraFields： {SalesOrderObj: ['discount'], selef__obj: ['field_num']}，//一般不用传，当底层分析出来的需要计算的字段不满足业务， 可以用它添加额外需要计算的字段，
                    calType： 'mdEdit', // 'masterEdit(主对象数据编辑)|mdEdit(从对象数据编辑)||mdCopy(从对象复制)||mdDel(从对象删除)|mdAdd(从对象添加)'
                    modifiedRowIdList: ['rowId1', 'rowId2'], //修改的从对象数据 rowId, 当calType为 mdEdit时需要传
                    objApiName： 'AccountObj',//当前发起计算的对象
                    masterData： '', //主对象数据，一般不用传，插件上下文获取
                    details： {...},//从对象数据，一般不用传，底层获取
                    parseParam： function() {}, //发起计算之前，可以拦截请求参数，再次进行修改
                })**/
                triggerCal(opts = {}) {
                    return new Promise(resolve => {
                        if(opts.skip) return resolve();

                        let param = {
                            noLoading: opts.noLoading,
                            loadingText: opts.loadingText,
                            noRetry: opts.noRetry,
                            changeFields: opts.changeFields || pluginService.changeFields,
                            filterFields: opts.filterFields || pluginService.filterFields,
                            extraFields: opts.extraFields || pluginService.extraFields, //额外计算的字段 ['discount'] || {SalesOrderObj: ['discount'], selef__obj: ['field_num']}
                            calType: opts.operateType || pluginService.operateType,
                            modifiedRowIdList: opts.dataIndex || pluginService.dataIndex,
                            objApiName: opts.objApiName || pluginService.objApiName,
                            masterData: opts.masterData || dataGetter.getMasterData(),
                            details: opts.details || dataGetter.getDetails(),
                            parseParam: opts.parseParam || pluginService.parseParam,
                            delDatas: opts.delDatas || pluginService.delDatas,
                            pluginService: pluginService
                        };

                        me.calculateService(param).then((res) => {
                            if(res && res.StatusCode) {
                                pluginService.__errorStatus = true;
                            }
                            opts.noMerge || (res && res.Value && dataUpdater.updateByCalResult(res.Value.calculateResult));
                            resolve(res);
                        })
                    })
                },

                /*发起计算和UI事件
                ps.triggerCalAndUIEvent({
                    noLoading: true,//计算时不显示loading,一般不用传，这是底层阻断用的，去掉可能会有数据并发问题
                    loadingText： 'xxxx',//底层计算时会默认增加loaing文字 '计算中..', 如果传了，可以替换为你的文字提示
                    noRetry： false，//一般不用传，当计算接口超时时，底层会根据此参数决定是否让用户重试，当为true时，重试的机制会被取消
                    changeFields: ['price', 'num'],//表示此次计算是因为修改了price 和 num引起的，必须传，底层需要根据它去寻找那些字段需要计算
                    filterFields： {SaleOrderProductObj: ['price']},//一般不用传//底层根据changeFields计算出需要计算的字段，此参数用于对需要计算的字段进行过滤 //过滤掉订单产品的price字段 代表此次不计算
                    extraFields： {SalesOrderObj: ['discount'], selef__obj: ['field_num']}，//一般不用传，当底层分析出来的需要计算的字段不满足业务， 可以用它添加额外需要计算的字段，
                    objApiName： 'AccountObj',//当前发起计算的对象
                    masterData： '', //主对象数据，一般不用传，插件上下文获取
                    details： {...},//从对象数据，一般不用传，底层获取
                    parseParam： function() {}, //发起计算之前，可以拦截请求参数，再次进行修改

                    skip: false, //当为true时会跳过UI事件的执行
                    noMerge: false,//当为true时底层不合并计算回来的数据
                    dataIndex： ['rowId1', 'rowId2'], //修改的从对象数据的rowId, 如果你当前的计算是因为修改了从对象数
                    newDataIndexs：['rowId1', 'rowId2'], //新加的从数据rowId，如果你发起计算是因为添加了从对象数据，需要传
                    delDatas: [{....}]  //删除的从对象数据，如果你当前发起计算时因为删除了从对象数据。因为底层details已经没有删掉的数据，需要完整传过来，给server用
                })**/
                triggerCalAndUIEvent(opts = {}) {
                    if(!opts.operateType) { //推断出操作类型
                        if(opts.newDataIndexs) {
                            opts.operateType = 'mdAdd';
                            opts.dataIndex || (opts.dataIndex = opts.newDataIndexs);
                        } else if(opts.delDatas) {
                            opts.operateType = 'mdDel'
                        } else if(opts.dataIndex) {
                            opts.operateType = 'mdEdit'
                        } else if(opts.objApiName === masterObjApiName) {
                            opts.operateType = 'masterEdit'
                        }
                    } 
                    
                    let myCalRst;
                    return pluginService.triggerCal(opts).then((calRst = {}) => {
                        myCalRst = calRst;
                        opts.skip = calRst.StatusCode;
                        return this.triggerUIEvent(opts);
                    }).then((uiRst = {}) => {
                        return Promise.resolve(myCalRst.StatusCode || uiRst.StatusCode ? {StatusCode: 1} : {calRst: myCalRst.Value && myCalRst.Value.calculateResult, uiRst: uiRst.Value});
                    })
                },

                //判断是否有过修改
                hasChange() {
                    return __addStatus || pluginService.__detailButton || pluginService.__detailRecordType || !_.isEmpty(_updatedData) || !_.isEmpty(_delDatas) || !_.isEmpty(_insertDatas) || !_.isEmpty(_masterUpdate) || !_.isEmpty(_fieldAttribute) || !_.isEmpty(_detailFieldAttribute) || !_.isEmpty(detailRowIdFieldAttribute) || !_.isEmpty(uiEventAttr) || !_.isEmpty(_rowId)
                },

                //收集整个插件运行期间的所有数据变更 属性变更
                collectChange() {
                    let ddm = {};
                    let __historyRowId = {..._rowId};
                    _.each(opts.details || me.getDetails(), (arr, k) => {
                        let di = {}
                        _.each(arr, item => {
                            if(_rowId[item.rowId]) {
                                item.rowId = _rowId[item.rowId];
                            }
                            di[item.rowId] = true;
                        })
                        ddm[k] = di;
                    })
                    _rowId = {};

                    let mdAdd = []
                    _.each(dataGetter.getDetails(), (arr, apiname) => {
                        if(ddm[apiname]) {
                            _.each(arr, data => {
                                ddm[apiname][data.rowId] || mdAdd.push(data);
                            })
                        } else {
                            mdAdd.push(...arr);
                        }
                    });

                    mdAdd = _.union(mdAdd);

                    let mdInsert = [];
                    let __insert = {};
                    _.each(_insertDatas, a => {
                        let insertDatas = [], insertRowData = dataGetter.getData('', a.insertRow);
                        if(!insertRowData) return;
                        _.each(a.rowIds, rowId => {
                            let tt = _.findWhere(mdAdd, {rowId: rowId});
                            if(tt) {
                                insertDatas.push(tt);
                                __insert[tt.rowId] = true;
                                mdAdd.splice(_.indexOf(mdAdd, tt), 1);
                            }
                        })
                        mdInsert.push({
                            isBefore: a.isBefore,
                            datas: insertDatas,
                            insertRow: insertRowData
                        })
                    })

                    let mdUpdate = {};
                    _.each(_updatedData, (a, rowId) => {
                        if(_.findWhere(_delDatas, {rowId: rowId}) || _.findWhere(mdAdd, {rowId: rowId}) || __insert[rowId]) return;
                        mdUpdate[rowId] = a;
                    })
                    
                    return {
                        masterUpdate: _masterUpdate,
                        mdDel: _delDatas,
                        mdAdd,
                        mdUpdate,
                        mdInsert,
                        mdMove: _moveDatas,
                        detailRowIdFieldAttribute,
                        fieldAttribute: _fieldAttribute,
                        detailFieldAttribute: _detailFieldAttribute,
                        mdSort: dataGetter.__getSort(),
                        __detailRecordType: pluginService.__detailRecordType,
                        detailButton: pluginService.__detailButton,
                        relatdDatas,
                        __historyRowId,
                        ...uiEventAttr
                    }
                },

                //通用插件动作 计算 事件 结束
                commonAction(error, opts = {}) {
                    if(error || pluginService.__errorStatus) return pluginService.end(error);
                    pluginService.triggerCal({skip: opts.skipCal}).then((rst = {}) => {
                        if(rst.StatusCode) return pluginService.end(true);
                        let param = {
                            calData: rst.Value && rst.Value.calculateResult
                        }

                        pluginService.opType = pluginService._isBatchAdd ? 'batchAdd' : pluginService.operateType ? pluginService.operateType.replace('md', '').toLowerCase(): '';
                        pluginService.calRst = param.calData;

                        pluginService.run('form.uiEvent.before').then(rst => {
                            pluginService.triggerUIEvent({skip: opts.skipUIEvent}).then((rst = {}) => {
                                if(!rst.StatusCode && opts.beforeEndPlugin) {
                                    param.uiData = rst.Value;
                                    pluginService.uiAndCalRst = param;
                                    delete pluginService.parseParam;//删除parseParam
                                    pluginService.run(opts.beforeEndPlugin).then((rst = {}) => {
                                        pluginService.end(rst.StatusCode);
                                    })
                                } else {
                                    pluginService.end(rst.StatusCode);
                                }
                            })
                        })
                    })
                },

                //获取指定从对象数据的通用信息 包含默认值 业务类型 rowId
                getRowBasicData(apiName, recordType) {
                    return me.getRowBasicData(apiName || pluginService.objApiName, recordType || pluginService.recordType);
                },

                //判断某个字段是否在布局里
                assertInLayouts(fieldName, objApiName, recordType) {
                    return _.contains(pluginService.dataGetter.getLayoutFields(objApiName, recordType), fieldName);
                },

                overUpdate(objectData, details) {
                    pluginService.__overUpdateParam = {objectData, details};
                }
            }

            let pluginService = {
                bizApi,
                dataUpdater,
                dataGetter,
                masterObjApiName,
                formType: me.getFormType(),
                UI: bizApi,

                //插件自身运行其它插件的简写
                run(name, param) {
                    return me.runPlugin(name, param || pluginService, pluginService);
                },

                //插件结束运行 error为true时不触发任何更新
                end(error) {
                    return new Promise(resolve => {
                        if(!error && !pluginService.__errorStatus) {
                            if(pluginService.__overUpdateParam) {
                                me.overUpdate(pluginService.__overUpdateParam.objectData, pluginService.__overUpdateParam.details);
                                pluginService.destroy();
                                resolve();
                                pluginService.finallyCallback && pluginService.finallyCallback();
                            } else {
                                let allChangeData = pluginService.collectChange();
                                pluginService.run('plugin.end.before').then(rst => {
                                    if(!(rst && rst.StatusCode)) {
                                        me.caltaskSuccessHandle(allChangeData);
                                        pluginService.successCallback && pluginService.successCallback();
                                        pluginService.run('plugin.end.after', {
                                            data: Object.assign(allChangeData,
                                                {
                                                    changeData: pluginService.changeData || {}
                                                }
                                            ),
                                            requestId: me.get('requestId')
                                        });

                                        me.trigger('form.data.update.complete');
                                    }

                                    pluginService.destroy();
                                    resolve();
                                    pluginService.finallyCallback && pluginService.finallyCallback();

                                    me.__mergePluginFieldCheck(pluginService.__fieldCheckCalculationConfig);
                                })
                            }
                        } else {
                            pluginService.destroy();
                            resolve();
                            pluginService.finallyCallback && pluginService.finallyCallback(true);
                        }
                    })
                },

                destroy() {}
            }

            _.each(['runMDDelService', 'runMDEditService', 'batchPickData', 'triggerUIEvent', 'triggerCal', 'triggerCalAndUIEvent', 'hasChange', 'collectChange', 'commonAction', 'getRowBasicData', 'assertInLayouts', 'overUpdate'], k => {
                pluginService[k] = function() {
                    return bizApi[k].apply(bizApi, arguments);
                }
            })

            return _.extend(pluginService, opts);
        },
    
        //初始化插件服务
        initPluginService(opts = {}) {
            let params = Object.assign({
                apiName: this.get('apiname'),
                actionCode: this.get('layout_type') === 'edit' ? 'Edit' : 'Add',
                agentType: 'web',
                recordType: this.get('record_type'),
                describe: opts.describe,
                pluginOpts: this.getPluginOpts(),
                formatPluginList: function(pluginList) {
                    pluginList.push(modelPlugin.getPlugin({apiName: this.get('apiname'), ...opts}));
                    //自定义插件安装装置
                    pluginList.push({
                        "pluginApiName": 'PWCPlugin',
                        "params": {
                            _host: 'form'
                        },
                        "resource": function(){
                            return window.PAAS.plugin?.libs.get('PWCPlugin');
                        }
                    });
                    return pluginList;
                }.bind(this)
            }, opts);

            return new Promise(resolve => {
                CRM.api.pluginService.initByInterface(params).then((pluginService) => {
                    if (pluginService) {
                        this.set('pluginService', pluginService);
                        this.on('parseSubmitSuccess', (res, param) => {
                            this.submitSuccess(res, param);
                        })
                        this.on('parseSubmitError', (res, param) => {
                            this.submitError(res, param);
                        })
                        pluginService.run('pwc.init.before', {
                            plugins: this.get('customPlugins')
                        }).then(resolve, resolve);
                    }else {
                        resolve();
                    }
                });
            })
        },

        //运行指定的插件
        runPlugin(name, options, ps) {
            let pluginService = this.get('pluginService');

            // this.addParamForLog(name, options, ps);

            return pluginService ? pluginService.run(name, options) : new Promise(resolve => {resolve()});
        },

        addParamForLog(name, param, ps) {
            if (name !== 'form.log') {
                ps.actionDescribe = ps.actionDescribe || {};
                ps.actionDescribe.action = ps.actionDescribe.action || name;

                ps.run && ps.run('form.log', {
                    actionDescribe: Object.assign({}, ps.actionDescribe, {
                        action: name === 'plugin.end.after' ? `${ps.actionDescribe.action}.DONE` : name
                    }),
                    requestId: this.get('requestId'),
                    data: param || '',
                    apiName: this.get('apiname')
                });
            }
        },

        runFetchDescribeAfterService(describe) {
            let me = this;
            const ps = me.getPluginService({
                objApiName: me.get('apiname'),
                recordType: me.get('record_type'),
                describe
            });
            
            ps.run('fetchdescribe.after').then(() => me.parseDescribe(describe));
        },

        //主表单数据变更 计算完成之后
        runMasterEndPlugin(param) {
            let me = this;
            let flag = me._checkNoNeedPluginService(['form.change.end', 'plugin.end.befor', 'plugin.end.after'])
           
            if(!me.masterChangeEnd && flag) {
                me.trigger('form.data.update.complete');
                return;
            }

            //没有任何插件，则执行主表单数据变更
            if(flag) return this._runMasterEndPlugin(param);

            //当从对象渲染完成之后，如果主表单数据变更计算完成，则需要等待从渲染完成
            //主要解决业务插件监听form.change.end钩子，更新从对象数据，但是从并没有渲染完成，导致更新错误
            //因此从对象未完全渲染完之前，不执行form.change.end插件，把参数先缓存，待从对象渲染完再执行
            if(this._masterEndParams) {
                this._masterEndParams.push(param);
                return;
            }

            //从对象已渲染完成，则执行主表单数据变更
            if(!this._mdRendering) return this._runMasterEndPlugin(param);

            this._masterEndParams = [param];

            let timer = setInterval(() => {
                if(this._mdRendering) return;

                clearInterval(timer);
                let inner = () => {
                    let xparam = this._masterEndParams.pop();
                    if(!xparam) {
                        this._masterEndParams = null;
                        return;
                    };
                    this._runMasterEndPlugin(xparam, inner);
                }
                inner();
            }, 50)
        },

        _runMasterEndPlugin(param, finallyCallback) {
            let me = this;
            const ps = me.getPluginService({
                objApiName: me.get('apiname'),
                recordType: me.get('record_type'),
                operateType: 'masterEdit',
                changeFields: param.keys,
                autoFields: {
                    mc_currency: me.mc_currency_auto
                }
            });

            let _collectChange = ps.collectChange;
            ps.collectChange = function() {
                let mdDel = [], mdUpdate = {}, mdAdd = [];
                _.each(param.mdChangeDatas, a => {
                    _.each(a.mdUpdate, (b, rowId) => {
                        mdUpdate[rowId] = _.extend(mdUpdate[rowId] || {}, b);
                    })
                    _.each(a.mdAdd, b => {
                        mdAdd.push(b);
                    })
                    _.each(a.mdDel, b => {
                        mdDel.push(b);
                    })
                })
                let newDDM = me.mdData2Object(), masterUpdate = {};
                let data = me.get('data');
                let {keys, ddm, uiResult, calResult, blurField} = param;
                _.each(keys, a => {
                    masterUpdate[a] = data[a];
                    if(data[a + '__r'] !== void 0) {
                        masterUpdate[a + '__r'] = data[a + '__r'];
                    }
                })
                if(calResult && calResult[ps.objApiName]) {
                    _.extend(masterUpdate, calResult[ps.objApiName][0]);
                }

                _.each(ddm, (obj, objApiName) => {
                    let tobj = newDDM[objApiName] || {};
                    _.each(obj, a => {
                        if(!_.findWhere(tobj, {rowId: a.rowId})) {//新的里面找不到了，证明是被删除
                            mdDel.push(a);
                        }
                    })
                })

                ddm && _.each(newDDM, (obj, objApiName) => {
                    let tobj = ddm[objApiName] || {};
                    _.each(obj, a => {
                        if(!_.findWhere(tobj, {rowId: a.rowId})) {//旧的里面找不到，证明新加的
                            mdAdd.push(a);
                        }
                    })
                })
                _.each(uiResult, (a, objApiName) => {
                    if(objApiName === ps.objApiName) {//主对象数据更新
                        _.extend(masterUpdate, a);
                    } else {
                        let d = ddm && ddm[objApiName];
                        d && _.each(a.u, (data, index) => {
                            d[index] && !_.isEmpty(data) && (mdUpdate[d[index].rowId] = data);
                        })
                    }
                })
                
                return {masterUpdate, blurField, mdDel, mdAdd, mdUpdate, fieldAttribute: uiResult && uiResult.fieldAttribute, detailFieldAttribute: uiResult && uiResult.detailFieldAttribute};
            }

            ps.run('form.change.end').then(rst => {
                if(rst && rst.StatusCode) {
                    finallyCallback && finallyCallback();
                    return;
                };
                ps.run('plugin.end.before').then(rst => {
                    if((!rst || !rst.StatusCode) && ps.hasChange() && !ps.__errorStatus) {
                        me.caltaskSuccessHandle(_collectChange.call(ps));
                    }
                    me.trigger('form.data.update.complete');
                    finallyCallback && finallyCallback();
                })
    
                let changeData = ps.collectChange();
                this.masterChangeEnd && this.masterChangeEnd(changeData);
    
                ps.run('plugin.end.after', {
                    data: changeData
                });
            })
        },

        //表单渲染之前
        runRenderBeforeService(param) {
            return new Promise((resolve) => {
                const ps = this.getPluginService({
                    objApiName: this.get('apiname'),
                    recordType: this.get('record_type'),
                    BaseComponnets,
                    BaseComponents: BaseComponnets,
                    ...param
                })
                
                ps.run('form.render.before').then((rst = {}) => {
                    if (rst.Value) {
                        rst = rst.Value;
                    }
                    // rst.title = 'xxxxx';
                    // rst.buttons = {
                    //     del: ['saveDraft'],
                    //     add: [{
                    //         action: 'xx',
                    //         label: 'ceshixinz',
                    //         callback(context) {
                    //             context.submitAndSkipValidate();
                    //         }
                    //     }],
                    //     reset: [{
                    //         action: 'submit',
                    //         label: 'yyyy'
                    //     },{
                    //         action: 'cancel',
                    //         label: '取消11',
                    //         callback() {
                    //             alert(111);
                    //         }
                    //     }]
                    // }
                    rst.title && this.set('pluginSetTitle', rst.title);
                    rst.components && this.set('pluginSetComponents', rst.components);
                    if(rst.buttons) {
                        _.each(rst.buttons.add, a => {
                            a.action || (a.action = 'action_' + CRM.util.getUUId())
                            a.callBack && (a.callback = a.callBack)//历史遗留，callBack应为驼峰，但是底层已经适配小写
                        });
                        this.set('pluginButtons', rst.buttons);
                    }
                    rst.isSkipLayoutFieldStateCheck && this.set('isSkipLayoutFieldStateCheck', true);
                    rst.footerSlot && this.set('pluginFooterSlot', rst.footerSlot);
                    (rst.fieldEditBeforeCallbacks || rst.fieldEditBeforeCallBacks) && this.set('fieldEditBeforeCallbacks', rst.fieldEditBeforeCallbacks || rst.fieldEditBeforeCallBacks);
                    (rst.fieldRenderBeforeCallbacks || rst.fieldRenderBeforeCallBacks) && this.set('fieldRenderBeforeCallbacks', rst.fieldRenderBeforeCallbacks || rst.fieldRenderBeforeCallBacks);//用户修改组件初始化参数
                    this.set('fieldChangeBeforeCallBacks', rst.fieldChangeBeforeCallBacks);
                    rst.createDataHandler && this.set('createDataHandler', rst.createDataHandler);
                    rst.updateDataHandler && this.set('updateDataHandler', rst.updateDataHandler);
                    rst.mdSwitchCallBack && this.set('mdSwitchCallBack', rst.mdSwitchCallBack);
                    rst.cancelCallBack && this.set('pluginCancelCallBack', rst.cancelCallBack);
                    rst.submitSuccessCallBack && this.set('submitSuccessCallBack', rst.submitSuccessCallBack);
                    rst.beforeSaveDraft && this.set('beforeSaveDraftCallBack', rst.beforeSaveDraft);
                    rst.duplicateConfig && this.set('duplicateConfig', rst.duplicateConfig)
                    this.set('groupSlots', rst.groupSlots);

                    if(rst.no_support_draft) {
                        let uibuttons = this.get('uibuttons');
                        uibuttons && _.find(uibuttons, (a, index) => {
                            if(a.action === 'Add_Save_Draft' || a.action === 'Edit_Save_Draft') {
                                uibuttons.splice(index, 1);
                                return true;
                            }
                        })
                        this.set('support_draft', false);
                    }

                    if(rst.beforeUIAction) {
                        this.set('beforeFormUIAction', rst.beforeUIAction);
                    }

                    resolve({
                        masterData: _.extend(param.masterData, ps.collectChange().masterUpdate)
                    })

                    //不更新数据
                    ps.end(true);
                })
            })
        },

        //组件渲染之前
        runFieldRenderBeforeService(fieldName, param) {
            let hook = (this.get('fieldRenderBeforeCallbacks') || {})[fieldName];

            return hook ? hook(param) : param;
        },


        hasFieldEditbeforeHooks(fieldName) {
            return (this.get('fieldEditBeforeCallbacks') || {})[fieldName];
        },

        runFieldEditBeforeService(fieldName) {
            let hooks = this.hasFieldEditbeforeHooks(fieldName);
            return new Promise(resolve => {
                if(!hooks) return resolve();
                let index = 0;
                let param = {
                    fieldAttr: this.getFieldAttr(fieldName),
                    data: _.extend({}, this.get('data'))
                }
                let beforeRequests = [];
                let pluginParam = {};
                let inner = function() {
                    let hook = hooks[index++];
                    if(!hook) {
                        if(beforeRequests.length) {
                            pluginParam.beforeRequest = function(param1) {
                                _.each(beforeRequests, fn => {
                                    param1 = fn(param1);
                                })

                                return param1;
                            }
                        }

                        return resolve(pluginParam);
                    }

                    hook(param).then((rst = {}) => {
                        if(rst.StatusCode) return resolve();

                        let vv = rst.Value || rst;
                        if(vv.beforeRequest) {
                            beforeRequests.push(vv.beforeRequest);
                        }
                        _.extend(pluginParam, vv);

                        inner();
                    })
                };

                inner();
            })
        },

        //表单渲染完成后
        runFormRenderAfterService() {
            const ps = this.getPluginService({
                objApiName: this.get('apiname'),
                recordType: this.get('record_type')
            })

            this.trigger('toggleeditstatus');//解除限制编辑的状态

            ps.run('form.render.after').then(rst => {
                ps.end(ps.hasChange() ? rst && rst.StatusCode : true);

                let rv = rst && rst.Value;

                if(rv) {
                    rv.hideNoDatasDetails && this.hideNoDatasDetails();//隐藏没有数据的从对象
                    rv.scrollToField && this.set('scrollToField', rv.scrollToField);
                }

                this.trigger('renderFooterSlot');
                this.trigger('form.render.complete');

                this.trigger('scrolltofield', this.get('scrollToField'));
            })
        },

        //隐藏没有数据的从对象
        hideNoDatasDetails() {
            let allDatas = this.get('allTablesData') || {};
            _.each(this.get('detailObjectList'), des => {
                let tm = allDatas[des.objectApiName] || {};
                _.each(des.layoutList, ly => {
                    let datas = tm[ly.record_type] || [];
                    if(!datas.length) {//没有数据
                        this.triggerMDActionEvent(des.objectApiName, ly.record_type, 'removeRecordTypeHandle');
                    }
                })
            })
        },

        //校验相关对象必填
        validateRelateIsRequire(param) {
            let rdl = (param && param.related_data_list) || {};
            let errors = [];
            _.each(this.get('layout'), a => {
                _.each(a.tabs, b => {
                    if(b.is_required_when_create && b.field_api_name) {
                        let obj = _.findWhere(rdl[b.ref_object_api_name], {relatedFieldName: b.field_api_name});
                        if(!obj || !obj.dataList.length) {
                            errors.push($t('请填写{{label}}', {label: b.header}));
                        }
                    }
                })
            })

            if(errors.length) {
                CRM.util.alert(errors.join('</br>'));
                return false;
            }
            
            return true;
        },

        //数据提交之前
        runBeforeSubmit(param, $event) {
            return new Promise(resolve => {
                if(!this.validateRelateIsRequire(param)) {
                    return resolve({
                        fail: true
                    })
                }

                if(!this.assertUsePlugin()) return resolve({noChange: true});

                const ps = this.getPluginService({
                    objApiName: this.get('apiname'),
                    recordType: this.get('record_type'),
                    buttonAction: $event && $event.buttonAction
                })
                
                ps.run('form.submit.before').then(rst => {
                    let status = {};
                    this.__beforeSubmitParseParam = rst && rst.Value && rst.Value.parseParam;

                    if(rst && rst.StatusCode) {
                        status.fail = true;
                        return resolve(status);
                    }

                    ps.end().then(() => resolve(status));
                })
            })
        },

        runAfterSubmit(res) {
            return new Promise(resolve => {
                const __eventKey = 'form.submit.after';
                const ps = this.getPluginService({resObject: res.Value.objectData, res, __eventKey});
                ps.run(__eventKey).then(rst => {
                    resolve((rst && rst.Value) || {});
                })
            })
        },

        //节流
        runDataChangeService(param) {
            let me = this;
            if(!this.__changeService) {
                this.__changeService = _.debounce(() => {
                    this.__changeService = null;
                    let changeData = {};
                    let oldData = {};
                    let cs = this.__changeServiceParam;
                    let hisData = this.get('__hisData') || {};
                    this.__changeServiceParam = null;
                    let autoFields = {};
                    let isManual;
                    _.each(cs, param => {
                        changeData[param.fieldName] = param.newValue;
                        oldData[param.fieldName] = param.oldValue;
                        if(hisData[param.fieldName + '__r'] !== void 0) {
                            oldData[param.fieldName + '__r'] = hisData[param.fieldName + '__r'];
                        }
                        
                        if(this.__triggerOnloadUIDataResult && this.__triggerOnloadUIDataResult[param.fieldName]) {
                            autoFields[param.fieldName] = true;
                            delete this.__triggerOnloadUIDataResult[param.fieldName];
                        }

                        if(param.exOpts?.isBlur) {
                            isManual = true;
                        }
                    })

                    if(autoFields.mc_currency !== void 0) {
                        autoFields.mc_currency = me.mc_currency_auto;
                    }
                    const __eventKey = 'form.dataChange.after';
                    const ps = this.getPluginService({changeData, oldData, autoFields, isManual, __eventKey});
                    ps.run(__eventKey).then(rst => {
                        me._dataChangePlugining = false;
                        let masterUpdate, tmp;
                        try {
                            if(!ps.__errorStatus && ps.collectChange) {
                                tmp = ps.collectChange();
                            
                                masterUpdate = tmp.masterUpdate;
                                tmp.masterUpdate = {};
                                let flag = me._masterCalculating;
                                me._masterCalculating = false; //避免从数据检测到该值，阻止更新
                                me.newBatchUpdate(masterUpdate, true, true, true);
                                me.caltaskSuccessHandle(tmp);
                                me._masterCalculating = flag;
                            }
                        } catch(e) {}

                        ps.end(true);

                        if(tmp && (ps.filterFields || ps.extraFields)) {
                            tmp.exCalParam = {
                                filterFields: ps.filterFields,
                                extraFields: ps.extraFields
                            }
                        }

                        if(tmp && rst && rst.Value && rst.Value.parseParam) {
                            tmp.parseParam = rst.Value.parseParam;
                        }

                        _.each(cs, param => {
                            let {exOpts} = param;
                            exOpts.noCalculate || this.triggerCalculate(exOpts.key, exOpts.isBlur, _.isEmpty(masterUpdate) ? void 0 : masterUpdate, tmp);
                        })
                    })
                }, 20)
            }

            me._dataChangePlugining = true;

            (this.__changeServiceParam || (this.__changeServiceParam = [])).push(param)
            this.__changeService();
        },

        runMDRenderBeforeService(param) {
            var me = this;
            var sss = this;
            return new Promise(resolve => {
                let apinames = [...this.getMDApiNames()];
                let config = {};
                let attributes = [];
                const _inner = () => {
                    if(!apinames.length) {
                        me.set('__mdRenderBeforeAttributes', attributes);
                        return resolve(config);
                    }
                    const ps = this.getPluginService({
                        objApiName: apinames.pop()
                    })
                    ps.run('md.render.before').then((rst = {}) => {
                        let obj = ps.collectChange();
                        obj.masterUpdate && me.newBatchUpdate(me._puerObjectReferenceData(obj.masterUpdate, me.get('fields')), true);//更新主对象
                        
                        me.updateFormRuleRowId(obj.__historyRowId);

                        let allDatas = me.get('allTablesData') || {};
                        _.each(obj.mdMove, function(item) {
                            //先删除
                            (obj.mdDel || (obj.mdDel = [])).push(...item.datas);
            
                            //再插入
                            (obj.mdInsert || (obj.mdInsert = [])).push(item)
                        })
                        _.each(obj.mdAdd, a => {
                            let tt = allDatas[a.object_describe_api_name] || (allDatas[a.object_describe_api_name] = {});
                            if(tt[a.record_type]) {
                                tt[a.record_type].push(a)
                            } else {
                                (tt[a.record_type] = []).push(a);
                            }
                        })
                        _.each(obj.mdDel, a => {
                            let tt = allDatas[a.object_describe_api_name];
                            let ct = tt && tt[a.record_type];
                            if(ct) {
                                let nn = _.filter(ct, ca => ca.rowId !== a.rowId);
                                ct.splice(0, ct.length, ...nn);
                            }
                        })
                        _.each(obj.mdUpdate, (a, rowId) => {
                            _.find(allDatas, item => {
                                return _.find(item, arr => {
                                    return _.find(arr, ca => {
                                        if(ca.rowId === rowId) {
                                            _.extend(ca, a);
                                            return true
                                        }
                                    })
                                })
                            })
                        })
                        _.each(obj.mdInsert, a => {
                            let rowId = a.insertRow.rowId;
                            _.find(allDatas, item => {
                                return _.find(item, arr => {
                                    return _.find(arr, (ca, index)=> {
                                        if(ca.rowId === rowId) {
                                            arr.splice(a.isBefore ? index : index + 1, 0, ...a.datas)
                                            return true
                                        }
                                    })
                                })
                            })
                        })

                        _.each(obj.mdSort, (item, objApiName) => {
                            _.each(item, (rowIds, rt) => {
                                let datas = allDatas[objApiName] && allDatas[objApiName][rt];
                                if(!datas || datas.length !== rowIds.length) return;
                                let nn = [];
                                _.each(rowIds, rowId => {
                                    let n = _.findWhere(datas, {rowId});
                                    if(n) {
                                        nn.push(n);
                                    }
                                })
                                datas.splice(0, datas.length, ...nn);
                            })
                        })

                        if(obj.__detailRecordType) {
                            if(rst.Value) {
                                rst.Value.detailRecordType = obj.__detailRecordType;
                            } else {
                                rst.Value = {
                                    detailRecordType: obj.__detailRecordType
                                }
                            }
    
                        }

                        let _renders = {};
                        if(rst.Value && _.isArray(rst.Value.columnRenders)) {//把它解析为之前定义的格式
                            let _fns = {};
                            _.each(rst.Value.columnRenders, fieldRenders => {
                                _.each(fieldRenders, (render, fieldName) => {
                                    if(!_renders[fieldName]) {
                                        _renders[fieldName] = {
                                            depend_fields: [],
                                            render(cellValue, trData, column) {
                                                _.each(_fns[fieldName], fn => {
                                                    cellValue = fn(cellValue, trData, column);
                                                })
                                                return cellValue;
                                            },
                                            actionCallBacks: {}
                                        };
                                    }
                                    let dps = _renders[fieldName].depend_fields;
                                    (_fns[fieldName] || (_fns[fieldName] = [])).push(render.render || render);
                                    if(render.depend_fields) {
                                        Array.prototype.push.apply(dps, render.depend_fields);
                                    }
                                    _.extend(_renders[fieldName].actionCallBacks, render.actionCallBacks);
                                })
                            })
                            // rst.Value.columnRenders = {
                            //     common: _renders
                            // }
                        }

                        rst.Value && _.each(rst.Value.fakeFields, a => {
                            _.each(a.fields, b => {
                                if(b.depend_fields) {
                                    _renders[b.api_name] = {
                                        depend_fields: b.depend_fields,
                                        render: b.render
                                    }
                                }
                            })
                        })

                        if(!_.isEmpty(_renders)) {
                            rst.Value.columnRenders = {
                                common: _renders
                            }
                        }

                        attributes.push(_.pick(obj, ['detailRowIdFieldAttribute', 'detailFieldAttribute', 'fieldAttribute']));

                        // rst.Value = {
                        //     headerSlot: [wrapper => {
                        //             var instance = FxUI.create({
                        //                 wrapper: wrapper,
                        //                 template: '<div><fx-button @click="handleUpdate">排序</fx-button><fx-button @click="handAdd">设置单行文本只读</fx-button></div>',
                        //                 data() {
                        //                     return {
                        //                         input1: '',
                        //                         flag: true
                        //                     }
                        //                 },
                        //                 methods: {
                        //                     handleUpdate() {
                        //                         let ps = sss.getPluginService();
                        //                         ps.dataUpdater.sort('object_y6JkA__c', 'default__c', (details) => {
                        //                             let arr = _.shuffle(details);
                        //                             console.log(arr);
                        //                             return arr;
                        //                         })
                        //                         // ps.dataUpdater.setReadOnly({
                        //                         //     objApiName: 'object_7MGJr__c',
                        //                         //     fieldName: ['name'],
                        //                         //     dataIndex: 'all',
                        //                         //     status: true
                        //                         // })
                        //                         ps.end();
                        //                     },

                        //                     handAdd() {
                        //                         let ps = sss.getPluginService();
                        //                         let details = ps.dataGetter.getDetail('object_7MGJr__c');
                        //                         ps.dataUpdater.setReadOnly({
                        //                             objApiName: 'object_7MGJr__c',
                        //                             fieldName: ['field_kaxdu__c', 'field_gf15m__c'],
                        //                             dataIndex: details[0].rowId,
                        //                             status: true
                        //                         })
                        //                         ps.end();
                        //                     }
                        //                 }
                        //             })
                        //     }]
                        // }
                        // if(ps.objApiName === 'object_c1YBm__c') {
                        //     rst.Value = {
                        //         beforeMDUpdate: [(res) => {}, (res) => {}],//方法内部不能有任何异步，也不能在通过插件修改数据
                        //         filterBatchEditFields: ['price', 'discout'],//过滤掉不需要批量编辑的字段
                        //         //隐藏字段
                        //         hideFields: {
                        //             common: ['name'],
                        //             default__c: ['field_O4e6T__c']
                        //         },
                        //         fakeFields: [{
                        //             pos: 'after', //默认after 可不传
                        //             field_name: 'name',
                        //             fields: [{ //fields里 的api_name不可重复，为避免和预置的字段冲突，最好以__fake结尾，不过不强求。
                        //                 api_name: 'xxxxy__fake',
                        //                 data: 'xxxxy__fake',
                        //                 fixed: true, //如果此属性为true,会将此列固定到列表前面，不在name后面插入，比较适用icon列之类需要固定到前面的列，
                        //                 width: 56,
                        //                 is_readonly: true,
                        //                 label_html: '<em class="ico-tit-rel ico-tit-rel-productobj">*****************</em>', //跟label的区别是：label会进行转义后再显示，labelHtml可保留元素，不转义。
                        //                 render: function(cellValue, cellType, trData) { //可自定义该列的render
                        //                     return `<span>${trData.xxxxy__fake}</span>`
                        //                 },
                        //                 actionCallBacks: {
                        //                     accountobj_history() {
                        //                         alert(1)
                        //                     },
                        //                     accountobj_history2() {
                        //                         alert(22)
                        //                     }
                        //                 }
                        //             }]
                        //         }],
                        //         buttons: {
                        //             reset: {
                        //                 field_v1A0B__c: {
                        //                     label: 'xx',
                        //                     callBack() {
                        //                         let ps = sss.getPluginService();
                        //                         ps.batchPickData({
                        //                             objApiName: 'object_c1YBm__c',
                        //                             recordType: 'default__c',
                        //                             fieldName: 'field_v1A0B__c'
                        //                         })
                        //                     }
                        //                 }
                        //             }
                        //         },
                        //         operateBtns: [function() {
                        //             return {
                        //                 add: [{
                        //                     label: '测试1',
                        //                     action: 'test_handle',
                        //                     callBack(trData) {
                        //                         alert(trData.name)
                        //                     }
                        //                 },{
                        //                     label: '测试1',
                        //                     action: 'test_handle',
                        //                     callBack(trData) {
                        //                         alert(trData.name)
                        //                     }
                        //                 }],
                        //             }
                        //         }],
                        //         columnRenders: {
                        //             common: {
                        //                 name() {
                        //                     return 'name'
                        //                 },
                        //                 field_kh2rB__c() {
                        //                     return 'xxxxxxxxxx'
                        //                 },
                        //             },
                        //             default__c: {
                        //                 name(formatVal, trData) {
                        //                     console.log(...arguments);
                        //                     return 'name_default__c'
                        //                 }
                        //             }
                        //         },
                        //         columnWidthConfig: {
                        //             name: 400,
                        //             field_v1A0B__c: 400
                        //         },
                        //         tableOptions: {
                        //             isDrag: false,
                        //             showHelpTip: false,
                        //             disabledcfg: {},
                        //             autoHeight: '',
                        //             maxHeight: '',
                        //             termBatchPos: '',
                        //             renderComplete(instance) {
                        //                 // instance.check();
                        //                 // instance.unCheck();
                        //                 // instance.getCheckDatas()
                        //                 instance.on('check.change', checkDatas => {
                        //                     alert(checkDatas.length);
                        //                 })
                        //             }
                        //         },
                        //         tableFooterSlot: {
                        //             common(wrapper, list) {
                        //                 $(wrapper).html(`<div>数据数量:<span>${list.length}</span></div>`);
                        //                 return {
                        //                     destroy() {},
                        //                     update(list) {
                        //                        $(wrapper).find('span').text(list.length);
                        //                     }
                        //                 }
                        //             },
                        //             default__c(wrapper, list) {
                        //                 $(wrapper).html(`<div>金额合计:<span>${list.reduce((total, a) => total + a.field_ufYgj__c, 0)}</span><div>`);
                        //                 return {
                        //                     destroy() {},
                        //                     update(list) {
                        //                         $(wrapper).find('span').text(list.reduce((total, a) => total + a.field_ufYgj__c, 0));
                        //                     }
                        //                 }
                        //             }
                        //         },
                        //         layoutSlot($wrapper) {
                        //             $wrapper.html('<div>测试1123213</div>');
                        //             return {
                        //                 destroy() {

                        //                 },
                        //                 getValue() {
                        //                     return [{name: 11}]
                        //                 }
                        //             }
                        //         },
                        //         footerSlot: [(wrapper) => {
                        //             $(wrapper).html('zhesxxxxxxxxxxxxxxxxxxxxxxxxxxxxx');
                        //             return {
                        //                 destroy() {

                        //                 },
                        //                 update() {
                        //                     let ps = sss.getPluginService();
                        //                     $(wrapper).html(`name:${ps.dataGetter.getMasterData().name};数据总数:${ps.dataGetter.getDetail('object_c1YBm__c').length}`);
                        //                 }
                        //             }
                        //         }],
                        //         headerSlot:[(wrapper) => {
                        //             var instance = FxUI.create({
                        //                 wrapper: wrapper,
                        //                 template: '<div><fx-button @click="handleUpdate" plain>更新数据</fx-button><fx-button @click="handAdd" plain>添加一组数据</fx-button><fx-button @click="handleAddChildren" plain>添加子数据</fx-button></div>',
                        //                 data() {
                        //                     return {
                        //                         input1: '',
                        //                         flag: true
                        //                     }
                        //                 },
                        //                 methods: {
                        //                     handAdd() {
                        //                         let ps = sss.getPluginService();
                        //                         let r1 = sss.getRowUniqueId(), r2 = sss.getRowUniqueId(), r3 = sss.getRowUniqueId(), r4 = sss.getRowUniqueId();
                        //                         ps.dataUpdater.add([{
                        //                             object_describe_api_name: 'object_c1YBm__c',
                        //                             record_type: 'default__c',
                        //                             name: '产品数据1',
                        //                             field_ufYgj__c: 300,
                        //                             rowId: r1,
                        //                             isFake: true,
                        //                             trGroupUpIcon: 'fx-icon-jingxiaoshang',
                        //                             trGroupDownIcon: 'fx-icon-email'
                        //                         },{
                        //                             object_describe_api_name: 'object_c1YBm__c',
                        //                             record_type: 'default__c',
                        //                             name: '产品数据1-1',
                        //                             field_ufYgj__c: 200,
                        //                             parent_rowId: r1,
                        //                             rowId: r2
                        //                         },{
                        //                             object_describe_api_name: 'object_c1YBm__c',
                        //                             record_type: 'default__c',
                        //                             name: '产品数据1-1-1',
                        //                             field_ufYgj__c: 100,
                        //                             parent_rowId: r2,
                        //                             rowId: r3
                        //                         },{
                        //                             object_describe_api_name: 'object_c1YBm__c',
                        //                             record_type: 'default__c',
                        //                             name: '产品数据1-1-2',
                        //                             field_ufYgj__c: 100,
                        //                             parent_rowId: r2,
                        //                             rowId: sss.getRowUniqueId()
                        //                         },{
                        //                             object_describe_api_name: 'object_c1YBm__c',
                        //                             record_type: 'default__c',
                        //                             field_ufYgj__c: 100,
                        //                             name: '产品数据1-2',
                        //                             parent_rowId: r1,
                        //                             rowId: r4
                        //                         },{
                        //                             object_describe_api_name: 'object_c1YBm__c',
                        //                             record_type: 'default__c',
                        //                             field_ufYgj__c: 60,
                        //                             name: '产品数据1-2-1',
                        //                             parent_rowId: r4,
                        //                             rowId: sss.getRowUniqueId()
                        //                         },{
                        //                             object_describe_api_name: 'object_c1YBm__c',
                        //                             record_type: 'default__c',
                        //                             name: '产品数据1-2-2',
                        //                             field_ufYgj__c: 40,
                        //                             parent_rowId: r4,
                        //                             rowId: sss.getRowUniqueId()
                        //                         }])
                        //                         this.r1 = r1;
                        //                         this.r2 = r2;
                        //                         this.r3 = r3;
                        //                         ps.end();
                        //                     },
                        //                     handleAddChildren() {
                        //                         let ps = sss.getPluginService();
                        //                         let r2 = sss.getRowUniqueId(), r3 = sss.getRowUniqueId(), r4 = sss.getRowUniqueId();
                        //                         let r5 = sss.getRowUniqueId();
                        //                         ps.dataUpdater.add([{
                        //                             object_describe_api_name: 'object_c1YBm__c',
                        //                             record_type: 'default__c',
                        //                             parent_rowId: this.r1,
                        //                             rowId: r2,
                        //                             name: '二级'
                        //                         },{
                        //                             object_describe_api_name: 'object_c1YBm__c',
                        //                             record_type: 'default__c',
                        //                             parent_rowId: r2,
                        //                             rowId: r3,
                        //                             name: '三级'
                        //                         }, {
                        //                             object_describe_api_name: 'object_c1YBm__c',
                        //                             record_type: 'default__c',
                        //                             parent_rowId: this.r1,
                        //                             rowId: r4,
                        //                             name: '二级' 
                        //                         }, {
                        //                             object_describe_api_name: 'object_c1YBm__c',
                        //                             record_type: 'default__c',
                        //                             rowId: r5,
                        //                             name: 'yiddd级' 
                        //                         },{
                        //                             object_describe_api_name: 'object_c1YBm__c',
                        //                             record_type: 'default__c',
                        //                             parent_rowId: r5,
                        //                             rowId: sss.getRowUniqueId(),
                        //                             name: 'yiddd级-233' 
                        //                         }])
                        //                         ps.end();
                        //                     },
                        //                     handleUpdate(rowId) {
                        //                         let ps = sss.getPluginService();
                        //                         this.flag = !this.flag;
                        //                         ps.dataUpdater.updateTreeExpend(this.flag, 'object_c1YBm__c');
                        //                         ps.dataUpdater.updateDetail('object_c1YBm__c', this.r1, {name: '100'});
                        //                         ps.dataUpdater.updateDetail('object_c1YBm__c', this.r2, {name: '200'});
                        //                         ps.dataUpdater.updateDetail('object_c1YBm__c', this.r3, {name: '300'});
                        //                         ps.end();
                        //                     }
                        //                 }
                        //             })
    
                        //             return {
                        //                 destroy() {
                        //                     instance.destroy();
                        //                 }
                        //             }
                        //         }, (wrapper) => {
                        //             $(wrapper).append('<div>*****************1</div>');
                        //         }],
                        //         treeConfig: {
                        //             expend: true //默认不展开子数据
                        //         }
                        //     }
                        // }
                        if(rst.Value) {
                            config[ps.objApiName] = rst.Value;
                            rst.Value.treeConfig && me.updateTreeConfig(rst.Value.treeConfig, ps.objApiName);
                        }
                        _inner();
                    })
                }
                _inner();
            })
        },

        runMDRenderAftereService(param) {
            let attributes = this.get('__mdRenderBeforeAttributes');
            if(attributes) {
                _.each(attributes, attr => {
                    this.caltaskSuccessHandle(attr);
                })
                this.set('__mdRenderBeforeAttributes', null);
            }
            
            return new Promise(resolve => {
                const apinames = [...this.getMDApiNames()];
                const _inner = () => {
                    if(!apinames.length) return resolve();
                    const ps = this.getPluginService({
                        objApiName: apinames.pop()
                    })
                    ps.run('md.render.after').then((rst = {}) => {
                        ps.end(rst.StatusCode).then(_inner);
                    })
                }

                _inner();
            })
        },

        runMasterEditService(changeFields, cb, context, blurKey) {
            const ps = this.getPluginService({
                objApiName: this.get('apiname'),
                changeFields: changeFields || [],
                triggerField: blurKey,
                masterData: _.extend({}, this.get('data'), changeFields && changeFields.__data)
            })
            const isOcr = blurKey && (ps.dataGetter.getFieldAttr(blurKey) || {}).is_ocr_recognition;
            ps.triggerCal().then((res = {}) => {
                if(res.StatusCode) return cb({}, isOcr ? void 0 : {isBack: true});//计算出错 直接返回并回滚

                ps.triggerUIEvent({
                    noMerge:true,
                    loadingText: isOcr ? $t( '图片上传识别中,请稍后') + '...' : '',
                    noRetry: isOcr
                }).then((uires = {}) => {
                    if(uires.StatusCode) return cb({}, isOcr ? void 0 : {isBack: true});//ui事件出错 直接返回并回滚
                    cb((res.Value && res.Value.calculateResult) || {});
                    uires.Value && this._updateDataByUIEventData(uires.Value, {blurKey: blurKey});
                })
            })
        },

        //添加一行
        runMDAddOneService(param) {
            const ps = this.getPluginService(param);
            ps.operateType = 'mdAdd';
            ps.newData = ps.getRowBasicData();
            ps.dataIndex = ps.newDataIndexs = [ps.newData.rowId];

            if(!ps.noScrollTo) {
                let finallyCallback = ps.finallyCallback;
                ps.finallyCallback = (error) =>{
                    finallyCallback && finallyCallback(error);
                    this.triggerMDActionEvent(ps.objApiName, ps.recordType, 'srcollToHandle', ps.newData.rowId);
                }
            }
            
            ps.run('md.add.before').then(rst => {
                if(rst && rst.StatusCode) return ps.end(true);
                if(ps.insertedRowId) {
                    ps.dataUpdater.insert(ps.objApiName, ps.insertedRowId, [ps.newData]);
                } else {
                    ps.dataUpdater.add([ps.newData]);
                }
                
                ps.addDatas = _.map(ps.dataIndex, rowId => ps.dataGetter.getData('', rowId));
                ps.run('md.add.after').then(rst => {
                    ps.commonAction(rst && rst.StatusCode,  {beforeEndPlugin: 'md.add.end'});
                })
            })
        },

        //从xx批量添加
        runBatchAddService(param) {
            const ps = param.pluginContext || this.getPluginService(param);
            ps.operateType = 'mdAdd';
            ps._isBatchAdd = true;
            ps.filterFields = {[ps.objApiName]: [ps.lookupField.api_name]};

            ps.run('md.batchAdd.before').then((rst = {}) => {
                if(rst.StatusCode) return ps.end(true);//
                if(rst.Value) {
                    _.extend(ps, rst.Value);
                    ps.pickConfig = rst.Value;
                }
    
                ps.pickData(ps).then(opts => {
                    if(!opts) return ps.end(true);
                    const addDatas = _.map(opts.addDatas, a => ({...ps.getRowBasicData(), ...a}));
                    ps.lookupDatas = opts.lookupData;
                    ps.dataIndex = ps.newDataIndexs = _.pluck(addDatas, 'rowId');

                    if(ps.insertedRowId) {
                        ps.dataUpdater.insert(ps.objApiName, ps.insertedRowId, addDatas);
                        ps.addDatas = addDatas;
                    } else {
                        ps.addDatas = ps.dataUpdater.add(addDatas);
                    }

                    ps.run('md.batchAdd.after').then(rst => {
                        if(rst && rst.Value && rst.Value.newDatas) {
                            ps.dataUpdater.__filterOldAddDatas(ps.dataIndex);
                            let newDatas = _.map(rst.Value.newDatas, a => ({...ps.getRowBasicData(), ...a}));
                            ps.dataIndex = ps.newDataIndexs = _.pluck(newDatas, 'rowId');
                            ps.addDatas = ps.dataUpdater.add(newDatas);
                        }
                        ps.commonAction(rst && rst.StatusCode, {
                            beforeEndPlugin: 'md.batchAdd.end',
                            skipCal: !!(rst && rst.Value && rst.Value.skipCal),
                            skipUIEvent: !!(rst && rst.Value && rst.Value.skipUIEvent)
                        });
                    })
                })
            })
        },

        //复制从对象数据
        runMDCoypeService(param) {
            let changeData = param.changeData;
            delete param.changeData;

            const ps = this.getPluginService(param);
            ps.operateType = 'mdCopy';

            ps.run('md.copy.before').then(rst => {
                if(rst && rst.StatusCode) return ps.end(true);
                let oldRowId = {};
                const copyDatas = _.map(ps.copyRowIds, rowId => {
                    let obj = this.copyRowData(ps.dataGetter.getData('', rowId))
                    oldRowId[rowId] = obj.rowId;
                    return _.extend(obj, changeData);
                })
                _.each(copyDatas, a => {
                    oldRowId[a.parent_rowId] && (a.parent_rowId = oldRowId[a.parent_rowId]);
                })
                ps.parseParam = rst && rst.Value && rst.Value.parseParam;
                ps.dataIndex = ps.newDataIndexs = _.pluck(copyDatas, 'rowId');
                ps.dataUpdater.insert('', _.last(ps.copyRowIds), copyDatas);
                ps.run('md.copy.after').then(rst => {
                    ps.commonAction(rst && rst.StatusCode, {beforeEndPlugin: 'md.copy.end'});
                })
            })
        },

        //excel导入
        runImportExcelSevice(param) {
            const ps = this.getPluginService(param);
            ps.operateType = 'mdAdd';
            //ps.skipUIEvent = !CRM.util.getUserAttribute('crmImportMDSupportEvent'); //导入的数据不触发add事件

            ps.run('md.excelimport.before').then((rst = {}) => {
                if(rst.StatusCode) return ps.end(true);
                let importConfig =  rst.Value && rst.Value.importConfig || {};
                let parseFormatDatas = importConfig.parseFormatDatas;
                if(!parseFormatDatas) {
                    parseFormatDatas = function(formatDatas) {
                        return Promise.resolve(formatDatas);
                    }
                }
                importConfig.getRowBasicData = function() {
                    return ps.getRowBasicData();
                }

                ps.importFunc(importConfig).then((error, formatDatas, evt) => {
                    if(error) return;
                    formatDatas = _.map(formatDatas, item => {
                        return {...ps.getRowBasicData(), ...item};
                    })
                    parseFormatDatas(formatDatas, ps).then(formatDatas => {
                        if (!formatDatas || !formatDatas.length) {
                            return ps.end(true)
                        }

                        let addDatas = _.map(formatDatas, item => {
                            _.each(item, (v, k) => {
                                if(/__ro$|__fields$/.test(k)) {//业务操作过程中追加的查找关联完整数据，不再需要
                                    delete item[k];
                                }
                            })
                            return item;
                        })
                        ps.filterFields = {[ps.objApiName]: _.keys(formatDatas[0])};

                        ps.dataIndex = ps.newDataIndexs = _.pluck(addDatas, 'rowId');
                        ps.dataUpdater.add(addDatas);
                        ps.fileEvent = evt;
                        ps.run('md.excelimport.after').then(rst => {
                            ps.commonAction(rst && rst.StatusCode, {
                                beforeEndPlugin: 'md.excelimport.end',
                                skipUIEvent: ps.skipUIEvent
                            });
                        })
                    })
                })
            })
        },

        //目前复制粘贴不支持触发事件，需要给提示
        __pasteTips(changeFields, hasAdd, objApiName) {
            if(this.__showPasteTips) return;

            try {
                let events = (this.get('__originLayout') || {}).events;
                if(!events || !events.length) return;
                let event = _.find(events, event => {
                    return event.trigger_describe_api_name === objApiName && (_.find(changeFields, cf => _.contains(event.trigger_field_api_names, cf)) || (hasAdd && event.triggers[0] == 2))
                })
    
                if(event) {
                    FxUI.Message({
                        //showClose: false,
                        //isMiddler: true,
                        duration: 3000,
                        message: $t('paas.crm.excelpaste.tip'),
                        type: 'warning'
                    })
                    this.__showPasteTips = true;
                }
            } catch(e) {}
        },

        //excel复制粘贴到表格
        runExcelCopyService(param) {
            const ps = this.getPluginService(param);
            ps.run('md.excelpaste.before').then((rst = {}) => {
                if(rst.StatusCode) return ps.end(true);
                let rv = rst.Value || {};
                if(rv.noSupportPaste) return ps.end(true);
                if(ps.fieldIsReadOnly && !rv.supportPaste) return ps.end(true);

                let importConfig = rst.Value && rst.Value.pasteConfig || {};
                importConfig.getRowBasicData = function() {
                    return ps.getRowBasicData();
                }

                ps.excelCopyFunc(importConfig).then(opts => {
                    if(!opts) return ps.end(true);

                    let {changeData, addDatas} = opts;
                    let changeFields = _.without(_.keys(changeData[0]), 'record_type', 'object_describe_api_name', 'rowId');

                    ps.filterFields = {[ps.objApiName]: changeFields};

                    ps.pasteContent = {
                        changeData, addDatas, changeFields
                    }


                    ps.dataIndex = _.pluck(changeData.concat(addDatas), 'rowId');
                    ps.newDataIndexs = _.pluck(addDatas, 'rowId');
                    
                    ps.dataUpdater.add(addDatas);

                    
                            
                    ps.run('md.excelpaste.after').then(rst => {
                        if(rst && rst.StatusCode) return ps.end(true);

                        addDatas = ps.pasteContent.addDatas;

                        let arrRowIds = _.pluck(addDatas, 'rowId');

                        _.each(arrRowIds, rowId => {
                            ps.dataUpdater.updateData('', rowId, {
                                __fields: void 0,
                                __ro: void 0
                            });
                        })

                        this.__pasteTips(changeFields, !!addDatas.length, ps.objApiName);

                        ps.triggerCal({
                            operateType: 'mdAdd',
                            dataIndex: arrRowIds,
                            skip: !addDatas.length
                        }).then(rst1 => {
                            if(rst1 && rst1.StatusCode) {//有错
                                return ps.end(true);
                            }
                            
                            changeData = ps.pasteContent.changeData;
                            _.each(changeData, a => {
                                _.each(a, (v, k) => {
                                    if(/__ro$|__fields$/.test(k)) {//业务操作过程中追加的查找关联完整数据，不再需要
                                        delete a[k];
                                    }
                                })

                                ps.dataUpdater.updateData('', a.rowId, a);
                            });

                            ps.triggerCal({
                                changeFields: ps.pasteContent.changeFields,
                                operateType: 'mdEdit',
                                dataIndex: _.pluck(changeData, 'rowId'),
                                skip: !changeData.length
                            }).then(rst2 => {
                                if(rst2 && rst2.StatusCode) {//有错
                                    return ps.end(true);
                                }
                                ps.run('md.excelpaste.end').then(rst3 => {
                                    ps.end(rst3 && rst3.StatusCode);
                                })
                            })
                        })
                    })
                })
            })
        },
        
        //单元格编辑
        runMDEditService(param) {

            const ps = this.getPluginService(param);
            ps.operateType = 'mdEdit';

            let me = this;
            me.mdEditExcuteing = true;
            me.trigger('lock.fieldedit');//锁定主表单上字段的编辑
            let end = ps.end;
            ps.end = function() {
                me.mdEditExcuteing = false;
                me.trigger('unlock.fieldedit');//解除主表单上字段的编辑的锁定
                end.apply(ps, arguments);
            }

            ps.beChangeFields = [];

            ps.run('md.edit.before').then((rst = {}) => {
                if(rst.StatusCode) return ps.end(true);//
                let filterRowIds;
                if(rst.Value) {
                    if(rst.Value.options) {
                        ps.fieldOptions = rst.Value.options;
                        delete rst.Value.options
                    }
                    if(rst.Value.filterRowIds) {
                        filterRowIds = rst.Value.filterRowIds;
                        delete rst.Value.filterRowIds
                    }
                    ps.skipSearch = rst.Value.skipSearch;
                    ps.pickConfig = rst.Value;
                }

                //防止业务自己写的，清除不掉
                if(ps.skipSearch || (ps.pickConfig && ps.pickConfig.fieldEditCallBack)) {
                    me.mdEditExcuteing = false;
                    me.trigger('unlock.fieldedit');//解除主表单上字段的编辑的锁定
                }

                ps.fieldEditTask(ps).then((changeData, lookupData) => {
                    if(!changeData) return ps.end(true);
                    ps.changeData = changeData;
                    ps.lookupData = lookupData || changeData.lookupData;
                    delete changeData.lookupData;

                    if(filterRowIds) {
                        ps.dataIndex =  _.difference(ps.dataIndex, filterRowIds);
                        _.each(filterRowIds, rowId => delete changeData[rowId]);
                    }
                    ps.run('md.edit.after').then(rst => {
                        let changeFields;
                        _.each(ps.changeData, (a, rowId) => {
                            ps.dataUpdater.updateData('', rowId, a);
                            changeFields || (changeFields = _.keys(a));
                            ps.dataUpdater.__setFieldCheckCalculationConfig({changeFields: {[rowId]: changeFields}});
                        })
                        ps.changeFields = _.union(changeFields || [], ps.beChangeFields || []);

                        delete ps.beChangeFields;

                        if(this.assertUsePlugin()) {//目前先判断使用了插件的有这逻辑，后期考虑去掉，都走
                            if(ps.filterFields) {
                                if(ps.filterFields[ps.objApiName]) {
                                    ps.filterFields[ps.objApiName] = ps.filterFields[ps.objApiName].concat(changeFields);
                                } else {
                                    ps.filterFields[ps.objApiName] = changeFields;
                                }
                            } else {
                                ps.filterFields = {[ps.objApiName]: changeFields};
                            }
                        }
                        ps.commonAction(rst && rst.StatusCode, {beforeEndPlugin: 'md.edit.end'});
                    })
                })
            })
        },

        //md数据删除
        runMDDelService(param, pluginContext) {
            let ps;
            if(pluginContext) {
                ps = _.extend(pluginContext, param);
            } else {
                ps = this.getPluginService(param);
            }

            ps.operateType = 'mdDel';

            ps.run('md.del.before').then(rst => {
                if(rst && rst.StatusCode) return ps.end(true);
                _.each(ps.delDatas, a => ps.dataUpdater.del(ps.objApiName, a.rowId));
                ps.run('md.del.after').then(rst => {
                    ps.commonAction(rst && rst.StatusCode, {beforeEndPlugin: 'md.del.end'});
                })
            })
        },

        //自定义按钮
        runThirdService(param) {
            const ps = this.getPluginService(param);
            ps.run('md.customAction');
        },

        //获取指定对象的描述
        getDescribeByObjApiName(apiName) {
            if(apiName === this.get('apiname')) return this.get('__originDescribe');
            const dom = _.findWhere(this.get('detailObjectList'), {objectApiName: apiName});
            return dom && dom.objectDescribe;
        },

        /**
         * 获取计算字段
         * @param operateType 操作类型 masterEdit(主对像字段触发) mdEdit(从对象字段触发) mdDel(从对象删除) mdAdd(从对象添加) mdCopy(从对象复制)
         * @param changeFields masterEdit|mdEdit操作时需要传，告诉都改了哪些字段
         * @param extraFields 在通用计算字段的基础上需要额外需要计算的字段 
         * @param modifiedObjectApiName  当前操作
         * @returns ctf 需要计算的字段
         */
        getCalculateFields(param) {
            let ctf = {};
            let operateType = param.operateType;
            let ds = this.getDescribeByObjApiName(param.modifiedObjectApiName);
            if(!ds) return ctf;
            let inner = (changeFields, fields, apiName) => {
                _.each(changeFields, key => {
                    let tt = fields[key];
                    tt && tt.calculate_relation && _.each(tt.calculate_relation.calculate_fields, (arr, k) => {
                        if (k === '__uievents') return;
                        ctf[k] = _.union(arr, ctf[k] || []);
                    })
                    if(apiName) {
                        ctf[apiName] = _.union(changeFields, ctf[apiName] || []);
                    }
                })
            }
            if(operateType === 'mdEdit' || operateType === 'masterEdit' || (!operateType && param.changeFields)) {
                inner(param.changeFields, ds.fields);
            } else if(operateType === 'mdDel' || operateType === 'mdCopy') {
                _.each(ds.calculate_relation && ds.calculate_relation.calculate_fields, (fields, apiName) => {
                    ctf[apiName] = [...fields];
                }) 
            } else if(operateType === 'mdAdd') {
                _.each(ds.fields, item => {
                    if (!item.calculate_relation) return;
                    _.each(item.calculate_relation.calculate_fields, (arr, k) => {
                        ctf[k] = _.union(arr, ctf[k] || []);
                    })
                });
    
                _.each(ds.calculate_relation && ds.calculate_relation.calculate_fields, (arr, k) => {
                    ctf[k] = _.union(arr, ctf[k] || []);
                })
            }

            if(param.extraFields) {
                if(_.isArray(param.extraFields)) {
                    inner(param.extraFields, ds.fields);
                } else {
                    _.each(param.extraFields, (a, b) => {
                        let tds = this.getDescribeByObjApiName(b);
                        tds && inner(a, tds.fields, b);
                    })
                }
            }

            _.each(param.filterFields, (fields, apiName) => {
                let tc = ctf[apiName];
                if(tc) {
                    ctf[apiName] = _.filter(tc, a => {
                        let isC = /__c$/.test(a);
                        if(isC && _.contains(fields, '__c')) return;
                        if(!isC && _.contains(fields, '__s')) return;
                        return !_.contains(fields, a);
                    })
                }
            })

            delete ctf.__uievents;

            return ctf;
        },

        /**
         * 把detils变为计算 或 UIEvent 需要的格式
         * @param {*} details 
         */
        details2DDM(details) {
            let ddm = {};
            _.each(details, (arr, apiname) => {
                let tt = {};
                _.each(arr, a => {
                    tt[a.rowId] = _.extend({}, a, {
                        __fields: void 0,
                        __rules: void 0,
                        __srules: void 0,
                        _cellStatus: void 0
                    });
                })
                ddm[apiname] = tt;
            })
            return ddm;
        },

        /**
         * 通用计算方法，无任何特殊业务，根据传入的参数发起计算
         * @param param = {
         * objApiName: '', //要计算的对象apiname
         * calType: 'masterEdit|mdEdit||mdCopy||mdDel|mdAdd'
         * changeFields: [''] //那些字段触发的
         * filterFields: {objectApiname: ['xx', 'xx']} //过滤掉那些字段不用算
         * masterData: {.....} //主对像数据
         * details: {a: [{....},...], b: [{....},...]} 从对象数据
         * modifiedRowIdList: ['rowId1', 'rowId2'] 修改的从对象数据 rowId
         * calculateFields: {a: ['price'], b: ['tax']} 表示本次需要计算对象a的price 和 对象b的tax
         * noLoading: false //默认显示数据计算中loading
         * }
         * @returns promise
         */
        calculateService(param) {
            let me = this;
            return new Promise(resolve => {
                let masterApiName = me.get('apiname');
                let modifiedObjectApiName = param.objApiName === masterApiName ? void 0 : param.objApiName;
                let calculateFields = me.getCalculateFields({
                    operateType: param.calType,
                    changeFields: param.changeFields,
                    extraFields: param.extraFields,
                    filterFields: param.filterFields,
                    modifiedObjectApiName: modifiedObjectApiName || masterApiName
                });
                if(_.isEmpty(calculateFields)) return resolve();

                let details = me.filterFakeDetails(param.details);

                let requestParam = {
                    modifiedObjectApiName: modifiedObjectApiName,
                    masterObjectApiName: masterApiName,
                    modifiedDataIndexList: param.modifiedRowIdList && (_.isArray(param.modifiedRowIdList) ? param.modifiedRowIdList : [param.modifiedRowIdList]),
                    calculateFieldApiNames: calculateFields,
                    masterData: me._filterrCalculateMasterData(param.masterData),
                    detailDataMap: me._filterCalculateMDData(me.details2DDM(details), calculateFields),
                    calculateFields: me._getCalculateFieldOrder(calculateFields),
                    _changeFields: param.changeFields,
                    _editField: param.pluginService && param.pluginService.fieldName,
                    _calType: param.calType,
                    maskFieldApiNames: me._getCalculateEncryptFields(calculateFields)
                }
                param.parseParam && (requestParam = param.parseParam(requestParam));

                //补充计算优化参数
                let modifiedType = {mdEdit: 'edit', mdDel: 'del', mdAdd: 'add', mdCopy: 'copy'}[param.calType];
                if(modifiedType) {
                    if(modifiedType === 'edit') {
                        requestParam.modifiedType = modifiedType
                        requestParam.modifiedFields = param.changeFields;
                        let oldDDM = me.getOldDetailDataMap(requestParam.modifiedDataIndexList, modifiedObjectApiName);
                        requestParam.oldDetailDataMap = oldDDM; // me._filterCalculateMDData(oldDDM, calculateFields);
                    } else if(modifiedType === 'del') {
                        let ttt = {};
                        _.each(param.delDatas, a => ttt[a.rowId] = a);
                        requestParam.oldDetailDataMap = {[modifiedObjectApiName] : ttt};
                        requestParam.modifiedType = modifiedType
                    } else {
                        requestParam.modifiedType = modifiedType;
                    }
                }

                me.commonRequest({
                    type: 'cal',
                    url: '/EM1HNCRM/API/v1/object/calculate/service/batchCalculate?_postid=' + FS.crmUtil.getUUIdAsMiniProgram() + FS.crmUtil.getTraceId(),
                    data: requestParam,
                    loadingText: param.noLoading ? '' : param.loadingText || $t('数据计算中') + '...',
                    noRetry: param.noRetry,
                    pluginService: param.pluginService
                }, true).then(resolve)
            })
        },

        getOldDetailDataMap(rowIds, apiname) {
            let obj = {};
            _.each(rowIds, rowId => {
                _.find(this.getDetails(), arr => {
                    return _.find(arr, item => {
                        if(item.rowId === rowId) {
                            let tmp = (obj[apiname] || (obj[apiname] = {}))[rowId] = _.extend({}, item, {
                                __fields: void 0,
                                __rules: void 0,
                                __srules: void 0,
                                _cellStatus: void 0,
                                __operateWidth: void 0
                            });

                            _.keys(tmp).forEach(key => {
                                if(/__(?:r|l)$/.test(key)) {
                                    delete tmp[key];
                                }
                            })
                            return true;
                        }
                    })
                })
            })

            return obj;
        },

        /**
         * 通用retry提示框
         * resolve(true|false)
         */
        commonIsRetry(msg, cancelTitle) {
            return new Promise(resolve => {
                let isRest;
                let confirm = FS.crmUtil.confirm(msg, $t('提示'), function () {
                    isRest = true;
                    confirm.hide();
                }, {
                    hideFn: function () {
                        resolve(isRest);
                        $('body>.crm__aaa').remove();
                    },
                    btnLabel: {
                        confirm: $t('重试')
                    },
                    zIndex: 20000
                })
                confirm.element && confirm.element.find('.b-g-btn-cancel').addClass('crm-ui-title').attr({
                    'data-class': 'crm__aaa',
                    'data-pos': 'top',
                    'data-title': cancelTitle || $t('取消后本次修改无效!')
                })
            })
        },

        commonWaiting(text) {
            let $wait;
            let ta = ':opacity .2s linear .3s'
            let trans = `transition${ta};-moz-transition${ta};-webkit-transition${ta};-o-transition${ta}`;
            $(document.body).append($wait = $(`<div style="${trans};opacity:0;position:fixed;top:0;bottom:0;left:0;right:0;display:flex;align-items:center;justify-content:center;z-index:20000;flex-direction:column"><div style="z-index:100;margin-bottom:8px;font-size:14px;color:var(--color-primary06)" class="waiting-text">${text || ''}</div><div class="crm-loading-anima"></div></div>`));
            setTimeout(() => $wait.css('opacity', 1));
            return $wait;
        },

        /**
         * 通用发送请求
         * resolve(true|false)
         */
        commonRequest(param, isCal) {
            let me = this;
            return new Promise(resolve => {
                let msgs = $t('crm.计算错误提示', null, '取消后本次修改无效！是否继续重试？&本地网络连接问题，&UI事件未正确执行，是否重试？&数据未正确计算，是否重试？').split('&');
                let inner = () => {
                    me._analysisAjax = true;
                    let $waiting = param.loadingText && me.commonWaiting(param.loadingText);
                    let _timer = new Date().getTime();
                    let _type = param.type === 'cal' ? 'calculate' : 'uiEvent';

                    me.logByPlugin([{
                        action: `form.${_type}.before`,
                        eventData: {
                            data: param.data,
                        }
                    }]);

                    param.data.seriesId = me.requestId;

                    calService({
                        url: param.url,
                        data: param.data,
                        getFields(objApiName) {
                            return (me.getDescribeByObjApiName(objApiName) || {}).fields || {};
                        },
                        success(res) {
                            $waiting && $waiting.remove();
                            me._analysisAjax = false;
                            if (res.Result.StatusCode === 0) {
                                me.logByPlugin([{
                                    action: `form.${_type}.after`,
                                    eventData: {
                                        data: res
                                    }
                                }]);
                                me._suppEncryptData(res && res.Value.calculateResult, res && res.Value && res.Value.data);
                                me.__addUIFlag(res && res.Value && res.Value.data, res.Value, {detail_object_data: param.data.detail_object_data});
                                resolve(res);
                            } else {
                                let msg = res.Result.FailureMessage + ' ' + msgs[ param.type === 'cal' ? 3 : 2];
                                if(param.noRetry) {
                                    let _res = {StatusCode: 1, FailureMessage: res.Result.FailureMessage, serverRes: res};
                                    param.noAlert || FS.crmUtil.alert(res.Result.FailureMessage);
                                    me.logByPlugin([{
                                        action: `form.${_type}.after`,
                                        eventData: {
                                            data: _res,
                                        }
                                    }], {
                                        bizLog: true
                                    });
                                    resolve(_res)
                                } else {
                                    me._analysisAjax = true;
                                    me.commonIsRetry(msg).then(flag => {
                                        me._analysisAjax = false;
                                        if (flag) {
                                            inner()
                                        }
                                        else {
                                            let _res = {StatusCode: 1, FailureMessage: msg};
                                            me.logByPlugin([{
                                                action: `form.${_type}.after`,
                                                eventData: {
                                                    data: _res,
                                                }
                                            }], {
                                                bizLog: true
                                            });
                                            resolve(_res);
                                        }
                                    });
                                }
                            }
                        },
                        error(res) {
                            $waiting && $waiting.remove();
                            let msg = (new Date().getTime() - _timer < 16 ? msgs[1] : (res && res.Result && res.Result.FailureMessage) || '') + ' ' + msgs[ param.type === 'cal' ? 3 : 2];
                            if(param.noRetry) {
                                me._analysisAjax = false;
                                msg = $t('计算失败');
                                param.noAlert || FS.crmUtil.alert(msg);

                                let _res = {StatusCode: 1, FailureMessage: msg};

                                me.logByPlugin([{
                                    action: `form.${_type}.after`,
                                    eventData: {
                                        data: _res,
                                    }
                                }], {
                                    bizLog: true
                                });
                                resolve(_res);
                            } else {
                                me._analysisAjax = true;
                                me.commonIsRetry(msg).then(flag => {
                                    me._analysisAjax = false;

                                    if (flag) {
                                        inner()
                                    }
                                    else {
                                        let _res = {StatusCode: 1, FailureMessage: msg};
                                        me.logByPlugin([{
                                            action: `form.${_type}.after`,
                                            eventData: {
                                                data: _res,
                                            }
                                        }], {
                                            bizLog: true
                                        });
                                        resolve(_res);
                                    }
                                });
                            }
                        }
                    }, {
                        errorAlertModel: 1
                    })
                }

                inner();
            })
        },

        //ocr服务识别
        ocrService(ocrFieldApiName, param) {
            let me = this;
            let field = me.getFieldAttr(ocrFieldApiName);
            let isOcr = field && field.is_ocr_recognition && field.identify_field_mapping && field.identify_field_mapping.length;
            if(!isOcr) return {
                then(resolve) {
                    resolve();
                }
            }
            let loadingText = $t('图片上传识别中,请稍后') + '...';
            CRM.util.waiting(loadingText, true);
            return new Promise(resolve => {
                let masterObjectApiName = me.get('apiname');
                let masterData = param.data || param.masterData || me.get('data');
                let detailDataMap = param.detailDataMap || param.ddm;
                function getOcrErrorResultMap(res) {
                    let fieldData = masterData[ocrFieldApiName] || [];
                    if(!fieldData.length) return;

                    let result = [];
                    if(res.StatusCode) {
                        result = [{
                            path: fieldData[fieldData.length - 1].path,
                            errorMsg: res.FailureMessage
                        }]
                    } else {
                        _.each(res.Value && res.Value.ocrDataResult, a => {
                            if(a.ocrData && a.ocrData.success === false) {
                                result.push({
                                    path: a.path,
                                    errorMsg: a.ocrData.errorMsg
                                })
                            }
                        })
                    }

                    if(!result.length) return;

                    return {
                        [ocrFieldApiName]: result
                    }
                }
                function end1(res) {
                    CRM.util.waiting(false, true);
                    let calculateResult = res.Value && res.Value && res.Value.calculateResult || {};
                    let error = getOcrErrorResultMap(res);
                    resolve({
                        Result: {StatusCode: 0},
                        Value: {
                            calculateResult,
                            uiEventResult: error && {
                                data: {},
                                ocrErrorResultMap: error
                            }
                        }
                    })
                }
                function end2(res1, res2) {
                    CRM.util.waiting(false, true);
                    resolve({
                        Result: {StatusCode: 0},
                        Value: {
                            calculateResult: res1.Value.calculateResult || {},
                            uiEventResult: {data: {}, ocrErrorResultMap: getOcrErrorResultMap(res2), ...res2.Value}
                        }
                    });
                }
                function triggerUIEvent(res1) {
                    let isFail = res1.StatusCode || _.find(res1.Value && res1.Value.ocrDataResult || [], a => a.ocrData && a.ocrData.success === false);
                    if(!isFail && param.event) {//成功
                        let ddm = me.suppDDM(detailDataMap);
                        let newMasterData;
                        _.each(res1.Value.calculateResult, (item, apiname) => {
                            if(apiname === masterObjectApiName) {
                                newMasterData = {...masterData, ...item[0]}
                            } else {
                                let tt = ddm[apiname];
                                tt && _.each(item, (a, rowId) => {
                                    tt[rowId] && (tt[rowId] = _.extend({}, tt[rowId], a));
                                })
                            }
                        })
                        me.commonRequest({
                            url: '/EM1HNCRM/API/v1/object/' + masterObjectApiName + '/action/TriggerEvent',
                            data: {
                                event_id: param.event._id,
                                layout_api_name: me.get('layout_apiname'),
                                object_data: newMasterData || masterData,
                                detail_object_data: ddm,
                                trigger_field_api_name: [ocrFieldApiName],
                                ocrDataResult: res1.Value.ocrDataResult,
                                seriesId: me.requestId,
                                maskFieldApiNames: me._getCalculateEncryptFields(),
                                trigger_info: me.getTriggerInfo()
                            },
                            loadingText,
                            noRetry: true,
                            noAlert: true
                        }).then(res2 => {
                            end2(res1, res2);
                        })
                    } else {//失败
                        end1(res1);
                    }
                }

                me.commonRequest({
                    url: '/EM1HNCRM/API/v1/object/describe/service/ocrIdentify',
                    data: {
                        ocrFieldApiName,
                        masterObjectApiName,
                        masterData,
                        detailDataMap,
                        seriesId: me.requestId
                    },
                    loadingText,
                    noRetry: true,
                    noAlert: true
                }).then(triggerUIEvent)
            })
        },

        /**
         * 通用UI事件方法，无任何特殊业务，根据传入的参数发起UI事件
         * @param param = {
         * objApiName: '', 要发起UI事件的对象 不传 默认是主对象
         * triggerField: '',
         * beTriggerFields: ['', ''],当triggerField找不到事件时。从备选的字段里依次找
         * masterData: {...} 主对像数据
         * details: {a: [{....},...], b: [{....},...]} 从对象数据
         * editRowId: 'rowId1' 对应从对象编辑事件的从对象数据
         * newRowId: [''rowId1', ''rowId2'] 对应新增事件的从对象数据'rowId
         * delDatas: [{....}] 对应删除的从对象数据 因为底层details已经没有删掉的数据，需要完整传过来，给server用
         * noLoading: false //默认显示数据计算中loading
         * }
         * @returns promise
        */
        uiEventService(param) {
            let me = this;
            let masterApiName = this.get('apiname');
            return new Promise(resolve => {
                let triggerObiApiName = param.objApiName || masterApiName;
                let event = param.event;
                let triggerFieldNames;
                let triggerType;
                if(!event) {
                    if(triggerObiApiName === masterApiName) {
                        triggerType = 1;
                    } else if(param.newRowId) {
                        triggerType = 2;
                    } else if(param.delDatas) {
                        triggerType = 4;
                    } else if(param.editRowId) {
                        triggerType = 3;
                    }
                    let events = (me.get('__originLayout') || {}).events;
                    if(!events || !events.length) return resolve();
                    
                    _.find(_.union([param.triggerField], param.beTriggerFields || []), tn => {
                        event = _.find(events, event => {
                            //event.triggers 有四种值 [1] [2] [3] [4]
                            //[1] 字段变化失去焦点(主对象) [2] 新增行(从对象) [3]编辑行(从对象) [4]删除行(从对象)
                            let tf = event.trigger_field_api_names;
                            triggerFieldNames = tn;
                            return event.trigger_describe_api_name === triggerObiApiName && event.triggers[0] == triggerType && (!tf.length || _.contains(tf, tn))
                        })
                        return event;
                    })
                    triggerFieldNames && (triggerFieldNames = [triggerFieldNames])
                } else {
                    triggerFieldNames = event.trigger_field_api_names;
                }
                
                if(!event) return resolve();

                let details = param.details // me.filterFakeDetails(param.details); 有问题，会删除isFake 导致删除子数据，可能需要补齐数据

                let requestParam = {
                    event_id: event._id,
                    layout_api_name: me.get('layout_apiname'),
                    object_data: param.masterData,
                    detail_object_data: me.details2DDM(details),
                    editing_object_data: triggerType == 3 ? _.isArray(param.editRowId) ? param.editRowId[0] : param.editRowId : void 0,
                    new_details: param.newRowId ? _.isArray(param.newRowId) ? param.newRowId : [param.newRowId] : void 0,
                    trigger_field_api_name: (triggerType == 1 || triggerType == 3) ? triggerFieldNames || [] : [],
                    deleted_details: param.delDatas ? {
                        [triggerObiApiName]: param.delDatas
                    } : void 0,
                    maskFieldApiNames: me._getCalculateEncryptFields(),
                    trigger_info: me.getTriggerInfo(),
                    batch_editing_object_data: param.pluginService && param.pluginService.batchEditDataIndex
                }

                let fakeRowIds = me.filterUIEventFakeData(requestParam);

                me.commonRequest({
                    url: '/EM1HNCRM/API/v1/object/' + masterApiName + '/action/TriggerEvent',
                    data: requestParam,
                    loadingText: param.noLoading ? '' : param.loadingText || $t('UI事件执行中') + '...',
                    noRetry: param.noRetry
                }).then(res => {
                    me.addFakeDataToUIEvent(res, fakeRowIds);
                    resolve(res);
                })
            })
        },

        /**
         * 过滤请求参数里从对象的临时数据
         */
        filterUIEventFakeData(param) {
            let fakeRowIds = {}, rowIds = [];
            _.each(param.detail_object_data, (obj, objApiName) => {
                let arr = [];
                _.each(obj, (item, rowId) => {
                    if(item.isFake) {
                        obj[rowId] = void 0;
                        arr.push(rowId);
                        rowIds.push(rowId);
                    }
                })
                if(arr.length) {
                    fakeRowIds[objApiName] = arr;
                }
            })
            if(rowIds.length) {
                if(param.new_details) {
                    param.new_details = _.filter(param.new_details, rowId => !_.contains(rowIds, rowId));
                }
                if(param.deleted_details) {
                    param.new_details = _.filter(param.new_details, a => !_.contains(rowIds, a.rowId));
                }
            }

            return fakeRowIds;
        },

        /**
         * 追加临时数据到返回的UI事件里，防止底层更新数据把它理解为删除
         */
        addFakeDataToUIEvent(res, fakeRowIds) {
            if(!fakeRowIds || !res) return;
            res.Value && _.each(res.Value.data, (data, objApiName) => {
                if(data.u) {
                    _.each(fakeRowIds[objApiName], rowId => {
                        data.u[rowId] = {};
                    });
                }
            })
        },

        /**
         * 隐藏显示表单通用按钮 以及 客户预置的自定义按钮
         * actions: [...] 表示要操作的按钮 预置的 submit submitAndCreate saveDraft
         * status: true | false  true表示隐藏
         * toggleCommonButton(['submit'], true) //隐藏提交按钮
         */
        toggleCommonButton(actions, status) {
            this.trigger('toggleCommonButton', actions, status);
        },

        //过滤
        filterFakeDetails(details) {
            if(!details) return details;
            let tmp = {};
            _.each(details, (a, objApiName) => {
                tmp[objApiName] = _.filter(a, a => !a.isFake);
            })

            return tmp;
        },

        //数表专用，更新是否默认展开子数据
        updateTreeConfig(config, objApiName) {
            let key = objApiName + '_treeConfig';
            let newStatus = _.extend(this.get(key) || {}, config);
            this.set(key, newStatus);
            this.trigger(key + ':change', newStatus);
        },

        getTreeConfig(objApiName) {
            return this.get(objApiName + '_treeConfig') || {};
        },

        // mergePlugins(plugins, describe) {
        //     let domain = Array.concat.apply([], plugins);
        //     // let data = {};

        //     // domain.forEach(item => {
        //     //     data[item.pluginApiName] = item;
        //     // });

        //     plugins = this.filterPlugins(domain, describe);

        //     return plugins;
        // },

        // filterPlugins(plugins, describe) {
        //     plugins = this.filterPluginsCommon(plugins, describe);
        //     plugins = this.filterPluginsHack(plugins, describe);

        //     return plugins;
        // },

        // filterPluginsCommon(plugins, describe) {
        //     let recordType = this.get('record_type');

        //     plugins = plugins.filter(item => {
        //         if (recordType && item.recordTypeList && item.recordTypeList.length) {
        //             return item.recordTypeList.includes(recordType);
        //         }

        //         return true;
        //     });

        //     return plugins;
        // },

        // filterPluginsHack(plugins, describe) {
        //     // 从对象ApiName
        //     let detailObjects = (describe.detailObjectList || []).map(item => item.objectApiName).filter(item => !!item);

        //     /**
        //      * 过滤从对象描述
        //      * 插件描述指定的从对象不属于当前页面的从对象，过滤该从对象描述
        //      * 用于后续判断该插件是否过滤
        //      * 为什么会有这种下发？
        //      */
        //     plugins.forEach(item => {
        //         if (item.params && item.params.details) {
        //             item.params.details = item.params.details.filter(item => detailObjects.includes(item.objectApiName));
        //         }
        //     });

        //     /**
        //      * 过滤插件
        //      * 基于上一步操作
        //      * 插件描述有从对象结构，但没有从对象描述数据，过滤该插件
        //      */
        //     plugins = plugins.filter(item => {
        //         if (item.params && item.params.details) {
        //             return item.params.details.length > 0;
        //         }

        //         return true;
        //     })

        //     // 只有一个crm日志插件，则不加载该插件
        //     if (plugins.length === 1) {
        //         plugins = plugins.filter(item => item.pluginApiName !== 'crm-form-log');
        //     }

        //     return plugins
        // },

        //初始化关联数据
        initRelateListData() {
            let relateDataList = this.get('related_data_list');
            if(relateDataList) {
                _.each(relateDataList, (arr) => {//关联对象
                    _.each(arr, (b) => {
                        _.each(b.dataList, (c) => {
                            c.rowId || (c.rowId = this.getRowUniqueId());
                        })
                    })
                })
            } else {
                this.set('related_data_list', {});
            }
        },
        //获取指定业务类型的关联数据
        getRelateListData(opts) {
            let relateDataList = this.get('related_data_list');
            let tmp = relateDataList && _.findWhere(relateDataList[opts.objectApiName] || [], {relatedFieldName: opts.relatedFieldName});
            return _.where(tmp && tmp.dataList || [], {record_type: opts.recordType});
        },
        //g更新指定业务类型的关联数据
        updateRelateListData(dataList, oldDatas, opts) {
            let relateDataList = this.get('related_data_list');
            if(!relateDataList) {
                this.set('related_data_list', relateDataList = {});
            }
            if(relateDataList[opts.objectApiName]) {
                let tmp = _.findWhere(relateDataList[opts.objectApiName], {relatedFieldName: opts.relatedFieldName});
                if(!tmp) {
                    relateDataList[opts.objectApiName].push({
                        relatedFieldName: opts.relatedFieldName,
                        dataList
                    })
                } else {
                    tmp.dataList = _.filter(tmp.dataList, a => a.record_type !== opts.recordType).concat(dataList);
                }
            } else {
                (relateDataList[opts.objectApiName] = []).push({
                    relatedFieldName: opts.relatedFieldName,
                    dataList
                })
            }
        },

        toggleDetailsChecked(rowIds, isChecked, opts) {
            let details = this.getDetails() || {};
            if(!rowIds) {
                rowIds = [];
                if(opts && opts.objApiName) {
                    let arr = details[opts.objApiName];
                    _.each(arr, item => {
                        if(!opts.recordType || item.record_type === opts.recordType) {
                            rowIds.push(item.rowId);
                        }
                    })
                } else {
                    _.each(details, arr => {
                        _.each(arr, item => rowIds.push(item.rowId));
                    })
                }
            }
            if(!rowIds.length) return;

            
            let tmp = {}
            _.each(rowIds, rowId => {
                _.find(details, (arr, objApiName) => {
                    let tt = _.findWhere(arr, {rowId});
                    if(tt) {
                        tmp[objApiName] || (tmp[objApiName] = {});
                        (tmp[objApiName][tt.record_type] || (tmp[objApiName][tt.record_type] = [])).push(rowId);
                        return true;
                    }
                })
            })
            _.each(tmp, (item, objApiName) => {
                _.each(item, (rowIds, recordType) => {
                    this.triggerMDActionEvent(objApiName, recordType, 'toggleCheckedHandle', rowIds, isChecked);
                })
            })
        },

        // 初始化插件管理配置
        getPluginOpts() {
            let me = this;
            let recordLogObj = ['SalesOrderObj', 'QuoteObj', 'SaleContractObj', 'NewOpportunityObj'];
            let apiName = this.get('apiname');

            return {
                appId: 'CRM-' + this.get('requestId'),
                traceId: this.get('requestId'),
                isRecordLog: recordLogObj.includes(apiName),
                isPrintLog: this.isDevDomain(),
                objectApiName: apiName,
                api: {
                    //生成一个pluginService实例ps 通过ps可执行 ps.overUpdate() ps.calculate()  ps.end() 不执行
                    // const ps = pluginServiceFactory({...}) 
                    // const ps1 = pluginServiceFactory({...})// 错误用法 ps 和 ps1之间数据不互通 有数据同步问题
                    // 需要等ps.end().then(() => {const ps1 = pluginServiceFactory({.....})}) 之后才能创建另一个
                    // ps.overUpdate(objectData, detail);
                    // ps.calculate()
                    // ps.end() 必须执行，意味本次实例被收回 才能将ps执行过程中的数据更新
                    // ps实例必须是串行的
                    pluginServiceFactory(opts = {}) {
                        if (!opts.pluginApiName) {
                            opts.pluginApiName = 'custom_plugin_' + CRM.util.getUUId();
                        }

                        return me.getPluginService(Object.assign(opts, {
                            actionDescribe: {
                                from: opts.pluginApiName
                            }
                        }));
                    },
                    getOptionsValue(key) {
                        return me.get(key);
                    },
                    getZIndex() {
                        return me.get('zIndex');
                    },
                    getRowUniqueId() {
                        return me.getRowUniqueId();
                    },
                    proxyService(serviceName, param) {
                        return me[serviceName] && me[serviceName](param);
                    },
                    //选中/取消选择某些数据
                    checkDetailsRow(rowIds, isChecked, opts) {
                        me.toggleDetailsChecked(rowIds, isChecked, opts);
                    },
                    UI: {
                        toggleMDCommonButtons(opts) {
                            let buttons = [];
                            _.each(opts.actions, action => {
                                buttons.push({
                                    buttonType: 'common',
                                    action,
                                    lookupFieldName: action,
                                    hidden: !!opts.status
                                })
                            })

                            me._toggleMDButtons({
                                [opts.objApiName]: [{
                                    buttons,
                                    record_type: opts.recordType
                                }]
                            })
                        }
                    },
                    showLoading(str) {
                        CRM.util.waiting(str || ' ', true);
                        me._pluginLoadingStatus = true;
                    },
                    hideLoading() {
                        CRM.util.waiting(false, true);
                        me._pluginLoadingStatus = void 0;
                    },
                    triggerModelEvent(event, d1, d2) {
                        me.trigger(event, d1, d2);
                    }
                }
            };
        },

        isDevDomain () {
            return Fx.domain && ['ceshi112.com', 'localhost'].includes(Fx.domain);
        },

        /**
         * 通过插件提供的日志方法打印日志
         *
         * @param {array} logs 
         * @param {object} opts
         * @param {object} opts.bizLog 使用架构组日志工具上报
         */
        logByPlugin(logs = [], opts = {}) {
            try {
                let ps = this.get('pluginService');

                ps && ps.printLog && ps.printLog(logs);

                if(opts.bizLog && window.logger && window.logger.log) {
                    logs.forEach(log => {
                        window.logger.log({
                            eventId: log.action,
                            eventData: log.eventData
                        })
                    })
                }
            } catch(e) {}
        }
    }
})
