/**
 * @desc 自定义数据模型 封装了几个通用操作
 * @ author wangj
 */
define(function(require, exports, module) {

    var util = require('crm-modules/common/util');
    var Rule = require('./rule');
    var format = require('../format/format');

    var TOWN_DATA = {};
    var sss = require('./sss');
    var Model = Backbone.Model.extend(_.extend({

        initialize: function() {
            Backbone.Model.prototype.initialize.apply(this, arguments);
			this.initRequestId();
		},

		/**
         * 初始化，生成requestId，保证一次表单操作，只有唯一requestId
         */
		initRequestId() {
            if(this.get('crmRequestId')) {
                this.requestId = this.get('crmRequestId');
            } else {
                this.requestId = util.getUUIdAsMiniProgram();
            }
			this.set('requestId', this.requestId);
		},

        defaults: function() {
            return {
                _additional: {},
                param: {},
                apiname: '',
                display_name: '',
                _id: '', //对象id
                dataId: undefined, // 数据id
                version: '',
                layout_type: 'add', //add edit
                record_type: '', //业务类型
                layout: [],
                data: {}, //数据
                fields: {},
                hideFields: [],
                include_detail_describe: true,
                detailObjectList: [],
                objectData: {},
                field_remind: null, // 字段级别的红色提示
                _postid: util.getUUIdAsMiniProgram() + util.getTraceId(),
				isCacheDescribe: false, //是否缓存Describe,
				isModifyAll: false //日程是否修改全部
            };
        },

        /**
         *@desc 重写set方法 对fieldData做进一步处理
         */
        set: function(a, b) {
            this._filterLayoutsExcessiveFields(a);
            Backbone.Model.prototype.set.apply(this, arguments);
            var fieldData = _.isObject(a) ? a.fieldData : a === 'fieldData' && b;
            fieldData && fieldData.length && this._hackParseFieldData(fieldData);
        },

        abort: function() {
            var me = this;
            _.each(me, function(key) {
                me[key] && _.isFunction(me[key].abort) && (me[key].abort(), me[key] = null);
            })
        },

        _fetchFieldMapping(callBack) {
            let apiname = this.get('apiname');
            if(apiname) {
                if(!this._fetchFieldMappingCallBack) {
                    CRM.util.findRecordFieldMapping([{
                        describeApiName: apiname
                    }]).then((fiedMapping) => {
                        this.__fieldMapping = fiedMapping && fiedMapping[apiname];
                    }).finally(() => {
                        _.each(this._fetchFieldMappingCallBack, callBack => callBack());
                        this._fetchFieldMappingCallBack = null;
                    })
                }

                if(callBack) {
                    (this._fetchFieldMappingCallBack || (this._fetchFieldMappingCallBack = [])).push(callBack);
                }
            } else {
                callBack && callBack();
            }
        },

        mergeFieldMapping(recordType, fields, callBack) {
            this._fetchFieldMapping(() => {
                let a = this.__fieldMapping && this.__fieldMapping[recordType];
                _.each(a && a.fields, (b, fieldName) => {
                    let tt = fields[fieldName];
                    tt && (tt.label = b.label)
                })
                this.__fieldMapping = null;
                callBack && callBack();
            })
        },

        fetch: function() {
            var me = this;

            if(me.get('cacheDescribe')) {
                let _des = JSON.parse(me.get('cacheDescribe'));
                var customPlugins = _des?.layout?.layout_structure?.plugins?.layout;
                me.set('customPlugins', customPlugins);
                me.initPluginService({
                    plugins: me.get('plugins'),
                    describe:_des
                }).then(() => {
                    if(customPlugins) {
                        me.trigger('applyPlugin', () => {
                            me.runFetchDescribeAfterService(_des);
                        }, _des);
                    } else {
                        me.runFetchDescribeAfterService(_des);
                    }
                });
                return;
            }

            let apiname = me.get('apiname');
            let url = '/EM1HNCRM/API/v1/object/'+ apiname +'/controller/DescribeLayout';

            me._fetchFieldMapping();
            me._fetchAjax = util.FHHApi({
                url,
                data: {
                    data_id: me.get('dataId'),
                    include_detail_describe: me.get('include_detail_describe'),
                    include_layout: true,
                    apiname: apiname,
                    layout_type: me.get('layout_type'),
					recordType_apiName: me.get('record_type'),
                    management: me.get('fromManagement'),
                    signExtendData: me.get('signExtendData'), //电子签新建时需要扩展的参数
                    action_type: me.get('action_type'),
                    seriesId: me.requestId,
                    ...me.get('crmFieldextendFetchQueryParam')
                },
                success: function(res) {
                    if (res.Result.StatusCode === 0) {
                        me.mergeFieldMapping(me.get('record_type'), res.Value.objectDescribe.fields, () => {
                            var customPlugins = res.Value?.layout?.layout_structure?.plugins?.layout;
                            me.set('customPlugins', customPlugins);
                            me.afterFetchSuccess && me.afterFetchSuccess(res);
                            me.initPluginService({
                                plugins: me.get('plugins'),
                                describe: res.Value
                            }).then(() => {

                                if(me.get('isSubmitAndCreate') || res.Value.supportSaveAndCreate || customPlugins) {
                                    try {
                                        me.set('cacheDescribe', JSON.stringify(res.Value))
                                    } catch(e) {}
                                }
                                if(customPlugins) {
                                    me.trigger('applyPlugin', () => {
                                        me.runFetchDescribeAfterService(res.Value);
                                    }, res.Value);
                                }else {
                                    me.runFetchDescribeAfterService(res.Value);
                                }
                            })
                        })

                        me._uploadErrorRt(res.Value);
                        
                        return;
                    }
                    let errorMsg = res.Result.FailureMessage || $t("暂时无法获取数据!");
                    if(me.get('errorCallBack')) {
                        me.get('errorCallBack')(errorMsg);
                    } else {
                        util.alert(errorMsg);
                    }
                    me.trigger('error', res);
                },
                complete: function() {
                    me._fetchAjax = null
                },
                appId: me.get('customAppId') || '',
            }, {
                errorAlertModel: 1
            })

            this.set('_fetchDesUrl', url);
            this.set('_fetchTraceId', me._fetchAjax && me._fetchAjax._traceId);
        },

        //
        _uploadErrorRt(res) {
            try {
                let rt = res.objectData && res.objectData.record_type
                if(rt !== this.get('record_type') || rt !== this.get('data').record_type) {
                    window.logger && window.logger.performance({
                        eventId: 'paasCrmErrorRt',
                        eventType: 'error',
                        apiName: this.get('apiname'),
                        str1: rt + '|' + this.get('record_type') + '|' + this.get('data').record_type,
                        str2: this.requestId
                    }, {
                        immediate: true
                    });
                }

                window.logger && window.logger.log({
                    eventId: 'paasDescribeLog',
                    eventData: {
                        apiName: this.get('apiname'),
                        recordType: this.get('record_type'),
                        layout: {
                            ..._.pick(res.layout, ['api_name', 'events', 'display_name', 'layout_structure', 'layout_type', 'version', '_id'])
                        }
                    }
                }, {
                    immediate: true
                });
            } catch(e) {}
        },

		parseDescribe:function(res){
        	var me = this;
			var attr = me.parse(res);
			var rules = res.layout.layout_rule;
			me.set({
				fields: attr.fields,
                calculateOrderMap: attr.calculateOrderMap,
                layout_rules: rules
			});


			me.beforeSetData(attr);
            me._preFillMdAreaLabel(res).then(() => {
                me._preFillOutOwner(attr).then(outOwner => {
                    outOwner && _.extend(attr.data, outOwner);
                    me.get('isEdit') || me.get('noPreCalculate') ? me.setAttrAndReady(attr, res) : me._preCalculate(attr, () => {
                        if(me.get('isCopy')) {
                            attr && _.extend(attr.data, attr.serverCalculateData);
                            me.setAttrAndReady(attr, res)
                            return;
                        }
                        
                        if(me.get('_staticData')) {
                            attr && _.extend(attr.data, me.get('_staticData'), attr.serverCalculateData);//复制，转换的时候以传入的值为准
                        }
                        me.setAttrAndReady(attr, res);
                    });
                })
            })
		},

        //设置属性，相关数据已准备完毕，开始渲染
        setAttrAndReady: function(attr, res) {
            var layout = attr.layout;
            var details = attr.mdData;
            var masterData = attr.data;

            delete attr.layout;//避免触发表单渲染
            this.set(attr); //让插件可以取到相关参数

            this.runRenderBeforeService({details, masterData}).then((rst = {}) => {

                if(this.triggerOnLoadEvent === Model.prototype.triggerOnLoadEvent) {
                    this.trigger('toggleeditstatus', {timer: 10000});////10秒之后强制解除
                }

                if(!layout.length && this.get('formFooterSlot')) {//兼容流程布局，避免没有布局时，底部审批意见没有显示
                    layout.push({});
                }
                
                this.set({
                    data: rst.masterData || masterData,
                    mdData: rst.details || details,
                    layout: layout
                });
                if(!layout.length) {
                    this.trigger('emptylayout');
                    this.trigger('toggleeditstatus');
                }
                this.initRule(this.get('layout_rules'), res.layout, attr.fields);
            })
        },

        getFormType: function() {
            if(this.get('isEdit')) return 'edit';
            if(this.get('isCopy')) return 'clone';

            return this.get('_from') || 'add';
        },

        beforeSetData:$.noop,

        initRule: function(rules, layouts, fields) {
            if(!rules || !rules.length) return;
            var me = this;
            var temp = {};
            if(layouts.components) {
                _.each(layouts.components, function(a) {
                    _.each(a.field_section, function(b) {
                        _.each(b.form_fields, function(c) {
                            temp[c.field_name] = _.extend({
                                api_name: c.field_name
                            }, c)
                        })
                    })
                    _.each(a.rows, (r) => {
                        _.each(r.cells, ce => {
                            _.each(ce.components, d => {
                                if(d.field_name) {
                                    temp[d.field_name] = {
                                        ...d,
                                        type: void 0,
                                        api_name: d.field_name
                                    }
                                }
                            })
                        })
                    })
                })
            } else {
                _.each(layouts, function(item) {
                    _.each(item.components, function(a) {
                        temp[a.api_name] = a;
                    })
                })
            }

            var rule = new Rule({
                layouts: temp,
                rules: rules,
                fields: fields
            });
            if(rule.isEmpty()) return;

            me._rule = _.debounce(function() {
                me.trigger('rule', rule.getRule(me._ruleData, false, me.get('data')));
                me._ruleData = {}; //清空
            }, 200)

            me._ruleData = {};

            me.on('_initRule', function() {
                me.trigger('rule', rule.getRule(_.extend(rule.getEmptyData(), me.get('data')), true), true);
            })

            me.set('__masterRuleInstance', rule);
        },

        excuteMasterRule(data) {
            let rule = this.get('__masterRuleInstance');
            return rule && rule.getRule(_.extend(rule.getEmptyData(), data), true);
        },

        submit: function(data, btn) {
            const me = this;
            this._continueParam = null;
            var param = this.formatParam(data);
            //////自由审批//////
            if(me.get('freeApprovalDef')
              &&  me.get('freeApprovalDef').tasks
              && !me.get('freeApprovalDef').tasks.length){
                util.alert($t('flow.approval.freeApprovalDef.alert','该对象开启了自由流程,请选择审批人'))
                return;
            } else {
                param.freeApprovalDef = me.get('freeApprovalDef');
            }
            /////////////
            var res = this.checkDataBySelf(param);
            if(!res)return;

            this._filterExtraAttr(param);

            if (this.checkDataAsyncBySelf) {
                me.checkDataAsyncBySelf(param).then((flag) => {
                    if (!flag) return;
                    me.get('isEdit') ? me.update(param, btn) : me.create(param, btn);
                    me.__uploadNewformLog();
                })
            } else {
                this.get('isEdit') ? this.update(param, btn) : this.create(param, btn);
                this.__uploadNewformLog();
            }
        },

        //保存并跳过所有检验 相当于暂存数据
        submitAndSkipValidate(data, $btn) {
            var param = this.formatParam(data);

            param.optionInfo = _.extend({}, param.optionInfo, {
                skipVersionCheck: true, //版本校验
                skipCheckCleanOwner: true, //预置对象特殊
                skipFuncValidate: true, //函数
                useValidationRule: true, //验证规则
                isDuplicateSearch: false //查重
            })

            param.object_data.IsDuplicateSearch = false;//老对象特殊参数

            this.get('isEdit') ? this.update(param, $btn) : this.create(param, $btn, true);
        },

		checkDataBySelf:function(data){
        	return true
		},

        //内部使用，验证规则等会有继续保存
        _continueSubmit: function(param, btn) {
            var cp = this._continueParam;
            if(cp) {
                param.optionInfo = _.extend(cp, param.optionInfo);
            }
            this._continueParam = _.extend({}, param.optionInfo);


            if(param.optionInfo && param.optionInfo._funcValidateData) {
                _.extend(param, param.optionInfo._funcValidateData);
                delete param.optionInfo._funcValidateData;
            }

            this.get('isEdit') ? this.update(param, btn) : this.create(param, btn);
        },

        formatParam: function(data) {
            var isDuplicate = this.get('isDuplicate');
            var cleanOwner = this.get('cleanOwner');
            var details = data.details;
            var isEdit = this.get('isEdit');
            data.details = void 0;
            var param = _.extend({
                record_type: this.get('record_type')
            }, this.get('param'), data);

            if (isEdit) {
                param.version = this.get('version');
                param._id = this.get('dataId');
                param.__changed_data_id__ = this.get('__changed_data_id__');
            }

            //将传过来的关联数据强制附加到数据上 解决关联字段不可见但是仍需要关联的情况
            var re = this.get('_relationData');
            _.each(re, function(v, k) {
                param[k] || (param[k] = v);
            })

            _.isUndefined(isDuplicate) || (param.IsDuplicateSearch = isDuplicate);

            var trimData = this.trimData;
            var _fixSysData = this._fixSysData;
            var me = this;
            if(details) {
                _.each(details, function(list, k) {
                    me._filterDetailsDisabledValue(list, k);
                    _.each(list, function(item, index) {
                        list[index] = _fixSysData(trimData(item));
                    })
                })
            }

            //一些被隐藏掉的默认值需要提交
            var mn = this.get('_masterNeedSubmit');
            if(mn) {
                var _data = this.get('data');
                var that = this;
                _.each(mn, function(k) {
                    if(param[k] === void 0 && _data[k] !== void 0) {
                        param[k] = _data[k];
                        if(!_data[k]) return;
                        var isOther = _.isArray(_data[k]) ? _.contains(_data[k], 'other') : _data[k] == 'other';
                        if(isOther && _data[k + '__o']) param[k + '__o'] = _data[k + '__o'];
                    }
                })
            }


            //将服务端的返回的一些默认值提给server
            //主要是防止当字段在布局里隐藏时，如果不将值提交给server
            //server会把该字段存为空值，如果有其他对象引用该字段作为计算参数无法计算
            var od = this.get('_originServerData');
            if(!this.get('isEdit') && od && _.isObject(od)) {
                var temp = {};
                _.each(od, function(v, k) {
                    if(v === '' || v === null) return;

                    // 临时处理公海新建复制客户不传owner
                    if (me.get('apiname') === 'AccountObj' && me.get('pageApiname') === 'HighSeasObj' && k === 'owner') {
                        return;
                    };

                    temp[k] = v;
                    var isOther = _.isArray(v) ? _.contains(v, 'other') : v == 'other';
                    if(isOther && od[k + '__o']) temp[k + '__o'] = od[k + '__o'];
                })
                param = _.extend(temp, param);
            }

            // requestId用于串联整个新建编辑生命周期日志
            param.requestId = this.get('requestId');

            param.related_data_list = void 0;
            return {
                object_data: _fixSysData(this._fixDateVal(trimData(param))),
                skipCheckCleanOwner: cleanOwner,
                details: me._filterDetailsUnlessAttrs(details),
                related_data_list: data.related_data_list,
                maskFieldApiNames: this._getCalculateEncryptFields(),
                action_type: this.get('action_type'),
                seriesId: me.requestId,
                fillMultiLang: true
            }
        },

        //修复编辑时 时间 日期 日期时间值变化问题
        //编辑时server传过来的值可能是带了毫秒数的
        //但前端提交给server的时候值是从日期组件上取的，会抹去对应的毫秒数
        //造成的问题是：端上没有进行数据修改，但server会判断为数据被改了，因为抹去了毫秒数
        //由于以上原因，需要对比当前的值和原始值，看是否有变化
        //已迁移到新组件，不再有此问题
        _fixDateVal: function(data) {
            return data;
            // if(!this.get('isEdit')) return data;

            // var fields = {};
            // _.each(this.get('layout'), function(a) {
            //     _.each(a.components, function(b) {
            //         fields[b.api_name] = b;
            //     })
            // })

            // var od = this.get('__originData');
            // if(!od) return data;

            // var types = ['date_time', 'time'];
            // _.each(data, function(v, k) {
            //     var tmp = fields[k];
            //     if(!tmp) return;
            //     var vv = od[k];
            //     if(v && vv && _.contains(types, tmp.type) && format(tmp, v) === format(tmp, vv)) {
            //         data[k] = vv;
            //     }
            // })

            // return _.extend({}, od, data);
        },

        _filterDetailsDisabledValue: function(details, apiname) {
            var disableds = this.get('mdDisabledOptions');
            if(!disableds || !disableds[apiname]) return;
            disableds = disableds[apiname];

            _.each(details, function(item) {
                _.each(item, function(v, k) {
                    if(v && disableds[k]) {
                        if(_.isString(v)) { //单选的禁用
                            _.contains(disableds[k], v) && (item[k] = null);
                        } else { //多选的禁用
                            item[k] = _.difference(v, disableds[k]);
                        }
                    }
                })
            })
        },

        //过滤掉不用提交给server的属性，避免数据过大，许多属性只是端上自用的
        _filterDetailsUnlessAttrs: function(details) {
            if(!details) return details;

            var ddm = {};
            _.each(details, function(list, k) {
                ddm[k] = _.map(list, function(item) {
                    return _.extend({}, item, {
                        __rules: void 0,
                        _cellStatus: void 0
                    })
                })
            })

            return ddm;
        },

        //去除参数里面的前后空格
        trimData: function(obj) {
            var trim = _.string.trim;
            var data = {};
            _.each(obj, function(v, k) {
                data[k] = v && _.isString(v) ? trim(v) : v;
            })
            return data;
        },

        //去除一些系统自带的属性值
        _fixSysData: function(data) {
            return _.omit(data, ['last_modified_by', 'last_modified_by__r', 'last_modified_time',  'relevant_team', 'relevant_team__r']);
        },

        //去除多余提交数据
        _filterExtraAttr(submitData, deepClone) {
            if (!util.getUserAttribute('paasFormFilterExtraAttr')) {
                return submitData;
            }

            try {
                if (deepClone) {
                    if (window.structuredClone) {
                        submitData = window.structuredClone(submitData);
                    } else {
                        submitData = JSON.parse(JSON.stringify(submitData));
                    }
                }

                let keys = ['__fields', '_cellStatus', '__srules', '__rules'];
                let inner = (data) => {
                    _.each(data, (v, k) => {
                        if(_.contains(keys, k) || /__(?:r|l)$/.test(k)) {
                            data[k] = void 0;
                        } else if(_.isArray(v) && _.find(v, a => a && a.path && a.signedUrl)) {
                            data[k] = _.map(v, a => {
                                return {...a, signedUrl: void 0}
                            })
                        }
                    })
                }

                inner(submitData.object_data);

                _.each(submitData.details, arr => {
                    _.each(arr, obj => inner(obj));
                })

            } catch (error) {
                console.error(error);
            }

            return submitData;
        },

        _getPostId: function() {
            var id = this.get('_postid');
            return id ? '?_postid=' + id : ''
        },

		beforeAddSubmit: $.noop,

        create: function(param, btn, noValid) {
            if(!noValid && !this.__validateDataRequire(param.object_data)) return;

            this._addOptionInfo && this._addOptionInfo(param, btn);
			this.beforeAddSubmit && this.beforeAddSubmit(param);

            var submitData = this.parseSubmitParam(param);

            if (this.get('createDataHandler')) {
                this.get('createDataHandler')({
                    btn,
                    param,
                    data: submitData,
                    operationType: 'add',
                    submitSuccess: (res) => {
                        this.submitSuccess(res, param, btn, submitData);
                    },
                    submitError: (res, noPrompt) => {
                        this.submitError(res, param, btn, noPrompt);
                    },
                    formDestroy: () => {
                        this.trigger('destroy');
                    }
                });
            }
            else {
                this._create(param, submitData, btn);
            }
        },

        _create: function(param, data, btn) {
            var me = this;
            
            if (me.createAjax) return;
            
            var $waiting = me.commonWaiting($t('提交中') + '...');

            me.createAjax = util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/' + me.get('apiname') + '/action/Add' + me._getPostId(),
                data: data,
                success(res) {
                    res.Result.StatusCode === 0 ? me.submitSuccess(res, param, btn, data) : me.submitError(res, param, btn);
                },
                complete() {
                    me.createAjax = null;
                    $waiting.remove();
                },
                error() {
                   $waiting.remove();
                }
            }, {
                submitSelector: btn,
                errorAlertModel: 1
            })
        },

        //追加新建来源
        _addOptionInfo: function(param, btn) {
            var source = this.get('_from');
            var type = {
                draft: 'fromDraft',
                clone: 'fromClone',
                mapping: 'fromMapping',
                transform: 'fromTransform',
                referenceCreate: 'fromReferenceCreate'
            }[source];

            let extendsLogInfo = {...this.get('extendsLogInfo')};
            if(this.get('oldDraftData')) {//记录是草稿箱新建
                extendsLogInfo.draftId = this.get('oldDraftData').id;
            }
            if(btn && $(btn).data('action') === 'submitAndCreate') {//记录是保存并新建
                extendsLogInfo.isSaveAndCreate = true;
            }

            if(!type) {
                _.isEmpty(extendsLogInfo) || ((param.optionInfo ||  (param.optionInfo = {})).extendsLogInfo = extendsLogInfo);
                return;
            }

            (param.optionInfo || (param.optionInfo = {}))[type] = true;

            param.optionInfo.extendsLogInfo = extendsLogInfo;

            if(type === 'fromClone') {
                param.optionInfo.fromId = this.get('dataId');
                param.optionInfo.fromApiName = this.get('apiname');
            } else if(type === 'fromMapping') {
                param.optionInfo.fromId = this.get('_sourceId');
                param.optionInfo.fromApiName = this.get('_sorrceApiName');
            } else if (type === 'fromTransform' || type === 'fromReferenceCreate') {
                Object.assign(param.optionInfo, this.get('_extOptionInfo') || {});
            }
        },

        update: function(param, btn) {
            var me = this;
			if(me._calculateMDAjax || me._analysisAjax )return;

            var submitData = me.parseSubmitParam(_.extend({}, param, me._diffVersionOriginalData(param)));

            if (me.get('updateDataHandler')) {
                me.get('updateDataHandler')({
                    btn,
                    param,
                    data: submitData,
                    submitSuccess: (res) => {
                        me.submitSuccess(res, param, btn, submitData);
                    },
                    submitError: (res, noPrompt) => {
                        me.submitError(res, param, btn, noPrompt);
                    },
                    formDestroy: (data) => {
                        this.trigger('destroy', data);
                    }
                });
            }
            else {
                me._update(param, submitData, btn);
            }
        },

        _update: function(param, data, btn) {
            var me = this;
			var editText = me.get('isModifyAll') ? '/action/EditCurrentAndAfter' : '/action/Edit';
            if (me.updateAjax) return;
            if (_.contains(['transform', 'referenceCreate'], me.get('_from'))) {
                me._addOptionInfo && me._addOptionInfo(data);
            }

            var $waiting = me.commonWaiting($t('提交中') + '...');
            me.updateAjax = util.FHHApi({
				url: '/EM1HNCRM/API/v1/object/' + me.get('apiname') + editText + me._getPostId(),
                data: data,
                operationType: 'edit',
                success: function(res) {
                   res.Result.StatusCode === 0 ? me.submitSuccess(res, param, btn, data) : me.submitError(res, param, btn);
                },
                complete: function() {
                    me.updateAjax = null;
                    $waiting.remove();
                },
                error() {
                   $waiting.remove();
                }
            }, {
                submitSelector: btn,
                errorAlertModel: 1
            })
        },

        parseSubmitParam(param) {
            if(this.__beforeSubmitParseParam) {
                param = this.__beforeSubmitParseParam(param);
            }

            let config = this.get('fieldCheckCalculationConfig') || {};
            if(config) {
                if(param.details) {
                    let dcf = {};
                    let index = 0;
                    let nDetails = {};
                    _.each(param.details, (arr, objApiName) => {
                        nDetails[objApiName] = _.map(arr, item => {
                            item.dataIndex = index;
                            let tm = config[item.rowId];
                            if(tm) {
                                let ta = (dcf[objApiName] || (dcf[objApiName] = {}));
                                let keys = [];
                                _.each(tm, (v, k) => {
                                    if(v === 1 && !/__(?:r|l|s|encrypt)$/.test(k)) {
                                        keys.push(k);
                                    }
                                });
                                ta[index] = keys;
                            }
                            
                            index++;
    
                            return _.extend({}, item, {
                                __fields: void 0,
                                __rules: void 0,
                                __srules: void 0,
                                _cellStatus: void 0
                            }); 
                        })
                    })
                    if(!_.isEmpty(dcf)) {
                        param.detailCalculateFields = dcf;
                    }
                    param.details = nDetails;
                }
    
                let mk = [];
                _.each(config.master, (v, k) => {
                    if(v === 1 && !/__(?:r|l|s|encrypt)$/.test(k)) {
                        mk.push(k);
                    }
                })
    
                mk.length && (param.masterCalculateFields = mk);
            };
            
            (param.optionInfo || (param.optionInfo = {})).supportValidationResult = true;//是否支持validationResult 前端写死

            return param;
        },

        //判断是否有验证规则
        assertValidationRule: function(obj) {
			return obj && ((obj.blockMessages && obj.blockMessages.length) || (obj.nonBlockMessages && obj.nonBlockMessages.length));
        },

        parseValidationRule: function(obj, submitData) {
            var messages = [];
            if(obj.blockMessages && obj.blockMessages.length) {
                let item = {
                    type: 'red',
                    content: obj.blockMessages.join('\n')
                }
                if(submitData && submitData.details && obj.matchDataIndex && obj.matchDataIndex.length) {//从对象阻断验证规则，得到报错行的rowId
                    _.find(obj.matchDataIndex, dataIndex => {
                        return _.find(submitData.details, arr => {
                            let t = _.find(arr, a => a.dataIndex == dataIndex);
                            if(t) {
                                item.matchDataIndex = t.rowId;//目前只处理一条，server也只会返回一条
                                return true;
                            }
                        })
                    })
                }
                messages.push(item);
            }
            if(obj.nonBlockMessages && obj.nonBlockMessages.length) {
                messages.push({
                    type: 'orange',
                    content: obj.nonBlockMessages.join('\n')
                })
			}
			if(obj.needConfirmCollect) {
				messages.push({
					content: $t('保存后将被自动归集')
				})
			}

            return messages;
        },

        //提交成功之后的处理函数
        submitSuccess: function(res, param, btn, submitData) {
            var rv = res.Value;
            var ob = rv.objectData;
            var vr = rv.validationRuleMessage;
			var fr = rv.funcValidateMessage;
			var needConfirmCollect = rv.needConfirmCollect;

            this.trigger('hideValidatVeConfirm');

            //命中了自定义插件验证规则
            if(rv.validationResult) {
                this.trigger('validaterule', [{content: rv.validationResult.message, type: rv.validationResult.block ? 'red' : 'orange'}] , param, null, res.Value, true);
                return;
            }

            //命中了一些验证规则，需要提示用户
            if(this.assertValidationRule(vr)) {
                this.trigger('validaterule', this.parseValidationRule(vr, submitData) , param);
                return;
			}

			//命中了重复线索验证规则，需要提示用户
			if (needConfirmCollect) {
				this.trigger('validaterule', this.parseValidationRule({
					needConfirmCollect: needConfirmCollect
				}, submitData), $.extend(true, param, {
					collectConfirmed: true,
					collectedToLeadsId: res.Value.collectedToLeadsId,
					needMarkObjectIdList: res.Value.needMarkObjectIdList,
					refreshVersion: res.Value.refreshVersion,
					duplicatedProcessingId: res.Value.duplicatedProcessingId,
					object_data: res.Value.object_data
				}));
				return;
			}

            //命中了验证函数规则，需要提示用户
            if(this.assertValidationRule(fr)) {
                this.trigger('validaterule', this.parseValidationRule(fr, submitData) , param, true, res.Value);
                return;
            }

            //命中了查重 //预置老对象的查重逻辑
            if(rv.isDuplicate || (ob && ob.IsDuplicate) || (ob && ob.success && ob.success.IsDuplicate) ) {
                param.object_data = _.extend({}, param.object_data, rv.objectData);
                this.duplicate(param);
                return;
            }

            // 命中负责人清空规则 提示清空负责人
            if (rv.cleanOwner) {
                this.trigger('cleanOwner', param);
                return;
            }

            // 命中的版本冲突校验
            if(rv.versionCheckBlocked) {
                this.showVersionCheckInfo(rv, param);
                return;
            }

            //命中了单独新建从对象触发了主的审批，出提示
            if(rv.masterApprovalResult && rv.masterApprovalResult.masterData) {
                rv.objectData._id && (rv.objectData._id = null);
                this.showMasterApprovalTip(rv.masterApprovalResult);
            } else {
                res.noRemind || util.remind(1, $t("操作成功"));
            }

            let _innerFn = (rst) => {
                if(rst && rst.noShowDetail) {
                    window.crmFieldPluginNoShowDetail = true;
                    setTimeout(() => {
                        delete window.crmFieldPluginNoShowDetail;
                    })
                }
                this.trigger('success', _.extend({}, this.get('data'), param.object_data, this.get('_additional'), ob || rv, {changeOrderApiName: rv.changeOrderApiName, changeOrderDataId: rv.changeOrderDataId}), this.toJSON(), res);
                
                if(window.crmFieldPluginNoShowDetail) {
                    delete window.crmFieldPluginNoShowDetail;
                }
                
                //6.8.0草稿箱逻辑，新建完之后删除草稿箱
                if(this.get('oldDraftData')) {
                    CRM.api.del_draft({
                        object_describe_api_name: this.get('apiname'),
                        draftID: this.get('oldDraftData').id
                    })
                }
                let sc = this.get('submitSuccessCallBack');
                sc && sc(res);
            }

            this.runAfterSubmit(res).then(rst => {
                if(res.beforeSuccessCallback) {
                    res.beforeSuccessCallback().then(() => _innerFn(rst))
                } else {
                    _innerFn(rst);
                }
            })
        },

        //提交失败之后的处理函数
        submitError: function(res, param, btn, noPrompt) {
            !noPrompt && util.alert(res.Result && res.Result.FailureMessage ? _.escape(res.Result.FailureMessage) : $t("操作失败"));
            this.trigger('error', res);
        },

        duplicate: function(param) {
            var opts = {
                apiname: this.get('apiname'),
                data: param.object_data,
                objectName: this.get('display_name'),
                ignoreId: param.object_data._id || '',
                duplicateConfig: this.get('duplicateConfig'),
                isEdit: this.get('isEdit')
            }
            this.trigger('duplicate', opts, param);
        },

        isOldObject: function(apiname) {
            return util.isCrmPreObject(apiname || this.get('apiname'));
        },

        isRequired: function(apiname) {
            var obj = this.get('fields')[apiname];
            return obj && obj.is_required;
        },

        isReadonly: function(apiname) {
            var obj = this.get('fields')[apiname];
            return obj && obj.is_readonly;
        },

        getLabelByApiname: function(api_name) {
            var obj = this.get('fields')[api_name];
            return obj && obj.label;
        },

        setParam: function(key, value) {
            var obj = this.get('param');
            _.isObject(key) ? _.extend(obj, key) : (obj[key] = value);
        },

        getParam: function(key) {
            var obj = this.get('param');
            return obj[key];
        },

        getData: function(apiname) {
            return this.get('data')[apiname];
        },

		valid_hooks: function () {
			return true
		},

        setData: function(data, noTrigger, isBlur, noCalculate) {
            var obj = this.get('data');
            var value = data.value;
            var key = data.apiname;
            var oldData = obj[key];
            var oldValue = data._oldValue || null;

            if (oldData === value || (this.dataIsNull(oldData) && this.dataIsNull(value))) {
                if(this.__triggerOnloadUIDataResult && this.__triggerOnloadUIDataResult[key]) {
                    delete this.__triggerOnloadUIDataResult[key];
                }
                return;
            };
			if (!this.valid_hooks(key, value, data, oldData)) return;
            if(isBlur) this.set('formDataChange', true);
            this._setHisData(obj[key], key);
            obj[key] = value;

            this.triggerRule({[key]: value});
            this.cascadeChangeTips();
            if(noTrigger) {
                if(this.__triggerOnloadUIDataResult && this.__triggerOnloadUIDataResult[key]) {
                    delete this.__triggerOnloadUIDataResult[key];
                }
                return;
            };

            if(!noCalculate && isBlur) {
                this.__setMasterCalStatus(key);
            }

            this.runDataChangeService({
                fieldName: key,
                newValue: value,
                oldValue: oldData,
                exOpts: {isBlur, noCalculate, data, key}
            })
            this.excuteFns(data);
            this.change(key, value, data, oldData, oldValue);
        },

        __setMasterCalStatus(fieldName) {
            let flag = this.assertFiledInEvents(fieldName) || (this.getFieldAttr(fieldName) || {}).calculate_relation;
            this._masterCalculating = !!flag;
        },

        change:$.noop,

        triggerRule: function(obj) {
            this._rule && (_.extend(this._ruleData, obj), this._rule());
        },

        _differenceData: function(obj, flag) {
            var data = this.get('data');
            var fields = this.get('fields');
            var temp = {};
            _.each(obj, function(v, k) {
                var to = fields[k];
                if(obj[k + '__encrypt'] !== void 0) {
                    temp[k] = v;
                    return;
                }
                if(to && to.type === 'quote') {
                    temp[k] = v;
                    return;
                }
                if(to && (to.type === 'select_one' || to.type === 'select_many') && !_.isUndefined(obj[to.api_name + '__o'])) {
                    temp[k] = v;
                    return;
                }
                if(flag && to && _.contains(['country', 'province', 'city', 'district'], to.type)) {
                    temp[k] = v;
                    return;
                }
                if(v === data[k]) return;
                temp[k] = v;
            })

            return temp;
        },

        _setHisData: function(v, k) {
            var obj = this.get('__hisData');
            if(!obj) {
                obj = {};
                this.set('__hisData', obj);
            }
            obj[k] = v === void 0 ? null : v;
        },

        //判断主对象是否在计算中
        masterIsCalculating() {
            return this._masterCalculating;
        },

        triggerCalculate: function(key, isBlur, changeData, mdChangeData) {
            var me = this;
            if(!me._analysis) {
                me._analysis = _.debounce(() => {
                    var _data = {};
                    var mdChangeDatas = [];
                    let exCalParam;
                    let parseParams = [];
                    if(me._analysisKeys) {
                        (me._analysisList || (me._analysisList = [])).push({
                            keys: _.keys(me._analysisKeys),
                            blurKey: me._blurKey
                        })
                        _.each(me._analysisKeys, a => {
                            _.extend(_data, a.changeData);
                            if(a.mdChangeData) {
                                mdChangeDatas.push(a.mdChangeData);
                                if(a.mdChangeData.exCalParam) {
                                    exCalParam = {...a.mdChangeData.exCalParam}
                                }
                                if(a.mdChangeData.parseParam) {
                                    parseParams.push(a.mdChangeData.parseParam);
                                }
                            }
                        });
                        me._analysisKeys = null;
                        me._blurKey = '';
                    }
                    if(me._calculating) return;//正在计算中


                    var listItem = me._analysisList && me._analysisList.pop();
                    if(!listItem) return;

                    me._calculating = true;

                    var keys = listItem.keys;
                    var valueFields = _.clone(keys);

                    me.beforeAnalysisCalculte(keys, listItem.blurKey).then(mixData => {
                        if(!_.isEmpty(_data)) {
                            if(!mixData) {
                                mixData = {masterData: _data};
                            } else {
                                mixData.masterData = _.extend({}, mixData.masterData, _data);
                            }
                        }
                        if(mixData) {
                            keys = keys.concat(_.keys(mixData.masterData));
                            keys.__data = _.extend({}, me.get('data'), mixData.masterData);
                        }

                        var masterApiname = me.get('apiname');
                        var isDobj = /__c$/.test(masterApiname);

                        me[isDobj ? 'analysisCalculte20': 'analysisCalculte'](keys, (res, exOpts, serverRes) => {
                            me._calculating = false;
                            me._masterCalculating = false;
                            if(exOpts === false || (exOpts && exOpts.isBack)) { //计算出现问题，回滚
                                me.__backFieldData(listItem.keys);
                                me._analysisList = null;
                                return
                            }

                            try {
                                _.isEmpty(res) || me.trigger('mdData:change', res, serverRes);
                            } catch(e) {}

                            var t = res[masterApiname];
                            t = t && t[0];
                            if(mixData) {
                                t = _.extend(mixData.masterData, t || {});
                            }
                            if(t) {
                                var ct = t;
                                t = me._differenceData(t);
                                try {
                                    me.triggerRule(t);
                                } catch(e) {}

                                _.extend(me.get('data'), t);
                                me._calResult = true;
                                _.each(t, (v, k) => {
                                    if (v === void 0) return;
                                    try {
                                        me.excuteFns({
                                            apiname: k,
                                            value: v,
                                            valueFields: valueFields,
                                            isCalculate: true
                                        }, true, true)
                                    } catch(e) {}
                                })

                                me._calResult = null;

                                me._afterFillOutOwner(ct);
                            }
                            if(isDobj) {
                                setTimeout(() => {
                                    me._analysis();
                                }, 16)
                            }
                        }, me, listItem.blurKey, (opts, calRes, uiRes, error) => {
                            me._calculating = false;
                            me._masterCalculating = false;
                            //主对象计算接口返回数据计算完成
                            setTimeout(() => {
                                me._analysis();
                            }, 16);
                            if(error === true) {//回滚数据
                                me.__backFieldData(listItem.keys);
                                me._analysisList = null;
                            }
                            me.masterCalculateSuccess(opts, keys, calRes, uiRes);
                        }, (opts, calResult, uiResult, isError) => {
                            me.set('__hisData', null);
                            
                            if(isError === true) return;

                            let ddm = opts && (opts.ddm || opts.detailDataMap);
                            me.__setFieldCheckCalculationConfig({detailDataMap: ddm, calResult, uiResult, changeFields: keys});
                            me.runMasterEndPlugin({
                                mdChangeDatas,
                                blurField: listItem && listItem.blurKey,
                                calResult,
                                uiResult,
                                keys,
                                ddm
                            })
                        }, exCalParam, parseParams.length ? (param) => {
                            _.each(parseParams, fn => param = fn(param));
                            return param;
                        } : void 0)
                    })
                }, 150);
            }
            //收集同一时间内触发计算的字段，合并为一次计算
            (me._analysisKeys || (me._analysisKeys = {}))[key] = {changeData, mdChangeData};
            isBlur && (me._blurKey = key);

            //开始计算
            me._analysis();
        },

        //存贮字段是否被计算过，或手动变更过，方便给保存接口传相关参数，告诉server那些字段需要重新验算
        __setFieldCheckCalculationConfig(opts) {
            try {
                let config = this.get('fieldCheckCalculationConfig');
                if(!config) {
                    this.set('fieldCheckCalculationConfig', config = {
                        master: {}
                    })
                }
                let masterApiName = this.get('apiname');
                let detailDataMap = opts.detailDataMap || {};

                _.each(opts.changeFields, k => config.master[k] = 0)
                _.each(opts.calResult, (item, objApiName) => {
                    if(objApiName === masterApiName) {
                        _.each(item[0], (v, k) => {
                            config.master[k] = 1;
                        })
                    } else {
                        _.each(item, (upDateTrData, rowId) => {
                            let trData = (detailDataMap[objApiName] || {})[rowId];
                            if(trData && trData.rowId) {//基于新的md20才做
                                let tm = config[trData.rowId];
                                if(!tm) {
                                    tm = config[trData.rowId] = {}
                                }
                                _.each(upDateTrData, (v, k) => {
                                    tm[k] = 1;
                                })
                            }
                        })
                    }
                })
                _.each(opts.uiResult, (item, objApiName) => {
                    if(objApiName === masterApiName) {
                        _.each(item, (v, k) => config.master[k] = 0)
                    } else {
                        _.each(item.u, (upDateTrData, rowId) => {
                            let trData = (detailDataMap[objApiName] || {})[rowId];
                            if(trData && trData.rowId) {//基于新的md20才做
                                let tm = config[trData.rowId];
                                tm && _.each(upDateTrData, (v, k) => tm[k] = 0)
                            }
                        })
                    }
                })
            } catch(e) {}
        },

        __mergePluginFieldCheck(opts) {
            if(!opts) return;
            let config = this.get('fieldCheckCalculationConfig');
            if(!config) {
                this.set('fieldCheckCalculationConfig', opts);
                return;
            }
            _.each(opts, (attr, k) => {
                if(config[k]) {
                    _.extend(config[k], attr);
                } else {
                    config[k] = attr;
                }
            })
        },

        //回滚字段数据数据
        __backFieldData(keys) {
            let hisData = this.get('__hisData');
            if(!hisData) return;

            _.each(this._analysisList, a => {
                _.each(a.keys, k => keys.push(k));
            })

            let tmp = {};
            _.each(keys, key => {
                if(_.has(hisData, key)) {
                    tmp[key] = hisData[key];
                    if(_.has(hisData, key + '__r')) {
                        tmp[key + '__r'] = hisData[key + '__r'];
                    }
                }
            })

            this.newBatchUpdate(tmp, true);

            this.set('__hisData', null);
        },

        beforeAnalysisCalculte: function(keys, blurkey) {
            var attr = this.getFieldAttr(blurkey) || {};
            var data = this.get('data');
            return new Promise((resolve) => {
                var ft = (this.get('allLayouts') || {}).out_owner;
                if((data.out_owner || []).length && ft && !ft.is_readonly) return resolve();//如果有数据，外部负责人在布局里，并且不是只读的，那就以当前数据为准

                var dataId = data[blurkey];

                if(!this.get('isEdit') && this.get('layout_apiname') && attr.relation_outer_data_privilege === 'outer_owner' && attr.type === 'object_reference') { //回填外部负责人
                    if(dataId) {
                        this.fetchObjectOutOwner(dataId, attr.target_api_name).then(outOwner => {
                            resolve( {masterData: outOwner || {
                                out_owner: [],
                                out_owner__l: []
                            }});
                        })
                    } else { //清空
                        resolve({
                            masterData: {
                                out_owner: [],
                                out_owner__l: []
                            }
                        })
                    }
                } else {
                    resolve();
                }
            })
        },

        // //主对象计算接口返回数据计算完成钩子函数
        masterCalculateSuccess:$.noop,
        //
        //
        // 触发字段事件
        excuteFns: function(data, triggerSelf, noTrigger) {
            var fns = this._fns;
            if (!fns) return;
            var key = data.apiname;
            _.each(fns.all, function(fn) {
                fn(data);
            })
            _.each(fns[key], function(fn) {
                fn(data);
            })
            var temp = this.get('fields')[key];
            temp && temp.render_type !== 'location' && _.each(fns[temp.render_type], function(fn) {
                fn(data);
            })
            triggerSelf && _.each(fns['_self_' + key], function(fn) {
                fn(data, noTrigger);
            })
        },

        onlyUpdateValue: function(obj) {
            var fns = this._fns;
            if (!fns) return;
            obj = this._differenceData(obj);
            if(_.isEmpty(obj)) return;
            _.extend(this.get('data'), obj);
            this.triggerRule(obj);
            _.each(obj, function(v, k) {
                _.each(fns['_self_' + k], function(fn) {
                    fn({value: v}, true);
                })
            })
        },

        batchUpdate: function(item, noTrigger) {
            if(noTrigger) {
                this.onlyUpdateValue(item);
                return;
            }
            var me = this;
            var un = void 0;
            var obj = this._differenceData(item, true);
            var fns = me._fns;
            if (!fns) return;
            _.each(obj, function(v, k) {
                if(v === un) return;
                _.each(fns['_self_' + k], function(fn) {
                    fn({
                        value: v
                    });
                })
            })
        },

        //国家省市区数据如果不全，需要补齐。
        _hackAreaUIData: function(obj) {
            var fields = this.get('fields');
            var data = this.get('data');
            try {
                _.each(obj, function(v, k) {
                    var t = fields[k];
                    if(!t) return;
                    var ca = t.cascade_parent_api_name;
                    if(t.type === 'province') {
                        obj[ca] !== void 0 || (obj[ca] = data[ca])
                    } else if(t.type === 'city') {
                        obj[ca] !== void 0 || (obj[ca] = data[ca]);
                        var t0 = fields[ca];
                        var ca0  = t0.cascade_parent_api_name;
                        obj[ca0] !== void 0 || (obj[ca0] = data[ca0]);
                    } else if(t.type === 'district') {
                        obj[ca] !== void 0 || (obj[ca] = data[ca]);
                        var t00 = fields[ca];
                        var ca00  = t00.cascade_parent_api_name;
                        obj[ca00] !== void 0 || (obj[ca00] = data[ca00]);
                        var t1 = fields[ca00];
                        var ca1  = t1.cascade_parent_api_name;
                        obj[ca1] !== void 0 || (obj[ca1] = data[ca1]);
                    }
				})
            } catch(e) {
                console.error(e);
            }

            return obj;
        },

        //把要更新的数据格式化为一个有序的list 保证在同时更新级联关系数据时，先更新父 再更新子。
        //如果不保证顺序，可能会导致先更新子，再更新父时会触发清空子的钩子，导致子更新不上
        _obj2arr: function(obj, fields) {
            var list = [];
            _.each(obj, function(v, k) {
                list.push({
                    v: v,
                    k: k
                })
            })

            return _.sortBy(list, function(a) {
                var cc = fields[a.k];
                return cc && cc.cus_parents ? cc.cus_parents.length : 0;
            })
        },

        //补全加密字段对应的值，方便触发字段的更新
        _suppEncryptData(calResult, uiData) {
            function _inner(item) {
                _.each(item, (a, k) => {
                    if(/__encrypt$/.test(k)) {
                        item[k.replace(/__encrypt$/, '')] = '';
                    } else if(/__s$/.test(k)) {
                        item[k.replace(/__s$/, '')] = '';
                    }
                })
            }
            _.each(calResult, obj => {
                _.each(obj, item => _inner(item));
            });
            _.each(uiData, (data, apiName) => {
                if(apiName === this.get('apiname')) {
                    _inner(data)
                } else {
                    _.each(data.u, item => _inner(item));
                }
            })
        },

        //获取加密字段
        _getCalculateEncryptFields(calFields) {
            let tmp = {};
            if(!calFields) {
                let arr1 = tmp[this.get('apiname')] = []
                _.each(this.get('fields'), field => {
                    field.mask_field_encrypt && arr1.push(field.api_name);
                })
                //从对象的
                _.each(this.get('detailObjectList'), a => {
                    let arr2 = [];
                    _.each(a.objectDescribe && a.objectDescribe.fields, (b, k) => {
                        b.mask_field_encrypt && arr2.push(k)
                    })
                    arr2.length && (tmp[a.objectApiName] = arr2);
                })
                //关联对象的
                _.each(this.get('relateObjectDescribes'), a => {
                    let arr2 = [];
                    _.each(a.objectDescribe && a.objectDescribe.fields, (b, k) => {
                        b.mask_field_encrypt && arr2.push(k)
                    })
                    arr2.length && (tmp[a.objectApiName] = arr2);
                })
            } else {
                _.each(calFields, (arr, objectApiName) => {
                    let fields = (this.getDescribeByObjApiName(objectApiName) || {}).fields;
                    if(fields) {
                        let narr = [];
                        _.each(arr, fieldName => {
                            let tt = fields[fieldName];
                            tt && tt.mask_field_encrypt && narr.push(fieldName);
                        })
                        narr.length && (tmp[objectApiName] = narr);
                    }
                })
            }

            return tmp;
        },

        newBatchUpdate: function(item, noTrigger, isUIEvent, isPlugin) {
            var me = this;
            var forms = me.get('forms');
            var obj = me._hackAreaUIData(me._differenceData(item, true));
            var fns = me._fns || {};
            var fields = me.get('fields');

            _.each(me._obj2arr(obj, fields), function(a) {
                var v = a.v;
                var k = a.k;
                if(_.isUndefined(v)) return;
                var comp = forms[k];
                var field = fields[k] || {};
                var type = field.type;
                var fnList = fns[k];
                if(type === 'object_reference' || type === 'master_detail') {
                    var data = {
                        id: v,
                        value: v ? item[k + '__r'] : '',
                        _id: v,
                        name: v ? item[k + '__r'] : '',
                        isUIEvent: isUIEvent
                    }
                    if(comp && comp.intercept) {
                        comp.intercept(data, function(flag) {
                            flag && comp.setValue(data, true);//????
                            comp._setData(data, null, true, isPlugin);
                        })
                    } else {
                        var tt = {
                            apiname: k,
                            value: v
                        }
                        me.setData(tt, noTrigger);
                        comp && comp.setValue(v, true);
                    }
                } else {
                    var tmp = {
                        apiname: k,
                        value: v,
                        isUIEvent: true
                    }
                    if(type === 'select_one' || type === 'select_many') {
                        me.setData({
                            apiname: k + '__o',
                            value: item[k + '__o'] || '',
                        }, true)
                    } else if(type === 'object_reference_many') {
                        me.setData({
                            apiname: k + '__r',
                            value: item[k] && item[k].length ? item[k + '__r'] : []
                        }, true)
                    } else if(_.contains(['employee', 'employee_many', 'department', 'department_many', 'dimension'], type)) {
                        me.setData({
                            apiname: k + '__l',
                            value: v && v.length ? item[k + '__l'] : []
                        }, true)
                    }

                    me.setData(tmp, noTrigger);

                    if(comp) {
                        type === 'country' ? comp.setCode(obj, true) : comp.setValue(v, true)
                    } else if(field.start_time_field) {//日期范围
                        comp = forms[field.start_time_field.api_name];
                        comp && comp.setValue();
                    }
                    _.each(fnList, function(fn) { //执行所有监听了k字段变化的事件。
                        fn(tmp);
                    })
                }
            })

            this._afterFillOutOwner(item);
        },

        //覆盖更新md
        overUpdate: function(objectData, details) {
            this.set({
                data: objectData,
                mdData: details
            }, {
                silent: true
            });
			this._fns = null;
			this.trigger('reloadcomps');
        },

        /**
        *修复特殊情况下引用字段不刷新问题，属于特例方法
        */
        updateQuoteValue: function(apiname) {
            this.triggerCalculate(apiname);
        },

        getLookupData: function(onlyValue) {
            var keys = [];
            var data = this.get('data');
            _.each(this.get('fields'), function(a) {
                var vv = data[a.api_name] || '';
                if(!vv && !onlyValue) return;
                var rt = a.render_type;
                if(rt === 'object_reference' || rt === 'master_detail') {
                    keys.push(a.api_name);
                    onlyValue || keys.push(a.api_name + '__r');
                }
            })

            return _.pick(this.get('data'), keys);
        },

        addEvent: function(events) {
            if (!events) return;
            var fns = this._fns || (this._fns = {
                all: []
            });
            _.each(events, function(v, k) {
                var kk = k.split('.');
                if (!kk[1]) {
                    fns.all.push(v);
                    return;
                }
                (fns[kk[1]] || (fns[kk[1]] = [])).push(v);
            })
        },

        //判断md是否有数据 已废弃
        hasMDDatas: function(apinames, next) {
            next({});
        },

        mdData2List: function() {
            var list = this.mdData2Object();
            var tmp = {};
            _.each(list, function(item, apiname) {
                tmp[apiname] = _.toArray(item);
            })

            return tmp;
        },

		/**
		 * @desc 开启cpq，M对象触发计算时，计算参数需要兼容treeTable
		 */
		mdData2Object: function() {
            if(this.assertUsePlugin()) return this.mdData2Object_normal();
			if(CRM._cache.cpqStatus && CRM.util.isSupportBom(this.get('apiname'))){
				return this.mdData2Object_tree();
			}
			return this.mdData2Object_normal();
		},

		mdData2Object_tree:function(){
			var allDatas = this.get('allTablesData');
			var list = {};
			function _fn(data,res){
				_.each(data, function(item) {
					if(!_.isObject(item)) return;
					res[item.rowId] = item;
					if(item.children && item.children.length){
						_fn(item.children,res)
					}
				})
			}

			_.each(allDatas, function(item, apiname) {
				var tarr = {};
				_.each(item, function(arr, rt) {
					_fn(arr,tarr)
				});
				list[apiname] = tarr;
			});
			return list;

		},

		mdData2Object_normal: function() {
            var list;
            try {
                var md = this.get('forms').md;
                list = md.td2ddm();
            } catch(e) {
                list = {};
                var allDatas = this.get('allTablesData');
                list = {};
                _.each(allDatas, function(item, apiname) {
                    var tarr = {};
                    var index = 0;
                    _.each(item, function(arr, rt) {
                        _.each(arr, function(item) {
                            tarr[index++] = item;
                        })
                    })
                    list[apiname] = tarr;
                })
            }

			return list;
        },

        fetchMDData: function(param) {
            var me = this;
            return new Promise(function(resove, reject) {
                var _from = me.get('_from');
                var mdData = me.get('mdData');
                var ca = param.associated_object_describe_api_name;
                if (!me.get('dataId') || _.contains(['clone', 'mapping', 'referenceCreate', 'transform'], _from)) {
                    resove({
                        Result: {
                            StatusCode: 0
                        },
                        Value: {
                            dataList: mdData[ca] || []
                        }
                    });
                } else {
                    let des = me.getDescribeByObjApiName(ca);
                    let mc = me.get('mdRequestConfig') || {};
                    let sqi = '{"limit":2000,"offset":0}';
                    let myParam = _.extend({
                        associate_object_data_id: me.get('dataId'),
                        associate_object_describe_api_name: me.get('apiname'),
                        include_associated: true,
                        search_query_info: sqi,
                        search_rich_text_extra: des && _.find(des.fields, a => a.type === 'big_text') ? true : void 0,//获取长文本的原始值
                        maskFieldApiNames: me._getCalculateEncryptFields()
                    }, param);
                    let url = mc.parseUrl ? mc.parseUrl(ca) : '/EM1HNCRM/API/v1/object/'+ ca +'/controller/RelatedList';
                    let data = mc.parseParam ? mc.parseParam(myParam) : myParam;

                    let sq = data.search_query_info === sqi && JSON.parse(sqi);
                    let dataList = [];
                    let queryCount = 0;
                    let _inner = () => {
                        queryCount++;
                        util.FHHApi({
                            url,
                            data,
                            success: function(res) {
                                if(res.Result.StatusCode === 0) {
                                    let rl = res.Value.dataList;
                                    if(dataList.length) {
                                        _.each(rl, item => _.findWhere(dataList, {_id: item._id}) || dataList.push(item));
                                    } else {
                                        dataList = rl;
                                    }
                                    
                                    if(sq && rl.length === sq.limit && queryCount < 3) {//有2000条数据，需要再继续请求 最多请求三次，也就是6000条数据
                                        sq.offset += sq.limit;
                                        data.search_query_info = JSON.stringify(sq);
                                        _inner();
                                    } else {
                                        me.cacheVersionOriginalDetail(res.Value.dataList = dataList, ca);
                                        resove(res);
                                    }
                                } else {
                                    util.alert(res.Result.FailureMessage);
                                    reject();
                                }
                            }
                        }, {
                            errorAlertModel: 1
                        })
                    }

                    _inner();
                }
            })
        },

        //获取从对象统计字段信息
        getMDSummaryInfo(objectApiName, recordtype) {
            let dob = _.findWhere(this.get('detailObjectList') || [], {objectApiName});
            let dl = dob && _.find(dob.layoutList, a => a.record_type === recordtype);

            let si = [], fieldMapping = this.get('mdFieldMapping');
            fieldMapping = fieldMapping && fieldMapping[objectApiName] && fieldMapping[objectApiName][recordtype];
            let fieldArias = fieldMapping && fieldMapping.fields;
            _.each(dl && dl.allPageSummaryInfo, a => {
                let field = dob.objectDescribe.fields[a.field_name];
                let aris = fieldArias && fieldArias[a.field_name];
                if(field) {
                    si.push({
                        ...field,
                        label: aris ? aris.label : field.label
                    })
                }
            })

            return si;
        },

        //获取列配置
        fetchColumnsConfig: function(apiname) {
            return util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/' + apiname + '/controller/ListHeader',//'/EM1HNCRM/API/v1/object/describe/service/findFieldListConfig',
                data: {
                    include_layout: false,
                    layout_type: "list"
                }
                // data: {
                //     objectDescribeApiName: apiname,
                //     extend_attribute: ""
                // }
            }, {
                errorAlertModel: 1
            })
        },

        //判断当前md数据是否有指定字段的值
        mdDatasHasFieldsValue: function(obj, next) {
            var flags = {};
            var list = this.mdData2List();
            _.each(obj, function(fields, apiname) {
                var tarr = list[apiname];
                var status = {};
                flags[apiname] = status;
                _.each(fields, function(fieldName) {
                    status[fieldName] = !!_.find(tarr, function(item) {
                        var vv = item[fieldName];
                        return !(vv === '' || vv === null || vv === void 0);
                    })
                })
            })

            next(flags);
        },

        //一期默认都有权限，后期会加入控制
        //业务方的特殊逻辑可以自己实现
        getMDHandleRight: function(handle, apiname, recordtype) {
			var copyobj = [
				'TPMActivityItemObj',
				'TPMActivityObj',
				'TPMActivityDetailObj',
				'TPMActivityStoreObj',
				'TPMActivityAgreementObj',
				'TPMActivityAgreementDetailObj',
				'TPMActivityProofObj',
				'TPMActivityProofDetailObj',
				'TPMActivityProofAuditObj',
				'TPMActivityProofAuditDetailObj',
				'TPMDealerActivityObj',
                'TPMStoreWriteOffObj',
				'TPMDealerActivityCostObj',
				'TPMActivityBudgetObj',
                'TPMBudgetAccountObj'
			];

			if(handle === 'copy') {
                return this.get('action_type') !== 'change_order' && (/__c$/.test(apiname) || _.indexOf(copyobj, apiname));//简单控制下只有自定义对象有复制按钮。
            }

			return true;
        },

        //判断数据是否是空
        dataIsNull: function(val) {
            return val === void 0 || val === '' || val === null || (_.isObject(val) && _.isEmpty(val));
        },

        //判断某个字段是否在布局里面
        assertFieldInLayouts: function(apiname, layout) {
            return !!_.find(layout || this.get('layout'), function(a) {
                return !!_.find(a.components, function(b) {
                    return b.api_name === apiname
                })
            })
        },

        //判断某个字段是否参与了布局规则
        assertFieldInRuleLayouts: function(apiname, rules) {
            var flag;
            rules || (rules = this.get('layout_rules'));
            rules && rules.length && _.find(rules, function(rule) {
                var mf = rule.main_field;
                if(mf === apiname) {
                    flag = true;
                } else {
                    _.find(rule.main_field_branches, function(branch) {
                        _.find(branch.branches, function(item) {
                            _.find(item.conditions, function(con) {
                                flag = apiname === con.field_name;
                                return flag;
                            })
                            return flag;
                        })
                        return flag;
					})
                }

				return flag;
            })

            return !!flag;
        },

        showMasterApprovalTip: function(data) {
            var md = data.masterData;
            var _alert = util.alert($t('crm.master_approval_tip', {
                name: '<span>【' + data.masterDisplayName + '】' + '<a href="javascript:;" class="to-master">' + md.name + '</a></span>'
            }))
            _alert.$('.to-master').click(function() {
                _alert.hide();
                CRM.api.show_crm_detail({
                    type: md.object_describe_api_name,
                    data: {
                        crmId: md._id
                    }
                });
            })
        },

        fetchDetail: require('../fetchdetail/fetchdetail'),
        cascadeChangeTips: require('./casecadetip'),
        //上传新表单使用情况
        __uploadNewformLog: function() {
            var dl = this.get('detailObjectList') || [];
            try {
                var type =  localStorage.getItem(this.get('apiname') + 'crm-action-field-viwetype') || 'large';
                util.uploadLog('field', 'newmd', {
                    eventId: 'crmnewform' + type + (type === 'mini' && dl.length ? 'master' : ''),
                    eventType: 'cl',
                    eventData: {
                        date: new Date().getTime()
                    }
                });
            } catch(e) {}
        },

        //获取md表格的宽度记忆
        fetchAllTDWidths: function() {
            return this.fetchTableWidth(_.map(this.get('detailObjectList'), a => a.objectDescribe.api_name)).then((widthConfig) => {
                this.set('allTDWidths', widthConfig);
                return Promise.resolve(widthConfig);
            })
        },

        //拉取表格宽度
        fetchTableWidth(apinames) {
            return new Promise(resolve => {
                if(!apinames.length) return resolve();
                util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/custom_scene/service/findFieldWidthConfig',
                    data: {
                        describeApiNames: apinames,
                        extendAttribute: null
                    },
                    success(res) {
                        let tmp = {};
                        if(res.Result.StatusCode === 0) {
                            _.each(res.Value.visibleFieldsWidth, function (arr, k) {
                                let tobj = {}
                                _.each(arr, function (item) {
                                    item.width && (tobj[item.field_name] = item.width);
                                })
                                tmp[k] = tobj;
                            })
                        }
						resolve(tmp);
                    },
                    error () {
                        resolve();
                    }
                }, {
                    errorAlertModel: 1
                })
            })
        },

        //一次性拉取所有md数据
        fetchAllMDDatas: function() {
            var me = this;
            return new Promise(function(resolve, reject) {
                var _from = me.get('_from');
                if (!me.get('dataId') || me.get('noFetchMDData') || _.contains(['clone', 'mapping', 'draft', 'referenceCreate', 'transform'], _from)) {
                    resolve(me.get('mdData'));
                    return;
                }
                var mdDatas = {};
                var dl = me.get('detailObjectList');
                var length = dl.length;
                if(!length) {
                    resolve();
                    return;
                }
                _.each(dl, function (item) {
                    var apiname = item.objectDescribe.api_name;
                    me.fetchMDData({
                        associated_object_describe_api_name: apiname,
                        associated_object_field_related_list_name: item.related_list_name,
                        is_ordered: true
                    }).then(function(res) {
                        length--;
                        if (res.Result.StatusCode === 0) {
                            me._filterDetailsDisabledValue(res.Value.dataList, apiname);
                            mdDatas[apiname] = res.Value.dataList;
                        }
                        length || (resolve(mdDatas));
                    })
                })
            })
        },

        //获取md的表格宽度配置。需fetchAllTDWidths执行成功
        getMDTableWidth: function(apiname) {
            var twidths = this.get('allTDWidths');
            return twidths ? twidths[apiname] || {} : {};
        },

        //更新md表格宽度
        upDataMDTdWidth: function(apiname, widths) {
            var tw = this.getMDTableWidth(apiname);
            tw && _.extend(tw, widths);
        },

        updateaMDDatas: function(apiname, rt,  datas) {
            var obj = this.get('allTablesData');
            obj[apiname][rt] = datas;
        },

        getMDTableDatas: function(apiname, rt) {
            var obj = this.get('allTablesData');
            return (obj && obj[apiname] && obj[apiname][rt]);
        },

        //判断md是否匹配
        mdIsNotMatch: function(rn, rt) {
            var tt = _.findWhere(this.get('detailObjectList'), {related_list_name: rn});
            if(tt) {
                tt = _.findWhere(tt.layoutList, {record_type: rt});
            }

            return tt && tt.not_match;
        },

        getDDM: function() {
            var ddm = {};
            _.each(this.get('allTablesData'), function(item, apiname) {
                var tarr = {};
                var index = 0;
                _.each(item, function(arr, rt) {
                    _.each(arr, function(item) {
                        tarr[index++] = item;
                    })
                })
                ddm[apiname] = tarr;
            })

			return ddm;
        },

        __validateDataRequire: function(data) {
            var me = this;
            var layout = this.get('__originLayout');
            if(!layout || !data) return true;
            var logs = [];
            var dr = [];
            var types = ['province', 'city', 'district'];
            var forms = me.get('forms') || {};
            var fields = me.get('fields');
            _.each(layout.components, function(a) {
                _.each(a.field_section, function(b) {
                    _.each(b.form_fields, function(c) {
                        var cf = c.field_name;
                        if(cf === 'owner' || _.contains(types, c.render_type)) return;
                        if((fields[cf] || {}).mask_field_encrypt) return;

                        if(c.is_required && me.dataIsNull(data[cf])) {
                            var hasComp = forms[cf];
                            logs.push(cf + '[' + c.is_readonly + '][' + !!hasComp + ']');
                            !c.is_readonly && hasComp && /__c$/.test(cf) && dr.push(cf);
                        }
                    })
                })
            })

            if(logs.length) {
                util.uploadLog('PaaSobj', 'Form', {operationId: 'datarequire', eventData: {fields: logs.join(';'), date: new Date().toLocaleString(), apiname: me.get('apiname')}});
            }

            if(!dr.length) return true;

            var fields = me.get('fields');
            var label = fields[dr[0]].label;

            util.alert($t("请填写{{label}}", {label: label}));

            return false;
        },

        //异步获取乡镇选项
        asyncGetTownOptions: function(code) {
            function _parse(options) {
                return _.map(options, function(a) {
                    return {
                        name: a.townName,
                        value: a.townId
                    }
                })
            }
            return new Promise(function(resolve, rejuect) {
                if(!code) {
                    return resolve();
                }
                if(TOWN_DATA[code]) {
                    return resolve(_parse(TOWN_DATA[code]));
                }
                util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/global_data/service/get_town_options',
                    data: {
						type: 'district',
                        code: code
                    },
                    success: function(res) {
                        if(res.Result.StatusCode === 0) {
                            resolve(_parse(TOWN_DATA[code] = res.Value.town));
                        } else {
                            resolve();
                        }
                    }
                })
            })
        },

        getTownData: function(code) {
            return TOWN_DATA[code];
        },

        getFieldAttr: function(fieldName) {
            return (this.get('fields') || {})[fieldName] || {};
        },

        //判断字段是否参与了ui事件
        assertFiledInEvents: function(fieldName) {
            var obj = (this.get('uiEvents') || {})[this.get('apiname')];
            return obj && obj[fieldName];
        },

        getALLMDLookupKey: function() {
            if(!this.__mdLookupFieldConfig) {
                var dl = this.get('detailObjectList');
                var fieldConfig = {};
                _.each(dl, function(a) {
                    var keys = [];
                    _.each(a.objectDescribe.fields, function(b) {
                        (b.type === 'object_reference' || b.type === 'master_detail') && keys.push(b.api_name);
                    })
                    fieldConfig[a.objectDescribe.api_name] = keys;
                })
                this.__mdLookupFieldConfig = fieldConfig;
            }

            return this.__mdLookupFieldConfig;
        },

        //获取所有从对象的lookup值
        getDetailsLookup: function(apiname, objectData) {
            var config = this.getALLMDLookupKey();
            var details = {};
            _.each(this.get('allTablesData'), function(item, apiname) {
                var keys = config[apiname];
                var tmp = [];
                if(keys && keys.length) {
                    _.each(item, function(arr) {
                        _.each(arr, function(a) {
                            tmp.push(_.pick(a, keys));
                        })
                    })
                }
                details[apiname] = tmp;
            })

            var tt = details[apiname];
            var attrs = _.pick(objectData, config[apiname]);
            var aa = _.find(tt, function(a) {
                var at = _.find(a, function(v, k) {
                    return attrs[k] != v
                })

				return at === void 0;
            })

            if(aa) {
                tt.splice( _.indexOf(tt, aa), 1);
            }

            _.each(config, function(a, k) {
                details[k] || (details[k] = [])
            })

			tt.unshift(objectData);

            return details;
        },

		//760在支持主从混排，从对象重构依赖的方法
		//======================================start==================================================//
		//获取从对象数据 return {ObjectApiname: [{....}], .....}
		getDetails: function () {
			var details = {};
			_.each(this.get('allTablesData'), function (a, apiName) {
				var list = [];
				_.each(a, function (arr, rt) {
					[].push.apply(list, arr);
				})
				details[apiName] = list;
			})
			return details;
		},

		//复制一条从数据
		copyRowData: function (data) {
            let extKeys = ['_id', 'version', '__rules', 'create_time', 'created_by', 'is_deleted', 'last_modified_by', 'last_modified_time', 'out_owner', 'order_by', 'out_tenant_id', 'package', '_fromUIEvent', 'extend_obj_data_id'];
            let item = _.omit(JSON.parse(JSON.stringify(data)), extKeys);

            let des = this.getDescribeByObjApiName(data.object_describe_api_name);
            if(des) {//把描述上不可复制的字段也排除
                let dData = (this.get('_mdDefaultData') || {})[data.object_describe_api_name] || {};
                _.each(des.fields, a => {
                    let fieldName = a.api_name;
                    if(item[fieldName] === void 0) return;
                    if(a.enable_clone !== false) return;
                    if(!this.dataIsNull(a.default_value) || (a.calculate_relation && a.calculate_relation.relate_fields)) return;

                    delete item[fieldName];
                    delete item[fieldName + '__r'];
                    delete item[fieldName + '__l'];
                    delete item[fieldName + '__encrypt'];
                    delete item[fieldName + '__s'];
                    if(dData[fieldName] !== void 0) {
                        item[fieldName] = dData[fieldName];
                        _.each(['__r', '__l', '__encrypt', '__s'], b => {
                            if(dData[fieldName + b] !== void 0) {
                                item[fieldName + b] = dData[fieldName + b];
                            }
                        })
                    }
                });
            }

			item.rowId = this.getRowUniqueId();

			return item;
		},

		// 获取一条从数据必备的信息
		getRowBasicData: function (apiname, rt) {
			var mdd = this.get('_mdDefaultData');
			return _.extend({
				record_type: rt,
				object_describe_api_name: apiname,
				rowId:  this.getRowUniqueId()
			}, mdd && mdd[apiname]);
		},

		// 获取一条从数据的唯一id
		getRowUniqueId: function () {
            return util.uniqueCode();
			// if (!this.__rowUUID) {
			// 	this.__rowUUID = 0;
			// }
			// return 'row_id_' + this.__rowUUID++ + _.shuffle(['a', 'b', 'c', 'd', 'e', 'f', 'g', 'j', 'l', 'i']).join('');
		},

		//更新从对象数据
		updateMDData: function (item, apiName, recordType) {
			var allTablesData = this.get('allTablesData') || {};
			var tt = allTablesData[apiName];
			if (!tt) {
				tt = allTablesData[apiName] = {};
			}
			var tmp = tt[recordType] || (tt[recordType] = []);
			var oldlength = tmp.length;

            if(item.del && item.del.length) {
				tmp = _.filter(tmp, function (a) {
					return !_.contains(item.del, a.rowId);
				})
			}

			item.add && Array.prototype.push.apply(tmp, item.add);

			item.insert && _.each(item.insert, function (a) {
				var index;
				_.find(tmp, function (b, i) {
					if (b.rowId === a.insertRow.rowId) {
						index = i + (a.isBefore ? 0 : 1);
						return true;
					}
				})
				index !== void 0 ? tmp.splice(index, 0, ...a.datas) : Array.prototype.push.apply(tmp, a.datas);
			})

            _.each(item.update, function (obj, rowId) {
				var cc = _.findWhere(tmp, {rowId: rowId});
				_.extend(cc, obj);
			})

			tt[recordType] = tmp;

			oldlength !== tmp.length && this.trigger('action:' + apiName + '_' + recordType, 'numChangeHandle', tmp.length);
		},

        newUpdateMDData(datas, objApiName, recordType) {
            let allTablesDatas = this.get('allTablesData') || {};
            let tt = allTablesDatas[objApiName];
            if(!tt) {
                tt = allTablesDatas[objApiName] = {};
            }

            let arr = tt[recordType];
            if(!arr) {
                arr = tt[recordType] = [];
            }

            arr.splice(0, arr.length, ...datas);

            this.trigger('action:' + objApiName + '_' + recordType, 'numChangeHandle', arr.length);
        },

        //重排顺序，和表格统一
		sortMDByDatas: function(datas) {
            let item = datas[0];
			let allTablesData = this.get('allTablesData') || {};
			let tt = (allTablesData[item.object_describe_api_name] || {})[item.record_type];
			if (tt && tt.length === datas.length && _.union(tt, datas).length === tt.length) {
                tt.splice(0, tt.length, ...datas);
			}
		},

		//根据配置初始化从对象数据
		initMDData: function (datas = {}, detailObjects) {
            var tmp = {};
            _.each(detailObjects, (recordTyps, objectApiName) => {
                let tt = {};
                _.each(recordTyps, recordType => {
                    tt[recordType] = _.filter(datas[objectApiName], data => {
                        if(data.record_type === recordType) {
                            data.object_describe_api_name = objectApiName;
                            if(!data.rowId) {
                                data.rowId = this.getRowUniqueId();
                            }
                            return true;
                        }
                    })
                })
                tmp[objectApiName] = tt;
            })

            this.set('allTablesData', tmp);

            return tmp;
		},

		// 净化server返回的查找关联数据，保证页面能清空
		_puerObjectReferenceData: function (data, fields) {
			_.each(data, function (v, k) {
				if (!v && data[k + '__r']) { //lookup没值要把显示的内容清掉
					data[k + '__r'] = '';
				}
			})

			return data;
		},

        hideNotMatchMD: function() {
            var me = this;
            setTimeout(function() {
                var allTablesData = me.get('allTablesData') || {};
                var attr = {};
                _.each(me.get('detailObjectList'), function(a) {
                    var tmp = allTablesData[a.objectDescribe.api_name] || {}
                    _.find(a.layoutList, function(b) {
                        return  !b.not_match || (tmp[b.record_type] && tmp[b.record_type].length);
                    }) || (attr[a.objectDescribe.api_name] = {hidden: true});
                })
                _.isEmpty(attr) || me._toggleMDStatus(attr);
            }, 100)
		},

		// 从对象计算任务成功之后
		caltaskSuccessHandle: function (res) {
			var me = this;
            res.optionAttribute && this._toggleFieldOptions(res.optionAttribute);

			res.masterUpdate && me.newBatchUpdate(me._puerObjectReferenceData(res.masterUpdate, me.get('fields')), true);//更新主对象

            this._toggleFieldRemind(res.remind, '', true);
			res.fieldAttribute && this._setFieldAttribute(res.fieldAttribute);
			res.objectAttribute && this._toggleMDStatus(res.objectAttribute);

			//按照业务类型再细分出 add update del insert
			var t = {};
			var _inner = function (item) {
				var apiName = item.object_describe_api_name;
				var rt = item.record_type;
				var t1 = t[apiName] || (t[apiName] = {});
				var t2 = t1[rt] || (t1[rt] = {});
				return t2;
			}
            
            if(res.mdMove && res.mdMove.length) {
                let _moveIds = [];
                _.each(res.mdMove, a => {
                    _.each(a.datas, b => _moveIds.push(b.rowId));
                })
                _.each(res.mdInsert, a => {
                    a.datas = _.filter(a.datas, b => !_.contains(_moveIds, b.rowId));
                })

                _.each(res.mdMove, function(item) {
                    //先删除
                    (res.mdDel || (res.mdDel = [])).push(...item.datas);
                    let _datas = [...item.datas];
                    _.each(item.datas, a => {
                        res.mdInsert = _.filter(res.mdInsert, b => {
                            if(b.insertRow.rowId === a.rowId) {
                                _.find(_datas, (c, index) => {
                                    if(c.rowId === a.rowId) {
                                        _datas.splice(b.isBefore ? index : index + 1, 0, ...b.datas);
                                        return true;
                                    }
                                })
                                return;
                            }

                            return true;
                        })
                    })

                    item.datas = _datas;
    
                    //再插入
                    (res.mdInsert || (res.mdInsert = [])).push(item);
                })

                var filterMdInsert = function() {
                    let flag = _.find(res.mdInsert, (a, index) => {
                        let item = _.find(res.mdInsert, b => {
                            return _.findWhere(b.datas, {rowId: a.insertRow.rowId})
                        })
                        if(item) {//被插入的行，本身就是插入的数据，则把它合并到相关位置
                            _.find(item.datas, (c, index) => {
                                if(c.rowId === a.insertRow.rowId) {
                                    item.datas.splice(a.isBefore ? index : index + 1, 0, ...a.datas);
                                    return true;
                                }
                            })
    
                            res.mdInsert.splice(index, 1)//合并之后移除
    
                            return true;
                        }
                    })
    
                    if(flag) {//继续过滤合并
                        filterMdInsert()
                    }
                }
    
                filterMdInsert();

                if(res.mdInsert && res.mdInsert.length) {
                    res.mdAdd = _.filter(res.mdAdd, item => {
                        !_.find(res.mdInsert, a => _.findWhere(a.datas, {rowId: item.rowId}));
                    })
                }
            }
            
			_.each(res.mdUpdate, function (item, rowId) {
				var t2 = _inner(item);
				(t2.update || (t2.update = {}))[item.rowId || rowId] = me._puerObjectReferenceData(item);
			})
			_.each(res.mdAdd, function (item) {
				item.rowId || (item.rowId = me.getRowUniqueId()) //通过ui事件增加的数据没有rowId
				var t2 = _inner(item);
				(t2.add || (t2.add = [])).push(me._puerObjectReferenceData(item));
			})
			_.each(res.mdDel, function (item) {
				var t2 = _inner(item);
				(t2.del || (t2.del = [])).push(item.rowId);
			})
			_.each(res.mdInsert, function (item) {
				var t2 = _inner(item.insertRow);
				_.each(item.datas, function (a) {
					me._puerObjectReferenceData(a);
				});
				(t2.insert || (t2.insert = [])).push(item);
			})
            _.each(res.mdSort, (item, objApiName) => {
                let t2 = t[objApiName] || (t[objApiName] = {});
                _.each(item, (rowIds, rt) => {
                    (t2[rt] || (t2[rt] = {})).mdSort = rowIds;
                })
            })

            function mergeInsertData(data) {
                if(CRM.util.getUserAttribute('crmGrayMergeInsert')) {
                    data.insert = _.filter(data.insert, item => {
                        return !_.find(data.add, (aitem, index) => {
                            if(aitem.rowId === item.insertRow.rowId) {
                                data.add.splice(item.isBefore ? index : index + 1, 0, ...item.datas);
                                return true;
                            }
                        })
                    })
                }

                return data;
            }

			_.each(t, function (item, apiName) {
				_.each(item, function (data, rt) {
                    //me.updateMDData(data, apiName, rt);
					me.triggerMDActionEvent(apiName, rt, 'mdupdateHandle', mergeInsertData(data));
					data.del && data.del.length && me.triggerMDActionEvent(apiName, rt, 'cancelSelectedHandle');
                    me.set('formDataChange', true);
				})
			})

            this._updateMDByUIEventResult(res);

            if(res.relatdDatas) {//更新关联数据
                let relateAdd = this.get('forms')['relateadd'];
                relateAdd && relateAdd.update(res.relatdDatas);
            }
		},

		//兼容主对象ui事件更新从对象数据的情景 todo 以后统一去掉
        //因为老md更新是根据index来的，并不是数据的rowId
        //{object_c1YBm__c: {a: [{...}]}, u: {rowid: {}}}
        //{object_c1YBm__c: {a: [{...}]}, u: {index: {}}}
		hackUpdateByUIEventData(data, _indexToRowId) {
			let me = this, mdUpdate = [], mdAdd = [], mdDel = [];

            //得到一个index对应的从对象数据
            //{object_c1YBm__c：{0: {}, 1: {}}}
            let ddm = me.mdData2Object();

            _.each(data, (obj, apiname) => {
                let td = ddm[apiname];
                let _u = obj.u || {};
                _indexToRowId && _.each(obj.u, (a, index) => {
                    _u[_indexToRowId[apiname + index]] = a;
                })
                _.each(td, (item, index) => {
                    let _uitem = (item.rowId && _u[item.rowId]) || _u[index];
                    if(_uitem) {
                        !_.isEmpty(_uitem) && item.rowId && mdUpdate.push(_.extend(_.pick(item, ['record_type', 'object_describe_api_name', 'rowId']), _uitem));
                    } else {
                        item.rowId && mdDel.push(item);
                    }
                })
                _.each(obj.a, item => {
                    if(item.__add_mark_rowid__) {
                        item.rowId = item.__add_mark_rowid__;
                        delete item.__add_mark_rowid__;
                    } else {
                        item.rowId = me.getRowUniqueId();
                    }
					item.object_describe_api_name = item.object_describe_api_name || apiname;
					item.record_type && mdAdd.push(item);
				})
            })

			me.caltaskSuccessHandle({mdUpdate, mdAdd, mdDel});
		},

		//兼容主对象计算了从对象相关数据的情景 todo以后加入计算任务统一去掉
		hackMdDataChange: function (res, _indexToRowId) {
			var ddm = this.mdData2Object();
			var mdUpdate = [];
			_.each(res, function (obj, apiname) {
				var td = ddm[apiname];
				td && _.each(obj, function (a, k) {
                    let rowId = _indexToRowId && _indexToRowId[apiname + k];
                    let tt = rowId && _.findWhere(td, {rowId});
                    if(!tt) {
                        tt = td[k]
                    }
					if (tt && tt.rowId) {
						mdUpdate.push(_.extend(_.pick(tt, ['record_type', 'object_describe_api_name', 'rowId']), a));
					}
				})
			});

			this.caltaskSuccessHandle({mdUpdate: mdUpdate});
		},

		// 获取从对象单个实例派发事件时的唯一key
		getMDActionEventKey: function (apiname, rt) {
			return 'action:' + apiname + '_' + rt;
		},

		// 单个从对象触发事件
		triggerMDActionEvent: function (apiname, rt, action, data, arg1) {
			this.trigger(this.getMDActionEventKey(apiname, rt), action, data, arg1);
		},

		// 获取所有从对象的apiname
		getMDApiNames: function () {
			return _.map(this.get('detailObjectList'), function (a) {
				return a.objectDescribe.api_name;
			})
		},

		//获取左侧导航数据
		getNavData: function () {
			var list = [];
			var mdGroupId;
			var me = this;
			_.each(me.get('layout'), function (a) {
				if (a.type === 'md' || a.type === 'newmd' || a.type === 'md20') {
					mdGroupId = a.groupId;
				}
				if (a.type === 'mdmix' || a.type === 'relatemix') {
					var apiNames = a.mdApiNames || []
					_.each(a.tabs, function (b, index) {
						list.push({
							groupId: a.groupId,
							label: b.header,
							type: 'md',
							mdApiName: apiNames[index]
						})
					})
				} else if(a.type === 'form_table') {
                    a.header && a.show_header && list.push({
						label: a.header,
						groupId: a.groupId
					})
                } else {
					a.label && !a.noHeader && list.push({
						label: a.label,
						groupId: a.groupId
					})
				}
			})

			if (mdGroupId && !me.get('isFormLayout')) { //没有走列表页布局的
				_.each(me.get('detailObjectList'), function (b) {
					list.push({
						groupId: mdGroupId,
						label: b.related_list_label,
						type: 'md',
						mdApiName: b.objectDescribe.api_name
					})
				})
			}

			if (list.length < 2) return;

			return list;

			// var dl = this.get('detailObjectList');
			// _.each(this.get('layout'), function(a, index) {
			//     if(a.type === 'md' || a.type === 'newmd' || a.type === 'md20') {
			//         _.each(dl, function(b) {
			//             list.push({
			//                 gindex: index,
			//                 label: b.related_list_label,
			//                 type: 'md'
			//             })
			//         })
			//     } else if(a.label) {
			//         list.push({
			//             gindex: index,
			//             label: a.label,
			//             type: 'common'
			//         })
			//     }
			// })

			// if(list.length < 2) return;
			// list[0].selected = true;

			// return list;
		},

		parseMDLayout: function (layout) {
			if (layout.components.length > 1) {
				var components = _.sortBy(layout.components, function (a) {
					return a.order;
				})

				var field_section = [];
				_.each(components, function (a) {
					Array.prototype.push.apply(field_section, a.field_section);
				})

				return _.extend({}, layout, {components: [_.extend({}, layout.components[0], {field_section: field_section})]})
			} else {
				return layout;
			}
		},
		//760在支持主从混排，从对象重构依赖的方法
		//======================================end==================================================//

        getTriggerOnLoadEventParam(){
            var uiEvents = this.get('uiEvents');
            var detailDataMap = this.mdData2Object();
            return {
                event: uiEvents.onload,
				detailDataMap: detailDataMap,
                checkResultCb: (res) => {
                    let flag;
                    if(!this.__triggerOnLoadNum) {
                        let ddm = this.mdData2Object();
                        _.find(detailDataMap, (item, objApiName) => {
                            flag = _.keys(item || {}).length !== _.keys(ddm[objApiName] || {}).length;
                            return flag;
                        })
                    }
                    
                    if(flag) {//从对象数据有变更，再执行一次
                        this.__triggerOnLoadEvent();
                        this.__triggerOnLoadNum = 1;
                        return false;
                    }

                    try {
                       _.each(res.Value && res.Value.data[this.get('apiname')], (v, k) => {
                           (this.__triggerOnloadUIDataResult || (this.__triggerOnloadUIDataResult = {}))[k] = 1;
                       })
                    } catch(e) {}
                },
                notifyFun: (a, b, res, isError) => {
                    this.runFormRenderAfterService();
                    this.trigger('form.data.update.complete');
                    this.set('__hisData', null);
                    if(isError) {//
                        CRM.util.uploadLog('form', 'field', {
                            eventId: 'onloadeventerror',
                            eventData: {
                                traceId: this.get('_fetchTraceId') || '',
                                apiName: this.get('apiname') || '',
                                msg: (res && res.Result && res.Result.FailureMessage) || $t('网络异常')
                            }
                        });
                    }
                }
            }
        },

        //上传表单渲染耗时埋点
        __sendCostLog() {
            let mdCost = this.get('_mdRenderCostTime') || 0;
            let sCost = this.get('_serverCostTime'), domCost = this.get('_mainFormCostTime') + mdCost;
            sCost && domCost && FS.log && FS.log('performance.obj.form', 'ct', {
                surl: this.get('_fetchDesUrl') || '',
                traceId: this.get('_fetchTraceId') || '',
                apiName: this.get('apiname') || '',
                sCost,
                domCost,
                uiCost: sCost + domCost
            });
        },

		triggerOnLoadEvent: function () {
            if(this.__onLoadExcuted) return;
            this.__sendCostLog();
            if(this.get('_from') === 'draft') {
                this.__onLoadExcuted = true;
                this.recoveryFormRule();//有从对象，等从对象渲染完触发
                this.runFormRenderAfterService();
                return;
            }
            let uiEvents = this.get('uiEvents');
            if(!uiEvents || !uiEvents.onload) {
                this.__onLoadExcuted = true;
                this.runFormRenderAfterService();
                return;
            }

            if(!this.__triggerOnLoadEvent) {
                this.__triggerOnLoadEvent = _.debounce(() => {
                    if(this._analysisAjax || this._dataChangePlugining || this._masterEndParams) {//等待其它计算任务完成，在执行
                        this.__triggerOnLoadEvent();
                        return;
                    };

                    this.__onLoadExcuted = true;
                    this._uiLoadEventAjax = false;
                    this.excuteUIEvent(this.getTriggerOnLoadEventParam());
                }, /__c/.test(this.get('apiname')) ? 500 : 1000)
            }
            this._uiLoadEventAjax = true;
            this.__triggerOnLoadEvent();
		},

        //fields {name: {.......}},对象所有字段的描述
        //layouts: [{field_name: 'name', render_type: 'text', is_required: true/false}]//当前表格需要显示的字段
        // rt 当前表格的业务类型，可不传
        formatMDColumns: function(fields, layouts, rt) {
            // var columns = [];
            // var roptions;
            // function _parseFieldOptions(field) {
            //     var options = [];
            //     _.each(field.options, function(a) {
            //         a.not_usable || options.push(_.extend({}, a, {
            //             ItemCode: a.value,
            //             ItemName: a.label,
            //             Children: field.type === 'multi_level_select_one' ? _.map(a.child_options, function(b) {
            //                 return {
            //                     ItemCode: b.value,
            //                     ItemName: b.label
            //                 }
            //             }) : a.child_options,
            //             isCustom: a.value === 'other',
            //             customRequired: a.is_required
            //         }))
            //     })

            //     if (roptions && field.cascade_parent_api_name === 'record_type') {
            //         var rp = _.findWhere(roptions, {
            //             api_name: rt
            //         })
            //         var map = {};
            //         rp && rp.child_options && _.each(rp.child_options[0], function(arr, k) {
            //             var temp = {};
            //             _.each(arr, function(v) {
            //                 temp[v] = 1
            //             })
            //             map[k] = temp;
            //         })
            //         var mr = map[field.api_name];
            //         options = !mr ? [] : _.filter(options, function(b) {
            //             return mr[b.ItemCode];
            //         })
            //     }

            //     return options;
            // }

            // if(rt && fields.render_type) {
            //     _.each(roptions = fields.record_type.options, function(a) {
            //         a && (a.value = a.api_name);
            //     })
            // }

            // _.each(layouts, function(item) {
            //     var fieldname = item.field_name;
            //     var field = fields[fieldname];
            //     if (!field) return;

            //     var rt = item.render_type || field.type;
            //     columns.push(_.extend({}, field, item, {
            //         id: fieldname,
            //         data: fieldname,
            //         dataType: rt,
            //         __isEdit: true,
            //         isEdit: !item.is_readonly,
            //         isRequired: !!item.is_required,
            //         showLookupText: true, // lookup 展示文本
            //         title: field.label,
            //         options: field.options && _parseFieldOptions(field, roptions),
            //         _options: field.options,
            //         returnType: field.quote_field_type || field.return_type,
            //         range: rt === 'percentile' ? [10, 6] : [field.length || 0, field.decimal_places || 0],
            //         referRule: {employee: 'Employee', employee_many: 'Employee'}[field.quote_field_type || rt],
            //         isId: true,
            //         type: field.quote_field_type || field.type,
            //         isDisable: field.is_active === false // 是否禁用
            //     }));
            // })

            // return columns;
        },

        openMDForm: function(opts) {
            return sss({
                apiname: opts.apiname,
                recordType: opts.recordType,
                model: this,
                type: opts.type,
                data: opts.data
            })
        },

        getRecordTypeLabel() {
            let label = '';
            let field = (this.get('fields') || {}).record_type;
            if(field && field.options) {
                let item = _.findWhere(field.options, {value: this.get('record_type')});
                if(item) {
                    label = item.label;
                }
            }

            return label;
        },

        //生成草稿箱标题
        createDraftTitle: function() {
            var me = this;
            var data = me.get('data');
            if(data.name) return data.name; //默认主属性
            var forms = me.get('forms');
            var title;
            var tts = ['mdmix', 'relatemix', 'md', 'md20'];
            try {
               _.find(me.get('layout'), function(item) {
                    if(_.contains(tts, item.type)) return;
                    _.find(item.components, function(comp) {
                        var instance = forms[comp.api_name];
                        if(!instance || instance._elIsHide) return;//不存在或被隐藏掉的
                        if(_.contains(['use_range', 'use_scope', 'file_attachment', 'big_file_attachment', 'image', 'signature', 'name'], comp.quote_field_type || comp.return_type || comp.type)) return;
                        var vv = data[comp.api_name];

                        if(instance.getFullName && _.isFunction(instance.getFullName)) {
                            title = instance.getFullName();
                        } else if(!me.dataIsNull(vv)) {
                            if(comp.type === 'quote') {
                                if(comp.quote_field_type === 'location') {
                                    title = vv.split('#%$')[2]
                                } else if(comp.quote_field_type === 'select_many') {
                                    title = vv.replace(/\|/, ',');
                                } else {
                                    title = vv;
                                }
                            } else {
                                title = _.unescape(format({...comp, disableRegionFormat: true}, data[comp.api_name], data, {noColor: true}));
                                if(title === '[object Object]' && _.isObject(data[comp.api_name])) {
                                    title = data[comp.api_name].text;
                                }
                            }
                        }
                        if(title || title === 0) {
                            title = comp.label + ':' + title;
                        }
                        return !!title
                    })
                    return !!title
                })
            } catch(e) {}

            return title;
        },

        //执行自定义按钮的ui事件
        excuteCustomBtnUIEvent: function(eventId, uiEventButton) {
            let me = this;
            eventId && me.excuteUIEvent({
                event: {
                    _id: eventId,
                },
                detailDataMap: me.mdData2Object(),
                endCb(a, b, uiEventResult) {
                    const ps = me.getPluginService({
                        objApiName: me.get('apiname'),
                        recordType: me.get('record_type'),
                        uiEventButton,
                        uiEventResult
                    });
                    ps.run('form.uievent.after').then(rst => ps.end(rst && rst.StatusCode));
                }
            })
        },

        isMiniView: function() {
            var vt = this.get('viewType');
            return vt === 'mid' || vt === 'mini';
        },

        //////////////////////////////////////////785新增外部负责人相关逻辑 start//////////////////////////////////////////
        //新建时 预先处理外部负责人自动填充逻辑
        _preFillOutOwner: function(obj) {
            var me = this;
            return {
                then: function(resolve) {
                    var data = obj.data;
                    //测试数据
                    // data.field_7be38__c = '5df217612ecc400001b99367'; 合作伙伴
                    // data.field_7be38__c__r = '71698';

                    // data.field_p9dFR__c = '5f0d7e8eb8777d000131d148';
                    // data.field_p9dFR__c__r = '自定义对象71698'

                    if(me.get('isEdit') || me.get('isCopy') || !data) return resolve();
                    if(data.out_owner !== void 0) return resolve();

                    var tmp = me.findRelationOutOwnerField(obj.fields, data);
                    if(tmp) {
                        me.fetchObjectOutOwner(data[tmp.api_name], tmp.target_api_name).then(function(outOwner) {
                            outOwner && _.extend(data, outOwner);
                            resolve();
                        })
                    } else {
                        resolve();
                    }
                }
            }
        },

        //计算或ui事件成功后，如果回填了合作伙伴或客户，且没有一起填充外部负责人时
        _afterFillOutOwner: function(data) {
            if(!data || CRM.util.isConnectApp()) return;
            var tmp = this.findRelationOutOwnerField(this.get('fields'), data);
            if(!tmp) return;//没有找到合作伙伴或客户的变更
            if(data.out_owner !== void 0) return;//如果返回的数据有外部负责人
            var ft = (this.get('allLayouts') || {}).out_owner;
            if((this.get('data').out_owner || []).length && ft && !ft.is_readonly) return;//如果有数据，外部负责人在布局里，并且不是只读的，那就以当前数据为准

            //获取最新的外部负责人
            this.fetchObjectOutOwner(data[tmp.api_name], tmp.target_api_name).then(outOwner => {
                outOwner && this.newBatchUpdate(outOwner);
            })
        },

        _preFillMdAreaLabel: function(attr) {
            const mdd = this.get('_mdDefaultData');
            const thenable = {then(callback) {callback()}};

            if (!mdd) {
                return thenable;
            }

            const fillCodes = [];
            const fillFields = {};
            _.each(mdd, (v, k) => {
                const obj = _.findWhere(attr.detailObjectList, {objectApiName: k});
                const fields = obj && obj.objectDescribe && obj.objectDescribe.fields || {};
                _.each(v, (fv, fa) => {
                    const field = fields[fa];
                    if (fv && field && _.contains(['country', 'province', 'city', 'district', 'town', 'village'], field.type)) {
                        fillCodes.push(fv);
                        (fillFields[k] || (fillFields[k] = [])).push(field);
                    }
                });
            });

            if (fillCodes.length <= 0) {
                return thenable;
            }

            return this._bacthQueryLabels(_.uniq(fillCodes)).then((dataMap) => {
                if (!dataMap) return;
                _.each(fillFields, (v, k) => {
                    const md = mdd[k];
                    if (md) {
                        _.each(v, (f) => {
                            const fa = f.api_name;
                            const code = md[fa];
                            if (!md[fa + '__r'] && dataMap[code]) {
                                md[fa + '__r'] = dataMap[code];
                            }
                        });
                    }
                });
            });
        },

        _bacthQueryLabels(codes) {
            return new Promise(resolve => {
                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/global_data/service/batch_query_labels',
                    data: {codes},
                    success(res) {
                        const dataList = res.Value && res.Value.zoneInfos;
                        const dataMap = (dataList || []).reduce((dataMap, item) => {
                            dataMap[item.value] = item.label;
                            return dataMap;
                        }, {});
                        resolve(dataMap);
                    },
                    error() {
                        resolve()
                    }
                }, {
                    errorAlertModel: 1
                })
            })
        },

        findRelationOutOwnerField: function(fields, data) {
            var p1, c1;
            _.find(fields, function(a) {
                if(a.relation_outer_data_privilege === 'outer_owner' && data[a.api_name]) {
                    a.target_api_name === 'PartnerObj' ? p1 = a : c1 = a; //合作伙伴优先
                }
                return p1;
            })
            return p1 || c1;
        },

        //获取关联对象的外部负责人
        fetchObjectOutOwner: function(dataId, targetApiName) {
            let me = this;
            let requestId = this.requestId;
            return {
                then: function(resolve) {
                    let $wait = me.commonWaiting();
                    me._fetchObjectOutOwnering = true;
                    util.FHHApi({
                        url: '/EM1HNCRM/API/v1/object/enterpriseRelation/service/getRelationDownstreamInfo',
                        data: {
                            describeApiName: targetApiName,
                            dataId: dataId,
                            seriesId: requestId
                        },
                        success: (res) => {
                            $wait.remove();
                            me._fetchObjectOutOwnering = false; 
                            var rv = res.Value;
                            if(res.Result.StatusCode === 0 && rv.userId) {
                                var userId = rv.userId + '';
                                resolve({
                                    out_owner: [userId],
                                    out_owner__l: [{
                                        id: userId,
                                        name: rv.userName,
                                        outerTenantId: rv.outerTenantId
                                    }]
                                })
                            } else {
                                resolve();
                            }
                        },
                        error() {
                            $wait.remove();
                            me._fetchObjectOutOwnering = false;
                        }
                    }, {
                        errorAlertModel: 1
                    })
                }
            }
        },

        //对比数据是否变化
        diffData(nData, oData) {
            let flag;
            _.find(nData, (v, k) => {
                if(/__r$/.test(k)) return;
                let vv = oData[k];
                let vnull = this.dataIsNull(v);
                let vvnull = this.dataIsNull(vv);
                if(!(vnull && vvnull)) {
                    if(vnull === vvnull) {//两个都有值
                        if(_.isArray(v)) { //数组对比
                            let tmp = v[0];
                            if(tmp && tmp.path) { //图片附件签名
                                v = _.pluck(v, 'path');
                                vv = _.pluck(vv, 'path');
                            }
                            flag = v.length !== vv.length || (_.union(v, vv).length !== v.length);
                        } else {
                            flag = v != vv;
                        }
                    } else { //或者其中一个有值
                        flag = true
                    }
                }
                return flag;
            })

            return flag;
        },

        getTitleName() {
            let ol = this.get('__originLayout');
            let titleType = ol && ol.layout_structure && ol.layout_structure.title_name;
            if(!titleType) return;

            if(titleType === 'object') return this.get('display_name');

            let attr = this.getFieldAttr('record_type');
            if(!attr) return;

            let option = _.findWhere(attr.options, {value: this.get('record_type')});
            return option && option.label;
        },

        //校验数据是否通过校验
        asyncvalidateFormData(data) {
            let $wait = this.commonWaiting();
            return new Promise(resolve => {
                let forms = this.get('forms');
                let comps = [];
                _.each(forms, comp => {
                    if(comp.asyncValidateData && _.isFunction(comp.asyncValidateData)) {
                        comps.push(comp);
                    }
                })

                function inner() {
                    let comp = comps.pop();
                    if(!comp) {
                        $wait.remove();
                        return resolve();
                    };

                    comp.asyncValidateData().then(noPass => {
                        if(noPass) {
                            $wait.remove();
                            resolve(true);
                        } else {
                            inner();
                        }
                    })
                }

                inner();
            })
        },

        //检查用户是否拥有AI权限
        checkAiUserLicense() {
            return new Promise(resolve => {
                this._checkAiUserLicense(resolve);
            })
        },
        _checkAiUserLicense(success) {
            let aiUserLicense = this.get('aiUserLicense');
            if(aiUserLicense !== void 0) {
                success && success(aiUserLicense);
                return;
            }

            let cbs = this._checkAiUserLicenseSuccessCallbacks;
            if(cbs) {
                cbs.push(success);
            } else {
                cbs = this._checkAiUserLicenseSuccessCallbacks = [success];
                let innerSuccess = (hasAiUserLicense) => {
                    _.each(cbs, fn => fn(hasAiUserLicense));
                    this._checkAiUserLicenseSuccessCallbacks = null;
                }
                util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/objectAi/service/checkAiUserLicense',
                    success: (res) => {
                        if(res.Result.StatusCode === 0) {//请求成功，缓存下来
                            this.set('aiUserLicense', res.Value.hasAiUserLicense);
                        }
                        innerSuccess(res.Value && res.Value.hasAiUserLicense);
                    },
                    error: () => {
                        innerSuccess();
                    }
                }, {
                    errorAlertModel: 1
                })
            }
        },

        destroy: function() {
            let pluginService = this.get('pluginService');
            try {
                pluginService && pluginService.run('form.model.destroy.before', {
                    apiName: this.get('apiname')
                }).then(() => {
                    // pluginService.destroy && pluginService.destroy();
                    CRM.api.pluginService.destroyById(pluginService.appId);
                    // let tmp = {};
                    // _.each(pluginService, (v, k) => {
                    //     tmp[k] = null;
                    // })
                    // _.extend(pluginService, tmp);
                });
                this.set('pluginService', null);
            } catch(e) {}
        }
        //////////////////////////////////////////785新增外部负责人相关逻辑 end//////////////////////////////////////////
    }, require('./parse'), require('./calculate'), require('./hack'), require('./pluginservice'), require('./version'), require('./mdrule')));

    return Model;
});
