define(function(require, exports, module) {

    var Model = require('../model/model');
    var NetModel = require('../model/netmodel');
    var components = require('../components/components');
    var util = FS.crmUtil;

    var newLoadTemplate = require('./newload-html');
    var newTemplate = require('./newtpl-html');
    var newnav = require('./newnav');

    var View = Backbone.View.extend({
        ruleTipTemplate: require('./ruletip-html'),

        initialize: function() {
            this.super = View.prototype;
            if(this.options && this.options.data && this.options.data.optionsCommonExtend) {
                _.extend(this.options, this.options.data.optionsCommonExtend);
                delete this.options.data.optionsCommonExtend;
            }
            if(this.options && this.options.data && this.options.data.fieldsExtendConfig) {
                this.options.fieldsExtendConfig = this.options.data.fieldsExtendConfig;
                delete this.options.data.fieldsExtendConfig;
            }
            this.options = _.extend({}, this.super.options, this.options);
            this.events = _.extend({}, this.super.events, this.events);
            this.forms = {};
            this.options.data && (this.options.data = $.extend(true, {}, this.options.data));
            var CurModel = this.options.Model || (this.options.ownerType ? NetModel : Model);
            this.model || (this.model = new CurModel(_.omit(this.options, ['el', 'className', 'mycomponents', 'Model'])));
            this._addEvent();

            if(this.options.isWM) {
                this.loadTemplate = newLoadTemplate;
                this.template = newTemplate;
                this.renderNav = newnav;
                this._findClosest = function(comp) {
                    return comp.$el.closest('.j-item-wrap');
                };
                this._getScrollEl = _.bind(function() {
                    if(!this._scrollEl) {
                        this._scrollEl = this.options.showScroll ? this.$('.crm-action-nfield') : this.$el.closest('.crm-scroll');
                    }
                    return this._scrollEl.length && this._scrollEl;
                }, this);
            }
        },

        mycomponents: {},

        events: {
            'click': '_triggerClickEvent'
        },

        _addEvent: function() {
            this.listenTo(this.model, {
                'change:layout': this.initView,
                'ready:layout': this.initView,
                'form.render.complete': this.reallyRenderComplete,
                'scrolltofield': this.scrollToField,
                'lock.fieldedit': this._lockFieldEdit,
                'unlock.fieldedit': this._unlockFieldEdit,
                emptylayout: this.emptyLayoutHandle,
                rule: this.setCompStatusByRule,
                resizedialog: this.resizeHandle,
                error: this.errorHandle,
                success: this.successHandle,
                duplicate: this.showDuplicate,
                cleanOwner: this.cleanOwnerHandle,
                validaterule: this.validateruleHandle,
                reloadcomps: this.reloadComps,
                mdrendercomplete: this.mdrendercomplete,
                applyPlugin: this.applyPlugin,
                warpperClick: () => {this.$el && this.$el.click()},
                toggleCommonButton: (actions, status) => {this.trigger('toggleCommonButton', actions, status)},
                hideValidatVeConfirm: this.hideValidatVeConfirm,
                renderFooterSlot: this.renderFooterSlot,
                destroy: (data) => {this.trigger('destroy', data)},
                toggleeditstatus: this._toggleEditStatus,
                getScroll: callBack => {
                    let scrollBar = this.get('scrollBar');
                    let $scroll;
                    if(scrollBar) {
                        $scroll = scrollBar.$el
                    } else {
                        $scroll = this.$('>.crm-scroll');
                        if(!$scroll.length) {
                            $scroll = this.$el.closest('.scroll-content');
                        }
                    }
                    callBack($scroll);
                }
            });
        },

        get: function(key) {
            return this.model && this.model.get(key);
        },

        set: function(key, value) {
            this.model && this.model.set(key, value);
        },

        loadTemplate: require('./load-html'),
        template: require('./view-html'),

        render: function() {
            this.$el.html(this.loadTemplate({loadTemplateLength: this.options.loadTemplateLength || 18}));
            this.resizeHandle();
            this.changeTitle();
            // [性能埋点]获取描述耗时START
            this._costTimeStart = new Date().getTime();
            this.model.fetch();
            this.index = 0;
        },

        applyPlugin(next, res) {
            // 安装v1版本自定义插件，未迁移完成
            const customPlugins = this.model.get('customPlugins');
            // if (!FS.util.getUserAttribute('paas_form_plugin_v2') && plugins && plugins.length) {
                if (!CRM.util.isUseNewCustomPlugin(this.options.apiName || this.options.apiname) && customPlugins && customPlugins.length) {
                const plugin = customPlugins.find(item => item.client_type === 'web');
                if (plugin) {
                    return PAAS.install_plugin(
                        `${this.get('apiname')}.form.${this.get('record_type')}`,
                        // 'http://localhost:9000/static/form.js',
                        plugin.api_name,
                        this
                    ).then((status) => {
                        if (FS.util.getUserAttribute('paas_plugin_strict_mode') && status === false) {
                            let errMsg = $t('加载失败请刷新页面重试');
                            CRM.util.alert(errMsg);
                            return;
                        }
                        next();
                    },() => {
                        if (FS.util.getUserAttribute('paas_plugin_strict_mode')) {
                            let errMsg = $t('加载失败请刷新页面重试');
                            CRM.util.alert(errMsg);
                            return;
                        }
                    })
                }
            }
            next();
            return;
        },

		changeTitle:$.noop,

        reloadComps: function(flag) {
            var me = this;
            _.each(me.forms, function(a) {
                a && a.destroy && (a.destroy(), a = null);
            });
            me.forms = {};

            me.$el.html(me.template({
                detailObjectList: me.get('detailObjectList'),
                layout: me.get('layout'),
                data: me.get('data'),
                className: me.__getClassName(),
                showNav: me.get('noShowNavBar') ? false : me.options.showNav,
                viewType: me.options.viewType,
                fieldStyle: me.get('fieldStyle')
            }))
            me.model.fns = {};
            me.model.__mdUIStatus = null;
            flag && (me.model.__onLoadExcuted = false);
            me.$mdMixWrap = me.$('.j-mdmix-wrap');
            me.$relateMixWrap = me.$('.j-relatemix-wrap');
            me.renderComps();
            me.$groups = me.$grounps = me.$('.j-item-group');
            me.renderNav();
            me.renderFooterSlot();
            setTimeout(function() {
                me.model.trigger('_initRule');
                me.model.cascadeChangeTips(true);
                if (flag && !me.forms.md && me.model.triggerOnLoadEvent) {
                    me.model.triggerOnLoadEvent();
                }
            })
        },

        __getClassName() {
            let me = this;
            let cls1 = me.className || me.options.className;
            let cls2 = me.$el.width() < 1800 && 'crm-field-tiny-view';
            let cls3 = me.options.isReallyFull && 'crm-action-nfield-fulldd';
            return `b-g-crm${cls1 ? ` ${cls1}` : ''}${cls2 ? ` ${cls2}` : ''}${cls3 ? ` ${cls3}` : ''}`;
        },

        errorHandle: function(serverReturnData) {
            this.$('.crm-loading').hide();
            this.trigger('error', serverReturnData);
        },

        successHandle: function(serverReturnData, modelData, res) {
            this.trigger('success', serverReturnData, modelData, res);
        },

        resizeHandle: function() {
            this.trigger('resize');
        },

        //布局是空的
        emptyLayoutHandle() {
            this.$el.html(`<div style="display:flex;align-items:center;justify-content:center;height:100%;"><div><div style="text-align:center"><span style="font-size:34px;color:#bbb" class="el-icon-document"></span></div><p>${$t('您当前没有可编辑的字段')}</p></div><div>`);
        },

        initView: function() {

            // [性能埋点]获取描述耗时END
            var _serverCostTime = new Date().getTime() - this._costTimeStart;
            var me = this;

            //数据存到modle上 埋点用
            me.set('_serverCostTime', _serverCostTime);

            var _domCostTimeStart = new Date().getTime();
            me.$el.html(me.template({
                detailObjectList: me.get('detailObjectList'),
                layout: me.get('layout'),
                data: me.get('data'),
                className: me.__getClassName(),
                showNav: me.get('noShowNavBar') ? false : me.options.showNav,
                viewType: me.options.viewType,
                fieldStyle: me.get('fieldStyle')
            }))
			me.$mdMixWrap = me.$('.j-mdmix-wrap');
            me.$relateMixWrap = me.$('.j-relatemix-wrap');
            me.renderComps();
            me.$groups = me.$grounps = me.$('.j-item-group');
            me.trigger('rendercomplete', {
                describeCost: _serverCostTime
            });
            me.renderNav();
            me.renderGroupSlots();

            me.resizeHandle();
            setTimeout(() => me.resizeHandle(), 1500);//保证一些异步组件初始化之后再触发一次resize

            //主表单的渲染时间
            me.set('_mainFormCostTime', new Date().getTime() - _domCostTimeStart);

            setTimeout(function() {
                try {
                if(!me.model) return;

                me.model.trigger('_initRule');
                me.model.cascadeChangeTips(true);
                //me.get('isCopy') && me.model.triggerCalculate('owner');//已转到model._preCalculate处理，避免业务是那个并发计算
                if(me.get('field_remind')) {
                    me.setFieldRemind(me.get('field_remind'));
                }

                if(!me.forms.md) {
                    me.model.triggerOnLoadEvent && me.model.triggerOnLoadEvent();
                }
                } catch(err) {
                    console.log(err);
                }
            })

            me.get('tenant_id') && me.toggleGroup();
        },

        renderGroupSlots() {
            let slots =  this.get('groupSlots');
            if(!slots) return;
            let layouts = this.get('layout');
            let groupSlotInstances = [];
            this.$groups.each((index, group) => {
                let groupId = $(group).data('groupid');
                let t = _.findWhere(layouts, {groupId});
                t && _.each(slots, slot => {
                    let instance = slot(t, group);
                    if(instance && instance.destroy) {
                        groupSlotInstances.push(instance);
                        $(group).css('display', 'flex');
                    }
                })
            })

            groupSlotInstances.length && (this.groupSlotInstances = groupSlotInstances);
        },
        destroyGroupSlots() {
            _.each(this.groupSlotInstances, slot => {
                try {
                    slot.destroy();
                } catch(e) {}
            })
            
            this.groupSlotInstances = null;
        },

        renderComps: function() {
            var me = this;
            var model = this.model;
            var forms = this.forms;
            var options = this.options;
			var mycomponents = _.extend({}, this.mycomponents, this.get('pluginSetComponents'));
			var pluginComps = (window.PAAS && PAAS.get_plugin && PAAS.get_plugin(this.get('apiname') + '.form.field')) || {};
            var isFromDraft = !!this.get('oldDraftData');
            if(isFromDraft) {
                this.model.set('isCopy', true);
            }
            if(me.get('apiname')!="ServiceKnowledgeObj"
				&&!mycomponents.rich_text) {//ServiceKnowledgeObj 不重置
                mycomponents = _.extend({
                    rich_text: components.rich_text_new,
                    //html_rich_text: components.rich_text_new
                }, mycomponents)
            }
            if(mycomponents.newmd) {//如果重写了newmd 则默认把md20强制换为newmd
                mycomponents = _.extend({
                    md20: components.newmd
                }, mycomponents)
            }

            this.$('.j-comp-wrap').each(function() {
                var obj = $(this).data();
                var Comp = pluginComps[obj.apiname] || pluginComps[obj.type] || mycomponents[obj.apiname] || mycomponents[obj.type] || components[obj.apiname + '__self'] || components[obj.type];
                var comp;
                if (Comp) {
                    comp = new Comp({
                        el: this,
                        model: model,
                        type: obj.type,
                        apiname: obj.apiname,
						zIndex: options.zIndex,
						$mdMixWrap: me.$mdMixWrap,
                        $relateMixWrap: me.$relateMixWrap
                    })
                    comp.on(`change.${comp.apiname}`, function(field, val, opts){
                        me.trigger(`change.${comp.apiname}`, field, val, opts);
                    })
                    comp.render();
                    comp.setStatus && comp.setStatus();
                    forms[obj.apiname] = comp;
                    comp.on('change', function (field, val) {
                        me.trigger('change', field, val)
                    })
                }
            })
            if(isFromDraft) {
                setTimeout(function() {
                    me.model.set('isCopy', void 0);
                })
            }
            this.model.set('forms', this.forms);
            //单选需要触发一下依赖关系
            _.each(forms, function(a) {
                a.autoTriggerEvent && a.autoTriggerEvent()
            })

            this.__renderFreeApproval();

            this.renderOnlyRequiredCheck();
        },
        __renderFreeApproval(){
            try {
                let me = this;
                this.__freeApprovalInstance && this.__freeApprovalInstance.destroy();
                let isEdit = me.get('isEdit');
                let data = me.get('data');
                let commonForm = me.get('is_common_form');
                let isFlowLayout = me.get('crmFieldextendFetchQueryParam')
                  && me.get('crmFieldextendFetchQueryParam').layoutApiName
                if(isEdit && data.life_status !== 'ineffective' || isFlowLayout){
                    return;
                }
                require.async('paas-paasui/vui',()=>{
                    if(!me.get('apiname')){
                        return;
                    }
                    window.PaasVUI && PaasVUI.formLayout.getLatestTasks(me.get('apiname'),
                      !me.get('isCopy') ? me.get('dataId') : undefined).then((res)=>{
                        if(res && res.showSelectFreeNode){
                            this.set('freeApprovalDef',{tasks:res.tasks || []});
                        }
                        if(res && res.showSelectFreeNode && commonForm){
                            let wrapper =$('<div class="free-approval"></div>');
                            let field_box = this.$el.find('.crm-action-nfield').length ? this.$el.find('.crm-action-nfield') : this.$el.find('.crm-action-field'); // 兼容工作台发起自由流程审批
                            field_box.append(wrapper)
                            me.__freeApprovalInstance = PaasVUI.formLayout.render(wrapper,res);
                            me.__freeApprovalInstance.$on('change',(tasks)=>{
                                this.set('freeApprovalDef',tasks);
                            })
                        }
                    })
                })
            } catch (e){
                console.log(e);
            }
        },
        //根据规则设置组件状态
        setCompStatusByRule: function(_rule, noAnimation) {
            this.model._hackRemoveAreaUIEventAttr(_rule);

            try {
                var isResize;
                var me = this;
                var isSkipLayoutFieldStateCheck = this.get('isSkipLayoutFieldStateCheck');
                _rule.no_required_field = _.difference(_rule.no_required_field, _rule.required_field);
                _rule.hide_field = _.difference(_rule.hide_field, _rule.show_field);

                var forms = me.forms || {};
                _.each(_rule.required_field, function(apiname) {
                    var comp = forms[apiname];
                    //有读写情况下才设为必填
                    comp && !comp.fieldAttr.is_required && !comp.fieldAttr.is_readonly && (me._findClosest(comp).addClass('f-required').removeClass('f-no-required'), comp.fieldAttr.is_required = comp.fieldAttr.a_is_required = true, _.contains(_rule.show_field, apiname) || _rule.show_field.push(apiname));
                })
                _.each(_rule.no_required_field, function(apiname) {
                    var comp = forms[apiname];
                    if (!comp) return;
                    var fieldAttr = comp.fieldAttr;
                    if (fieldAttr.is_required && !fieldAttr.a_is_required) return;//说明是原生的必填字段，不予响应
                    fieldAttr.is_required && (me._findClosest(comp).removeClass('f-required').addClass('f-no-required'), fieldAttr.is_required = fieldAttr.a_is_required = false, comp.hideError());
                })

                _.each(_rule.show_field, function(apiname) {
                    var comp = forms[apiname];
                    if (comp && comp._elIsHide) {
                        var _el = me._findClosest(comp);
                        if(me.get('supportRequiredCheck')) {
                            _el.css('display', 'flex');
                        } else {
                            _el.show();
                        }
                        _el.addClass('f-item-animation-show');
                        comp._noGet = false;
                        comp._elIsHide = false;
                        isResize = true;
                    }
                })
                _.each(_rule.hide_field, function(apiname) {
                    var comp = forms[apiname];
                    if (comp && !comp._elIsHide && (!comp.fieldAttr.is_required || isSkipLayoutFieldStateCheck)) {
                        comp.hideError();
                        comp.hideUIError && comp.hideUIError();
                        var _el = me._findClosest(comp);
                        _el.hide().removeClass('f-item-animation-show');
                        comp._noGet = true;
                        comp._elIsHide = true;
                        isResize = true;
                    }
                })


                ////////////750 新增只读不只读控制////////////
                _.each(_rule.readonly_field, function(apiname) {//把字段设为只读
                    var comp = forms[apiname];
                    if (!comp) return;
                    var fieldAttr = comp.fieldAttr;
                    if (fieldAttr.is_required && !fieldAttr.a_is_required && !isSkipLayoutFieldStateCheck) return;//说明是原生的必填字段，不予响应
                    comp.disable && (comp.disable(), comp._elIsReadonly = true);
                })
                _.each(_rule.force_readonly_field, function(apiname) {//把字段设为只读
                    var comp = forms[apiname];
                    if (!comp) return;
                    comp.disable && (comp.disable(), comp._elIsReadonly = true);
                })
                _.each(_rule.no_readonly_field, function(apiname) { //把字段设为不只读
                    var comp = forms[apiname];
                    if (!comp) return;
                    var fieldAttr = comp.fieldAttr;
                    if (fieldAttr.is_required && !fieldAttr.a_is_required && !isSkipLayoutFieldStateCheck) return;//说明是原生的必填字段，不予响应
                    comp._elIsReadonly && (comp._elIsReadonly = false, comp.enable && comp.enable());
                })

                me.toggleGroup();

                isResize && me.resizeHandle();
                
            } catch(e) {}
        },

        //当分组下无数据隐藏对应分组信息
        toggleGroup: function() {
            var me = this;
			var forms = me.forms;
			var groupStatus = {};
			forms && _.each(me.get('layout'), function (a) {
                if(!a.components || !a.label || a.noHeader) return;
                var tmp = !!_.find(a.components, function(b) {
                    return !(forms[b.api_name] && forms[b.api_name]._noGet);
                })
                var flag = me._onlyCheckComp && me._onlyCheckComp.dValue;
                if(tmp && flag) {//要显示，还需要判断是否仅显示必填
                    tmp = !!_.find(a.components, function(b) {
                        let s = forms[b.api_name] && forms[b.api_name].fieldAttr;
                        return s && s.is_required;
                    })
                }
				me.$groups.filter('[data-groupid="' + a.groupId + '"]')[tmp ? 'show' : 'hide']();

				groupStatus[a.groupId] = tmp;
            })

			me.model && me.model.trigger('navstatus:change', groupStatus);
        },

        _findClosest: function(comp) {
            return comp.$el.parent();
        },

        show: function() {
            this.$el.show();
            this.trigger('show');
        },

        hide: function() {
            this.$el.hide();
            this.trigger('hide')
        },

        getElementByApiname: function(apiname) {
            return this.$('[data-apiname="' + apiname + '"]');
        },

        isShowNotNull: function() {

        },

        _getScrollEl: function() {
        	if(!this.$el){
        		console.error($t('crm.codeExecutionSequenceTip'));
        		return []
			}
            if(!this._scrollEl) {
                 this._scrollEl = this.$el.closest('.scroll-content')
            }
            return this._scrollEl;
        },

        scrollToGrounp: function(index) {
            if(!this.$groups) return;
            var $g = this.$groups.eq(index);
            var $scroll = this._getScrollEl();
            if (!$scroll) return;
            $scroll.scrollTop(0);
            $scroll.scrollTop($g.offset().top - 136);
        },

        scrollToMD: function() {
            if(!this._mdrendered) {
                this._scorllToMD = true;
            } else {
                var $scroll = this._getScrollEl();
                if (!$scroll.length) return;
                $scroll.scrollTop(0);
                var size = this.$('.j-comp-wrap[data-apiname="md"]').offset().top;
                $scroll.scrollTop(size - 40);
            }
        },

        /**
         *@desc 滚动到第一个错误项的位置
         */
        scrollToError: function() {
            var $scroll = this._getScrollEl();
            if (!$scroll.length) return;

            var $error = this.$('.fm-error, .ui-error');
            if (!$error.length) return;

            if(this.get('scrollToCenter')) {
                $error[0].scrollIntoView({block: 'center'});
                return;
            }

            $scroll.scrollTop(0);
            var size = $error.offset().top;
            $scroll.scrollTop(size - $scroll.offset().top - 40);
        },

        /**
         *@desc 简单验证数据是否正确 可重写
         *@return {{Boolean}} true 数据正确 可以提交到服务器 false 不提交
         */
        validate: function() {
            return this.$el && !this.$('.fm-error, .ui-error')[0];
        },

        collect: function(all) {
            var data = {
                object_describe_api_name: this.get('apiname'),
                object_describe_id: this.get('_id')
            };

            _.each(this.forms, function(a, name) {
                var value = a.getValue();
				if (a._noGet && a.hasError) {
                    data[name] = null;
                    a.hideError();//把错误提示也隐藏掉，避免提交不了
                    return;
                };

				if(value && value.type === 'area') {
                    delete value.type;
                    _.extend(data, value);
                    return;
                }
                if(name === 'md') {
                    data.details = value;
                    return;
                }
                if(name === 'relateadd') {//关联对象
                    data.related_data_list = value;
                    return;
                }
                data[a.apiname || name] = value;

                if (all && a.fieldAttr && a.fieldAttr.render_type === 'object_reference') {
                    data[`${a.apiname || name}__r`] = a.getInputValue();
                }
            })

            return _.extend({}, this.get('param'), data);
        },

        submit: function(e, noCheckRequire, a, b, c, num = 0) {
            var me = this;
            if(me._submiting || !me.model || $(e.currentTarget).hasClass('state-requesting')) return;
            var layout = me.get('layout');
            if((!layout || !layout.length) && !me.get('layoutEmptyContinue')) return;
            var noCheck = noCheckRequire === true;

            me._submiting = true;
            me.checkDataBySelf();

            _.delay(function() {   // 整体做了 50秒延迟， 目的 最后一次失去焦点 修改默认值
                if(!me.model) return;
                if(me._isStopSubmit()) {//计算中
                    me._submiting = false;
                    return;
                };
                var submitEl = $(e.target);
                var data = noCheck ? me.collectDraft() : me.collect();

                if (!noCheck && !me.validate()) {
                    me._submiting = null;
                    me.scrollToError();
                    return;
                }

				if(me._checkCalculate() && num < 6) { //计算字段还没有完成延迟两秒再提交
                    var stext = submitEl.text();
                    submitEl.html('<span class="crm-action-icon-requesting"></span>' + stext);
                    _.delay(function() {
                        me._submiting = null;
                        submitEl.html(stext);
                        me.submit(e, noCheckRequire, a, b, c, num + 1);
                    }, 2000)
                } else {
                    me.model.runBeforeSubmit(data, e).then(rst => {
                        me._submiting = null;
                        if(rst.fail) return;
                        if(rst.noChange) {
                            me.model.asyncvalidateFormData(data).then(noPass => {
                                if(noPass) {
                                    me.scrollToError();
                                } else {
                                    me.model.submit(data, submitEl);
                                }
                            });
                        } else {
                            data = noCheck ? me.collectDraft() : me.collect();
                            if(!noCheck && !me.validate()) {
                                me.scrollToError();
                            } else {
                                me.model.asyncvalidateFormData(data).then(noPass => {
                                    if(noPass) {
                                        me.scrollToError();
                                    } else {
                                        me.model.submit(data, submitEl);
                                    }
                                });
                            }
                        }
                    })
                }
            }, 200);
        },

        _isStopSubmit() {
            let model = this.model || {};
            if(model._autoFanyiing) {
                FxUI.Message({
                    duration: 1500,
                    message: $t('paas.crm.fanyiing', null, '自动翻译执行中，请稍后提交'),
                    type: 'warning'
                })
                return true;
            }
            if(model._analysisAjax || model._uiLoadEventAjax || model.mdEditExcuteing || model._pluginLoadingStatus || model._dataChangePlugining || model._fetchObjectOutOwnering) {//计算中
                FxUI.Message({
                    duration: 1500,
                    message: $t('paas.crm.ing.tip', null, '数据计算中，请稍后再操作'),
                    type: 'warning'
                })
                return true;
            };
        },

        submitAndSkipValidate(e) {
            if(!this.__$skipValidateWait) {
                this.__$skipValidateWait = this.model.commonWaiting();
            }
            _.delay(() => {
                if(this._checkCalculate()) {
                    _.delay(() => this.submitAndSkipValidate(e), 2000);
                    return;
                }
                this.__$skipValidateWait.remove();
                this.model.submitAndSkipValidate(this.collect(), e && $(e.target));
            }, 200)
        },

        //判断是否正在提交数据中
        isSubmiting: function() {
            return this._submiting || !!this.model.updateAjax || !!this.model.createAjax;
        },

		checkDataBySelf:$.noop,

        //设置字段级别提示
        setFieldRemind: function(reminds) {
            var forms = this.get('forms');
            _.each(reminds, function(content, key) {
                var comp = forms[key];
                comp && comp.showUIError(content);
            })
        },

        _checkCalculate: function(cb) {
            var m = this.model || {},
                _checkRes= m._fetchObjectOutOwnering || m._dataChangePlugining || m.mdEditExcuteing || m._pluginLoadingStatus || m._uiLoadEventAjax || !!m._analysisAjax || !!m._calculateMDAjax || !!m._uiEventAjax || !!m._signatureAjax || (m._ruleData && !_.isEmpty(m._ruleData)) || this.__cacheRule,
                beforeCheckRes = this.businessAjaxCheck();
			return beforeCheckRes|| _checkRes;
        },
        //保存提交前，业务测是否有接口调用检查勾子函数
        businessAjaxCheck:function(){
            return false;
        },

          //通过监听viewClick事件代替在body上监听click来关闭某些浮层
        _triggerClickEvent: function() {
            this.model && this.model.trigger('viewClick');
        },

        /**
         *@desc 显示查重结果
         */
        showDuplicate: function(opts, param) {
            var me = this;
            // me.trigger('onlyhide');

            if(me.destroyDuplicate) {
                me.destroyDuplicate();
                me.destroyDuplicate = null;

            }
            if(me.__duplicateRuleName) {
                opts.data = _.extend({}, opts.data, {
                    duplicate_rule_api_name: me.__duplicateRuleName
                })
            }

            CRM.api.duplicate(_.extend({
                cancelHandle: function() {
                    me.destroyDuplicate = null;
                    me.__duplicateRuleName = '';
                },
                confirmHandle: function(btn, destroyFn, ruleName) {
                    //老对象的特殊参数
                    param.object_data.IsDuplicateSearch = false;

                    param.optionInfo = _.extend({}, param.optionInfo, {
                        isDuplicateSearch: ruleName ? void 0 : false,
                        duplicateRuleApiName: ruleName
                    });
                    me.continueSave(param, btn);
                    me.destroyDuplicate = destroyFn;
                    me.__duplicateRuleName = ruleName;
                },
                noShowContinue: param && param.noShowContinue
            }, opts));
        },

		cleanOwnerHandle: function(param) {
            var me = this;
            this.cleanOwnerConfirm = CRM.util.storeConfirm($t('清空负责人提示{{tip}}', {tip: `<em class="crm-ui-help crm-ui-title" data-title="${$t("满足条件时不清空负责人")}" data-pos="bottom">?</em>`}), null, function(e, isStore) {
                isStore && CRM.setLocal('skipCheckCleanOwner', isStore);
                param.skipCheckCleanOwner = true;
                me.continueSave(param, $(e.target));
            })
        },

        //查重 继续保存 可覆盖
        continueSave: function(param, btn) {
            this.model._continueSubmit(param, btn);
        },

        hideValidatVeConfirm() {
            this.validateConfirm && (this.validateConfirm.destroy(),this.validateConfirm = null);
        },

        //显示验证规则阻拦不阻拦提示
        validateruleHandle: function(messages, param, isFun, res, isPlugin) {
            var me = this;
            var html = me.ruleTipTemplate({messages: messages});
            var isContinue = messages[0].type !== 'red';
            this.hideValidatVeConfirm();
            if(isContinue && !param.noShowContinue) {
                var confirm = util.confirm(html, $t('提示'), function(e) {
                    //继续保存不再走验证规则
                    if(isFun) {
                        param.optionInfo = {
                            skipFuncValidate: true
                        }
                        if(res && res.funcValidateMessage && res.funcValidateMessage.submitReturnData) {
                            param.optionInfo._funcValidateData = {
                                details: res.newObjectData,
                                object_data: res.objectData,
                                related_data_list: res.relatedDataList
                            }
                        }
                    } else if(isPlugin) {
                        param.optionInfo = {
                            skippedValidatorList: res.validationResult.skippedValidatorList
                        }
                    } else {
                        param.optionInfo = {
                            useValidationRule: false
                        }
                    }
                    me.model._continueSubmit(param, e.currentTarget);
                    me.validateConfirm = confirm;
                }, {
                    btnLabel: {
                        confirm: $t('继续保存')
                    },
                    zIndex: 10010
                })
            } else {
                let matchDataIndex = messages[0].matchDataIndex;
                let _alert =  util.alert(html, null, {
                    hasMask: !matchDataIndex //从对象数据阻断提示，弹框不需要遮罩，因为要看着提示改数据
                });
                me.validateConfirm = _alert;
                
                if(matchDataIndex) {
                    this.model.trigger('scrollToDetailsByRowId', matchDataIndex);//根据数据rowId滚动到指定的从对象数据
                    _alert.element && _alert.element.on('click', '.tip-item', () => {
                        this.model.trigger('scrollToDetailsByRowId', matchDataIndex);//根据数据rowId滚动到指定的从对象数据
                    })
                }
            }
        },

        destroy: function() {
            this.undelegateEvents(), this.stopListening(), this.off();
            this.$el && this.$el.empty();
            _.each(this.forms, function(a) {
                a && a.destroy && (a.destroy(), a = null);
            });
            this.destroyFooterInstance();
            this.destroyGroupSlots();
            try {
                this.leftNavBar && (this.leftNavBar.destroy(), this.leftNavBar = null);
            } catch(e) {}
            this.cleanOwnerConfirm && (this.cleanOwnerConfirm.destroy(), this.cleanOwnerConfirm = null);
            this.destroyDuplicate && (this.destroyDuplicate(), this.destroyDuplicate = null);
            this.hideValidatVeConfirm();
            this.model && (this.model.abort(), this.model.off(), this.model.destroy());
            this.__freeApprovalInstance && this.__freeApprovalInstance.destroy();
            this._onlyCheckComp && this._onlyCheckComp.destroy();
            this._toggleEditStatus();
            this._submitedParam = null;
            this.ipts = this.$el = this.el = this.model = this.events = this.options = this.forms = this.components = this._scrollEl = null;
        },

        switchView: function(type) {
            this.model.set('viewType', this.options.viewType = type);
            var $f = this.$('.crm-action-nfield:first');
            var addCls;
            var removeCls;
            if(type === 'mini') {
                addCls = 'crm-action-nfield-mini crm-action-nfield-mico';
                removeCls = 'crm-action-nfield-mid crm-action-nfield-single';
            } else if(type === 'mid') {
                addCls = 'crm-action-nfield-mini crm-action-nfield-mid';
                removeCls = 'crm-action-nfield-single crm-action-nfield-mico';
            } else if(type === 'single') {//单列
                addCls = 'crm-action-nfield-single';
                removeCls = 'crm-action-nfield-mini crm-action-nfield-mid crm-action-nfield-mico';
            } else {
                removeCls = 'crm-action-nfield-mini crm-action-nfield-mid crm-action-nfield-single crm-action-nfield-mico';
            }
            addCls && $f.addClass(addCls);
            removeCls && $f.removeClass(removeCls);

            _.each(this.forms, function(comp) {
                comp.resize && comp.resize();
            });
            this.trigger('switchView', type);
        },

        renderNav: require('./nav'),

        mdrendercomplete: function() {
            try {
                this._mdrendered = true;
                if(this._scorllToMD) {
                    this.scrollToMD();
                }
                if(!this.model) return;

                var activeNavName = this.get('activeNavName')
                activeNavName && this.model.trigger('mdnav:change', activeNavName, true);
                this.model.hideNotMatchMD();//隐藏没有匹配上的md对象
            } catch(e) {
                CRM.util.uploadLog('form', 'field', {
                    eventId: 'mdrendercompleteerror',
                    eventData: {
                        apiName: this.get('apiname') || '',
                        msg: (e && e.message) || $t('渲染异常')
                    }
                });
            }
            
            this.model.triggerOnLoadEvent();
        },

        //是否支持草稿箱功能
        isSupportDraft: function() {
            let forms = this.get('forms');
            if(_.isEmpty(forms)) return;//空表单不支持
            return this.get('support_draft');

            // return 
            // if(!flag) return;

            // if(!this.get('isEdit')) return true;//新建时不用判断灰度，直接根据server下发的值

            // let crmEditDraft =  util.getUserAttribute('crmEditDraft');
            // if(!crmEditDraft) return;//没有灰度相关功能

            // let apiname = this.get('apiname');
            // return /__c$/.test(apiname) || _.contains(crmEditDraft, apiname);
        },

        isSupportSaveAndContinue: function() {
            var c = this.get('isSubmitAndCreate');
            if(c !== void 0) return c;

            var forms = this.get('forms');
            return !_.isEmpty(forms) && !this.get('isEdit') && this.get('_from') !== 'mapping' && this.get('supportSaveAndCreate'); //仅新建并且不是转换的时候支持
        },

        // 是否支持拉单
        isSupportPullOrder() {
            return !!_.findWhere(this.get('uibuttons'), {action: 'ReferenceCreate'});
        },

        isNeedSaveDraft: function() {
            return this.isSupportDraft() && this.get('formDataChange');
        },

        saveDraft: function(opts) {
            var me = this;
            me.saveDraftToServer(function(data) {
                me.set({
                    oldDraftData: data,
                    formDataChange: null
                })
            }, opts);
        },

        collectDraft: function() {
            var data = _.extend({
                object_describe_api_name: this.get('apiname'),
                object_describe_id: this.get('_id')
            }, this.get('param'), this.get('data'));

            if(this.forms.md) {
                data.details = this.forms.md.getValue(true);
            }
            if(this.forms.relateadd) {//暂不支持保留关联对象数据
                data.related_data_list = this.forms.relateadd.getValue(true);
            }
            return data;
        },

        saveDraftToServer: function(callback, opts) {
            var me = this;

            if(!(opts && opts.noRemind)) {
                util.waiting($t('保存中') + '...', true);
            }

            _.delay(function() {   // 整体做了 50秒延迟， 目的 最后一次失去焦点 修改默认值
                if(me._isStopSubmit()) {
                    util.waiting(false);
                    return;
                };

                if(me._checkCalculate()) { //计算字段还没有完成延迟两秒再提交
                    _.delay(function() {
                        me.saveDraftToServer(callback);
                    }, 2000)
                } else if(me.model) {
                    var draftData = me.model.formatParam(me.collectDraft());
                    draftData.title = me.model.createDraftTitle();
                    draftData.object_data = _.extend({draft_owner_object: me.get('display_name')}, me.get('data'), draftData.object_data);
                    //draftData.object_data.related_data_list = draftData.related_data_list;

                    draftData.object_data.record_type__r = me.model.getRecordTypeLabel();
                    draftData.object_data.field_remind = me.model._getFieldRemind();
                    draftData.object_data.cacheFormRuleData = me.model.getMergeCacheFormRuleData();

                    //编辑时需要存原始数据和来源
                    if(me.get('isEdit')) {
                        draftData.data_source = 'Edit';
                        draftData.object_data.originalData = me.get('originalData');
                        draftData.object_data.originalDetails = me.get('originalDetails');
                    }

                    me.beforeSaveDraft(draftData);
                    if(draftData.related_data_list && _.find(draftData.related_data_list, arr => _.find(arr, b => b.dataList && b.dataList.length))) {
                        delete draftData.related_data_list;
                        delete draftData.object_data.related_data_list;
                        let flag;
                        let confirm = util.confirm($t('crm.save.rd.tip', null, '相关对象数据暂不支持保存草稿，是否继续？'), $t('提示'),  () => {
                            flag = true;
                            confirm.hide();
                        }, {
                            zIndex: 20000,
                            hideFn() {
                                flag ? me.submitDraft(draftData, callback, opts) : util.waiting(false);
                            }
                        })
                    } else {
                        me.submitDraft(draftData, callback, opts);
                    }
                }
            }, 200);
        },

        beforeSaveDraft: $.noop,

        submitDraft(draftData, callback, opts) {
            let cbb = this.get('beforeSaveDraftCallBack');
            cbb && cbb(draftData);

            CRM.api.save_draft({
                complete: data => {
                    util.waiting(false);
                    data && callback(data);
                    let saveDraftCompleteCallback = this.get('saveDraftCompleteCallback');
                    saveDraftCompleteCallback && saveDraftCompleteCallback(data);
                },
                draftData: draftData,
                oldDraftData: this.get('oldDraftData'),
                noRemind: opts && opts.noRemind
            });
        },

        pullOrder: function(e) {
            var me = this;
            if(me._pullingOrder) return;
            me._pullingOrder = true;
            _.delay(function() {
                var obj = me.collectDraft();
                var details = obj.details;

                if(me._isStopSubmit()) {
                    me._pullingOrder = null;
                    return;
                };

				if(me._checkCalculate()) { //计算字段还没有完成延迟两秒再提交
                    _.delay(function() {
                        me._pullingOrder = null;
                        me.pullOrder(e);
                    }, 2000)
                } else {
                    delete obj.details;
                    me._pullingOrder = null;
                    CRM.api.secondRefCreate({
                        data: obj,
                        details: details,
                        apiname: obj.object_describe_api_name,
                        recordType: me.get('record_type'),
                        triggerPage: me.model.getTriggerInfo().trigger_page,
                        conversionRuleInfo: me.get('conversionRuleInfo'),
                        success: (res) => {
                            me.trigger('toggleCommonButton', ['pullOrder', 'saveDraft'], true);
                            me.set('support_draft', false);
                            me.set('mdData', res.mdData);
                            me.set('data', _.extend(me.get('data'), res.data));
                            me.set('conversionRuleInfo', res.conversionRuleInfo);
                            // this.set('_staticData', res._staticData);
                            // this.set('converParam', {ruleApiName: 'xxxx})
                            me.set('_extOptionInfo', res._extOptionInfo);
                            me.set('_from', res._from);
                            me.reloadComps(true);
                        }
                    });
                }
            }, 200);
        },

        //自定义按钮
        formUIActionHandle: function(e) {
            //util.waiting(' ');
            var me = this;
            if(me._formSubmiting) return;
            me._formSubmiting = true;
            _.delay(function() {
                if(me._isStopSubmit()) {
                    me._formSubmiting = null;
                    return;
                }
                var $target = $(e.target);
                var button_apiname = $target.attr('data-apiname');
                if(!button_apiname) {
                    me._formSubmiting = null;
                    return;
                }
                var btn = _.findWhere(me.get('uibuttons'), {api_name: button_apiname});
                var obj = me.collectDraft();
                var details = obj.details;

				if(me._checkCalculate()) { //计算字段还没有完成延迟两秒再提交
                    _.delay(function() {
                        me._formSubmiting = null;
                        me.formUIActionHandle(e);
                    }, 2000)
                } else {
                    me._formSubmiting = null;
                    delete obj.details;

					if (me[`${button_apiname}Handle`]) {
						me[`${button_apiname}Handle`]({
							btn: btn,
							apiname: obj.object_describe_api_name,
							data: obj,
							details: details,
							$btn: $target,
						});
						return;
					}

                    let beforeFormUIAction = me.get('beforeFormUIAction');
                    let param = _.extend({}, btn, {
                        apiname: obj.object_describe_api_name,
                        data: obj,
                        details: details,
                        $btn: $target,
                        trigger_info: me.model.getTriggerInfo(),
                        success: function(res) {
                            if(!res) return;
                            if(res.type === 'update') {
                                me.updateByData(res);
                            } else if(res.type === 'uievent') {
                                me.model.excuteCustomBtnUIEvent(res.eventId, btn);
                            }
                        }
                    })

                    if(beforeFormUIAction) {
                        beforeFormUIAction(param).then(() => CRM.api.form_uiaction(param));
                    } else {
                        CRM.api.form_uiaction(param);
                    }
                }
            }, 200);
        },

        updateByData: function(res) {
            this.model._updateDataByUIEventData(res);
        },

        renderFooterSlot() {
            let slots = this.get('pluginFooterSlot');
            if(!slots) return;

            this.destroyFooterInstance();

            let footerSlotInstances = [];
            let $wrapper = this.$('.crm-action-nfield >.j-footer-slot');
            _.each(slots, slot => {
                footerSlotInstances.push(slot($wrapper));
            })

            this.footerSlotInstances = footerSlotInstances;
        },

        destroyFooterInstance() {
            if(this.footerSlotInstances) {
                _.each(this.footerSlotInstances, footerInstance => {
                    try {
                        footerInstance && footerInstance.destroy && footerInstance.destroy();
                    } catch(e) {}
                })
                this.footerSlotInstances = null;
            }
        },

        //900新增，仅显示必填选项相关逻辑
        renderOnlyRequiredCheck() {
            let me = this;
            if(!me.get('supportRequiredCheck')) return;

            if(me._onlyCheckComp) {
                me._onlyCheckComp.destroy();
            }
            let $wrap =  $('<div>');

            let $f = me.$('.crm-action-nfield:first');

            let kk = 'crm_field_action_fieldstyle';

            $wrap.prependTo($f);
            me._onlyCheckComp = FxUI.create({
                wrapper: $wrap[0],
                template: `
                    <div style="margin-bottom:16px;display:flex;align-items:center;">
                        <label>${$t('ava.object_form.form.showrequired', null, '仅显示必填字段')}</label>
                        <fx-switch width="32" size="mini" v-model="dValue" style="margin: 0 24px 0 8px"/>
                        <label style="margin-right:8px">${$t('paas.crm.field.linestyle', null, '行距设置')}</label>
                        <fx-radio-group v-model="dStyle" size="micro" isStroke>
                            <fx-radio-button label="compact">${$t('紧凑')}</fx-radio-button>
                            <fx-radio-button label="standard">${$t('舒适')}</fx-radio-button>
                            <fx-radio-button label="loose">${$t('宽敞')}</fx-radio-button>
                        </fx-radio-group>
                    </div>
                `,
                data() {
                    return {
                        dValue: false,
                        dStyle: ''
                    }
                },
                mounted() {
                    if(me.get('onlyShowRequired')) {
                        this.$nextTick(() => this.dValue = true);
                    }
                    this.dStyle = CRM.getLocal(kk) || 'standard';
                },
                watch: {
                    dValue(v) {
                        $f[v ? 'addClass' : 'removeClass']('crm-field-control-required-show');
                        me.set('onlyShowRequired', v);
                        me.toggleGroup();
                    },
                    dStyle(v, v1) {
                        if(v1) {
                            $f.removeClass(kk + '_' + v1)
                        }
                        $f.addClass(kk + '_' + v);
                        CRM.setLocal(kk, v);
                    }
                }
            })
        },

        //使整个表单处于不可编辑状态/解除表单不可编辑状态
        _toggleEditStatus(opts) {
            if(!opts) {
                this._$disableEdit && (this._$disableEdit.remove(), this._$disableEdit = null);
            } else if(!this._$disableEdit) {
                this._$disableEdit = $('<div style="position:absolute;z-index:10000;background:none;opacity:0;inset:0"></div>');
                this.$el && this.$el.parent().append(this._$disableEdit);
                setTimeout(() => {
                    this._toggleEditStatus();
                }, opts.timer || 10000);
            }
        },

        //锁定主表单数据的编辑
        _lockFieldEdit() {
            this.$el.addClass('crm-lock-fieldedit');
        },

        //解除主表单数据的编辑
        _unlockFieldEdit() {
            this.$el.removeClass('crm-lock-fieldedit');
        },

        reallyRenderComplete() {},

        scrollToField(fieldName) {
            if(!fieldName) return;
            let $target = this.getElementByApiname(fieldName);
            if(!$target.length) return;

            let $scroll = this._getScrollEl();
            if ($scroll.length) {
                $scroll.scrollTop(0);
                $scroll.scrollTop($target.offset().top - $scroll.offset().top - 16);
            } else {
                $target[0].scrollIntoView && $target[0].scrollIntoView({behavior: 'smooth'});
            }
        }
    })

    module.exports = View;
})
