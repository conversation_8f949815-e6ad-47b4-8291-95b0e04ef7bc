/*
 * @Descripttion: 订单条件字段
 * @Author: chaoxin
 * @Date: 2022-08-08 15:10:01
 * @LastEditors: chaoxin
 * @LastEditTime: 2024-07-01 16:06:29
 */

define(function (require, exports, module) {
    const Base = require('crm-modules/action/field/field').components.Base;
    const FilterGroup = require('crm-modules/common/filtergroup/filtergroup');
    const AdvancedExpression = require('./advanced_expression');
    const {CONSTANT, getFilterApiName} = require('crm-modules/action/rebatepolicyobjcommon/rebatepolicyobjcommon');
    const {renderSwitch, renderEntry} = require('./function/function');

    const NFilterGroup = FilterGroup.extend({
        getValue() {
            const value = FilterGroup.prototype.getValue.apply(this, arguments);
            return typeof value === 'string' ? JSON.parse(value) : value;
        }
    })
    const commonFilterOptions = {
        width: 800,
        optionType: 'cascader',
        props: {
            lazy: true,
            checkStrictly: true, // 可选时，expandTrigger只能为click, hover方式会导致最开始默认选中项无法选中
            expandTrigger: 'click'
        },
        parseFields(fields) {
            return Object.keys(fields).reduce((pre, cur) => {
                const {is_active, is_index, api_name} = cur;
                if (is_active && is_index) {
                    pre[api_name] = cur;
                }
                return pre;
            }, {});
        },
        formatGetItem(item, fields) {
            const _object = fields[0];
            item.object_api_name = _object.value;
            item.object_api_name__s = _object.label;
            item.field_name = item.field_name.split('.').slice(1).join('.');
            item.field_name__s = item.field_name__s.split('.').slice(1).join('.');
            // 此字段用来区分普通字段还是聚合字段
            item.field_name_type = 'field';
            item.field_value_type = 'value';
            if (item.object_api_name === 'AggregateRuleObj') {
                item.field_name_type = 'aggregate';
                item.field_value_type = 'aggregate';
                item.agg_value_type = 'aggregate';
            }

            if (item.type === 'percentile') {
                item.field_values__s = item.field_values__s + '%';
            }
            return item;
        },
        parseDefualtData(filter) {
            return [filter.object_api_name].concat(filter.field_name.split('.'));
        },
        filterType: [
            'formula',
            'group',
            'image',
            'file_attachment',
            'master_detail',
            'auto_number',
            'signature',
            'quote',
            'embedded_object_list',
            'multi_level_select_one',
            'tree_path',
            'employee_many',
            'department_many',
            'html_rich_text',
            'object_reference_many'
        ],
        filterCusOptions: {
            showObjectsCascader: true,
        },
        parseCompare(compare, field) {
            // 过滤多选字段 属于/不属于
            return compare.filter(({value}) => ![25, 26].includes(value))
        }
    };

    const COMPONENTS_NAMES = {
        WRAPPER_DOM: 'wrapper_dom_comp',
        RULE_CONDITION: 'rule_condition_comp',
        ORDER_PRO_CONDITION: 'order_pro_condition_comp',
        PRODUCT_CONDITION: 'product_condition_comp',
        FUNCTION_CONDITION: 'function_condition_comp',
    };

    module.exports = Base.extend({
        initialize() {
            Base.prototype.initialize.apply(this, arguments);
            const {rule_type, rebate_basis, calculate_range} = this.get('ruleConfig');
            this.rule_type = rule_type;
            // 返利依据为 从对象 时，增加 产品条件 字段
            this.isRenderProduct = rebate_basis === CONSTANT.REBATE_BASIS.VALUES.detail;
            // 计算范围为 历史 时，增加 单据高级公式 字段
            this.isRenderOrderPro = calculate_range === CONSTANT.CALCULATE_RANGE.VALUES.history;
            // 判断是否为函数
            const function_info = this.getData('function_info') || [];
            this.functionDefaultValue = function_info.find((f) => f.name_space === CONSTANT.FUNCTION_NAMESPACE.VALUES.condition);
            this.showFunction = !!this.functionDefaultValue;

            this.widgets = {};
            this.initDom();
        },

        initDom() {
            this.$el.parents('.f-g-item').addClass('rule-condition');
            this.$el.parents('.f-g-item').siblings('.f-g-tit').addClass('rule-condition').append(`<div class="function-switch"></div>`);
            this.initWrapperDom();
        },

        initWrapperDom() {
            const me = this;
            const wrapperDom = FxUI.create({
                template: `
                    <div>
                        <div class="rule-condition-wrapper" v-show="!showFunction">
                            <div class="order-condition">
                                <div class="title">${$t('crm.rebatepolicyruleobj.keys.rebate_order_condition', null, '单据条件 ')}</div>
                                <div class="order-condition-content"></div>
                            </div>
                            <div class="order-pro-condition" v-if="isRenderOrderPro">
                                <div class="title">${$t('crm.rebatepolicyruleobj.keys.rebate_order_pro_condition', null, '单据高级公式')}</div>
                                <div class="order-pro-condition-content" />
                            </div>
                            <div class="product-condition" v-if="isRenderProduct">
                                <div class="title">${$t('crm.rebatepolicyruleobj.keys.rebate_product_condition', null, '产品条件 ')}</div>
                                <div class="product-condition-content"></div>
                            </div>
                        </div>
                        <div class="function-condition" v-show="showFunction"></div>
                    </div>
                `,
                data() {
                    return {
                        isRenderOrderPro: me.isRenderOrderPro,
                        isRenderProduct: me.isRenderProduct,
                        showFunction: me.showFunction
                    }
                },
                methods: {
                    changeFunctionStatus(showFunction) {
                        me.showFunction = this.showFunction = showFunction;
                        me.hideError();
                    }
                }
            });
            this.$el.append(wrapperDom.$el);
            this.widgets[COMPONENTS_NAMES.WRAPPER_DOM] = wrapperDom;
        },

        render() {
            const defaultValue = this.getData();
            this.renderFilter(this.filterCycleData(defaultValue));
            if (this.isRenderProduct) {
                const defaultValue = this.getData('execution_result')?.condition || {};
                this.renderProductFilter(this.filterCycleData(defaultValue));
            }
            if (this.isRenderOrderPro) {
                const defaultValue = this.getData('rule_condition_pro') || [];
                this.renderProFilter(defaultValue);
            }
            // 函数
            this.renderFunctionOpenedSwitch();
            this.renderFunctionEntry();
        },

        // 函数开关
        renderFunctionOpenedSwitch() {
            const me = this;
            const functionOpenedSwitch = renderSwitch({
                wrapper: $('.f-g-tit > .function-switch')[0],
                value: me.showFunction,
                text: $t('crm.rebatepolicyruleobj.rule_condition.function.switch_txt', null, '使用自定义函数作为条件'),
                helpText: $t('crm.rebatepolicyruleobj.rule_condition.function.switch_help_txt', null, '开启自定义函数条件，将无法使用预设条件')
            });

            functionOpenedSwitch.$on('change', (value) => {
                this.widgets[COMPONENTS_NAMES.WRAPPER_DOM].changeFunctionStatus(value);
            })
        },

        // 函数入口
        renderFunctionEntry() {
            const comp = renderEntry({
                wrapper: this.$el.find('.function-condition')[0],
                value: this.functionDefaultValue,
                text: $t('crm.rebatepolicyruleobj.rule_condition.function.entry_txt', null, '使用函数计算'),
                name_space: [CONSTANT.FUNCTION_NAMESPACE.VALUES.condition]
            });
            comp.$on('change', this.validate.bind(this));
            this.widgets[COMPONENTS_NAMES.FUNCTION_CONDITION] = comp;
        },

        // 过滤每满数据
        filterCycleData(values) {
            if (CRM.util.isEmptyValue(values)) return values;
            return values.map((value) => {
                return {
                    ...value,
                    filters: value.filters.filter((filter) => filter.filter_type !== 'cycle')
                }
            })
        },

        renderFilter(defaultValue = {}) {
            const me = this;
            const {calculate_range, SOURCE_OBJECT_DATA} = this.get('ruleConfig');
            // 历史 常规聚合值
            // 当单 聚合值 订单，同时可设置
            // 规则条件只显示主对象聚合规则
            const {history} = CONSTANT.CALCULATE_RANGE.VALUES;
            const {AGGREGATE_RULE_OBJ} = CONSTANT.OBJECTS_API_NAME.VALUES; const filterOpts = [];
            if (calculate_range === history) {
                filterOpts.push(AGGREGATE_RULE_OBJ);
            } else {
                filterOpts.push(
                    SOURCE_OBJECT_DATA.apiName.master,
                    AGGREGATE_RULE_OBJ
                );
            }
            const objects = this.get('objects')
                .filter((object) => (
                    filterOpts.includes(object.api_name) &&
                    (object.api_name === AGGREGATE_RULE_OBJ ? object.isMasterObject : true)
                ));

            const comp = new NFilterGroup({
                ...commonFilterOptions,
                $wrapper: me.$el.find('.order-condition-content'),
                apiname: 'ProductObj',
                defaultValue,
                objects: JSON.parse(JSON.stringify(objects)),
                filterApiname: getFilterApiName('master', SOURCE_OBJECT_DATA.fieldMapping.master),
                filterCusOptions: {
                    showObjectsCascader: true,
                    extra: {
                        // 点击查看更多追加一个选项
                        onClick(e, nodes, context) {
                            const node = nodes[0]
                            const apiname = 'AggregateRuleObj';

                            require.async('crm-modules/components/pickselfobject/pickselfobject', (Comp) => {
                                const pickComp = new Comp();
                                pickComp.on('select', function(list) {
                                    const _item = {
                                        child: node.data.child || {
                                            type: 'selectone',
                                            options: comp.filters[0]._getCompareOptions({
                                                label: list.name,
                                                type: 'number',
                                                api_name: list._id,
                                                describe_api_name: apiname,
                                                is_active: true,
                                                is_index: true,
                                                _id: list._id,
                                                agg_value_type:list.rule_type || "aggregate"//聚合规则区分组合规则和普通规则
                                            }),
                                        },
                                        describe_api_name: apiname,
                                        hasExtra: true,
                                        extraParams: node.data?.extraParams,
                                        label: list.name,
                                        leaf: true,
                                        type: "number",
                                        value: list._id,
                                    }
                                    let _val = _.clone(node.path);
                                    _val.splice(1, 1, _item.value);
                                    context.updateOptions(_val, _item, context.options);
                                    context.changeValue(_val);
                                });
                                pickComp.render({
                                    apiname: apiname,
                                    api_name: apiname,
                                    filters: node.data?.extraParams?.filters || [],
                                    zIndex: Math.max(FxUI.Utils.getPopupZIndex(), CRM.util.getzIndex()) + 2,
                                });
                                me.widgets[Date.now() + 'pickComp'] = pickComp;
                            })
                        }
                    }
                }
            });
            comp.on('change', function() {
                me.hideError();
            });
            me.widgets[COMPONENTS_NAMES.RULE_CONDITION] = comp;
        },

        // 单据高级条件
        async renderProFilter(defaultValue) {
            const me = this;
            // 比较符与筛选器数值类型保持一致
            const compares = FilterGroup.helper.getCompare('number')
                // 过滤 为空、不为空
                .filter((item) => ![9, 10].includes(item.value))
                .map((item) => ({label: item.name, value: item.value1}));

            const ExpressionComp = await AdvancedExpression.getExpressionCompFile();
            const getExpressionComp = (wrapper, field, data) => {
                const objects = me.get('objects')
                    .filter((object) => object.api_name === 'AggregateRuleObj')
                    .map((object) => AdvancedExpression.parseExpressionDataForSelect(object));
                const opts = {
                    wrapper,
                    field,
                    data,
                    objects,
                    objectDes: {
                        api_name: "SalesOrderObj"
                    },
                    updateParentCompValue: (value) => {
                        me.widgets[COMPONENTS_NAMES.ORDER_PRO_CONDITION].formValueChange(value, field, data)
                    },
                }
                return AdvancedExpression.getExpressionCompInstanceFactory(opts, ExpressionComp);
            }
            const columns = [
                {
                    api_name: 'field_name',
                    type: 'custom',
                    component: getExpressionComp,
                },
                {
                    api_name: 'cus_operator',
                    type: 'select',
                    options: compares,
                    defaultValue: '',
                },
                {
                    api_name: 'field_values',
                    type: 'numInput',
                    defaultValue: '',
                }
            ]

            require.async('crm-modules/buscomponents/action_field/index', (ActionField) => {
                const ReactiveForm = ActionField.C.ReactiveForm.ReactiveForm;
                const expressionCusFields = ['return_type', 'decimal_places', 'default_to_zero'];
                class Comp extends ReactiveForm {
                    // 未输入值
                    isNoInputValue(data) {
                        return data.every((item) => Object.values(_.omit(item, 'rowId')).every((value) => CRM.util.isEmptyValue(value)));
                    }
                    // 取值处理
                    getValue() {
                        if (this.isNoInputValue(this.collect())) return null;
                        const data = super.getValue();
                        return [{
                            connector: 'OR',
                            filters: data.map((d) => ({
                                ...(_.omit(d.field_name, ['default_value', 'default_value__s'])),
                                field_name: d.field_name.default_value,
                                field_name__s: d.field_name.default_value__s,
                                field_name_type: 'CALCULATE_PRO',
                                field_value_type: 'CALCULATE_PRO',
                                type: 'number',
                                operator: d.cus_operator,
                                operator__s: _.find(compares, (c) => c.value === d.cus_operator)?.label,
                                field_values: [d.field_values],
                                field_values__s: d.field_values,
                            }))
                        }]
                    }
                }
                // 默认值处理
                let defaultData = defaultValue?.[0]?.filters || [];
                defaultData = defaultData.map((d) => ({
                    field_name: {
                        default_value: d.field_name,
                        default_value__s: d.field_name__s,
                        ...(_.pick(d, expressionCusFields))
                    },
                    cus_operator: d.operator,
                    field_values: d.field_values[0]
                }));
                me.widgets[COMPONENTS_NAMES.ORDER_PRO_CONDITION] = new Comp(
                    me.$el.find('.order-pro-condition-content'),
                    columns,
                    defaultData,
                    false,
                    null,
                    null,
                    null,
                    true
                )
            })
        },

        renderProductFilter(defaultValue = {}) {
            const me = this;
            const {SOURCE_OBJECT_DATA} = this.get('ruleConfig');
            const objects = this.get('objects').filter((object) => object.api_name === SOURCE_OBJECT_DATA.apiName.detail);

            const comp = new NFilterGroup({
                ...commonFilterOptions,
                $wrapper: me.$el.find('.product-condition-content'),
                apiname: 'ProductObj',
                defaultValue,
                objects: JSON.parse(JSON.stringify(objects)),
                OR_MAX: 1,
                filterApiname: getFilterApiName('detail', SOURCE_OBJECT_DATA.fieldMapping.detail)
            });
            comp.on('change', function() {
                me.hideError();
            });
            me.widgets[COMPONENTS_NAMES.PRODUCT_CONDITION] = comp;
        },

        validate() {
            if (this.showFunction) {
                // 函数校验
                const value = this.widgets[COMPONENTS_NAMES.FUNCTION_CONDITION]?.getValue();
                if (!value) {
                    this.showError(this.$el, $t('crm.rebatepolicyruleobj.rule_condition.validate.function_empty_txt', null, '请选择函数'));
                    return false;
                }
            } else {
                // 组件校验
                const {rebate_basis} = this.get('ruleConfig');

                const rule_condition = this.widgets[COMPONENTS_NAMES.RULE_CONDITION]?.getValue();
                const product_rule_condition = this.widgets[COMPONENTS_NAMES.PRODUCT_CONDITION]?.getValue();
                const rule_condition_pro = this.widgets[COMPONENTS_NAMES.ORDER_PRO_CONDITION]?.getValue();

                if (
                    rebate_basis === CONSTANT.REBATE_BASIS.VALUES.master &&
                    CRM.util.isEmptyValue(rule_condition) &&
                    CRM.util.isEmptyValue(rule_condition_pro)
                ) {
                    // 主对象 单据条件必填
                    const msg = this.isRenderOrderPro ?
                        $t('crm.rebatepolicyruleobj.rule_condition.validate.order_or_pro_condition_empty_txt') :
                        $t('crm.rebatepolicyruleobj.rule_condition.validate.order_condition_empty_txt');

                    this.showError(this.$el, msg);
                    return false;
                } else if (
                    rebate_basis === CONSTANT.REBATE_BASIS.VALUES.detail &&
                    CRM.util.isEmptyValue(product_rule_condition)
                ) {
                    // 从对象 产品条件必填
                    this.showError(this.$el, $t('crm.rebatepolicyruleobj.rule_condition.validate.product_condition_empty_txt', null, '请填写产品条件'));
                    return false;
                }
            }
            this.hideError(this.$el);
            return true;
        },

        getValue() {
            const result = {
                function_condition: null,
                rule_condition: [],
                rule_condition_pro: '',
                product_rule_condition: []
            };
            if (!this.validate()) {
                return result;
            }
            if (this.showFunction) {
                return {
                    ...result,
                    function_condition: this.widgets[COMPONENTS_NAMES.FUNCTION_CONDITION]?.getValue()
                }
            } else {
                const rule_condition = this.widgets[COMPONENTS_NAMES.RULE_CONDITION]?.getValue();
                const product_rule_condition = this.widgets[COMPONENTS_NAMES.PRODUCT_CONDITION]?.getValue();
                const rule_condition_pro = this.widgets[COMPONENTS_NAMES.ORDER_PRO_CONDITION]?.getValue();
                return {
                    ...result,
                    rule_condition,
                    product_rule_condition,
                    rule_condition_pro: rule_condition_pro || ''
                };
            }
        },

        destroy() {
            Object.keys(this.widgets).forEach((key) => {
                this.widgets[key]?.destroy && this.widgets[key]?.destroy();
            })
            Base.prototype.destroy.apply(this, arguments);
        }
    });
});
