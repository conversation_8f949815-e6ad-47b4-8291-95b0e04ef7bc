/*
 * @Descripttion:
 * @Author: chaoxin
 * @Date: 2022-08-01 14:43:29
 * @LastEditors: chaoxin
 * @LastEditTime: 2023-10-18 15:40:42
 */

define(function (require, exports, module) {
    const View = require('crm-modules/action/field/field').View;

    module.exports = View.extend({
        mycomponents: {
            ruleObj: require('../form/ruleobj/ruleobj'),
            account_range: require('../form/account_range/account_range'),
            execute_mode: require('../form/execute_mode'),
            rebate_basis: require('../form/rebate_basis'),
            calculate_range: require('../form/rebate_basis'),
            rebate_dimension: require('../form/rebate_dimension'),
        },

        initView() {
            View.prototype.initView.apply(this, arguments);
            this.handleRuleObjError();
        },

        handleRuleObjError() {
            const comp = this.model.get('forms')['ruleObj'];
            if (comp) {
                comp.on('scrollToError', this.scrollToError.bind(this));
            }
        },

        collect() {
            const data = View.prototype.collect.apply(this, arguments);
            // 数据处理
            // 1. 处理返利产生时机，运行模式字段 execute_mode 包含 execute_mode、cycle_info, cycle_info 包含 cycle_info、cycle_count
            const {execute_mode, cycle_info = {}, specify_create_date} = data?.execute_mode || {};
            const {cycle_count} = cycle_info;
            delete cycle_info?.cycle_count
            const result = {
                ...data,
                execute_mode,
                specify_create_date,
                cycle_info: JSON.stringify(cycle_info),
                cycle_count
            }
            console.log(result)
            return result;
        }
    });
});
