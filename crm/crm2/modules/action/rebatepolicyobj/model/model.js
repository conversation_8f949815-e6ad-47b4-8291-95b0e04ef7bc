/*
 * @Descripttion:
 * @Author: chao<PERSON>
 * @Date: 2022-08-01 14:43:29
 * @LastEditors: chaoxin
 * @LastEditTime: 2024-02-29 14:33:44
 */

define(function (require, exports, module) {
    const Model = require('crm-modules/action/field/field').Model,
        {CONSTANT} = require('crm-modules/action/rebatepolicyobjcommon/rebatepolicyobjcommon');

    module.exports = Model.extend({
        initialize() {
            Model.prototype.initialize.apply(this, arguments);
            this.fetchRuleSourceObjectConfig();
        },

        parse(obj) {
            obj = Model.prototype.parse.apply(this, arguments);

            // 复制时，执行次数字段重置为0
            const isCopy = this.get('isCopy');
            if (isCopy) {
                obj.data.execute_count = '0';
            }

            // 编辑时，控制字段不可编辑
            const isEdit = this.get('isEdit');
            if (isEdit) {
                obj.layout.forEach((ly) => {
                    if (ly.components && ly.components.length) {
                        ly.components.forEach((comp) => {
                            if (['rebate_basis', 'calculate_range'].includes(comp.api_name)) {
                                comp.is_readonly = true;
                            }
                        })
                    }
                })
            }

            obj.layout.forEach((ly) => {
                if (ly.components && ly.components.length) {
                    ly.components = ly.components.filter((comp) => !['specify_create_date', 'cycle_info', 'cycle_count'].includes(comp.api_name));
                    ly.components.forEach((comp) => {
                        if (comp.api_name === 'execute_mode') {
                            comp.full_line = true;
                        }
                    })
                }
            })

            // 从对象获取返利规则字段
            const ruleObj = obj.detailObjectList.map((d) => d.objectDescribe).find((d) => d.api_name === 'RebatePolicyRuleObj');
            if (ruleObj) {
                obj.layout.splice(1, 0, {
                    label: ruleObj.display_name,
                    api_name: 'ruleObj'
                });
            }

            // 处理返利规则从对象
            // 1. 将从对象数据直接规则到data上，清空从对象数据
            // 2. 移除从对象布局
            obj['ruleObj'] = obj.detailObjectList.find((d) => d.objectDescribe.api_name === 'RebatePolicyRuleObj');
            obj.detailObjectList = [];
            obj.layout.pop();

            return obj;
        },

        async getRuleSourceObjectOptions() {
            const {objectDescribe: {fields = {}}} = this.get('ruleObj');
            const options = fields['source_object_api_name']?.options || [];
            const SOURCE_OBJECT_CONFIG = await this.fetchRuleSourceObjectConfig();
            // 未开启从对象的数据
            const noDetailApiNames = SOURCE_OBJECT_CONFIG.filter((c) => !c.detail_api_name).map((c) => c.api_name);
            // 返利依据选择从对象
            const isDetail = this.getData('rebate_basis') === CONSTANT.REBATE_BASIS.VALUES.detail;
            return options.filter((opt) => (isDetail ? !noDetailApiNames.includes(opt.value) : opt));
        },

        fetchRuleSourceObjectConfig() {
            const cache = this.get('SOURCE_OBJECT_CONFIG');
            if (cache) return Promise.resolve(cache);
            return Promise.resolve(CRM.util.getConfigValue('rebate_policy_source')).then((res) => {
                const result = _.isString(res) ? JSON.parse(res) : res;
                this.set('SOURCE_OBJECT_CONFIG', result);
            });
        },

        // 获取规则来源对象的数据: 主从对象、字段映射
        fetchRuleSourceObjectData(apiName) {
            const PRESET_API_NAME = ['SalesOrderObj', 'DeliveryNoteObj'];
            const config = (this.get('SOURCE_OBJECT_CONFIG') || []).find((config) => config.api_name === apiName) || {};
            let fieldMapping = {},
                dFieldMapping = {};
            // 2022-02-10 后台开关可新增任意对象，此处不专门过滤指定对象字段了
            // 仅保留原先 订单、发货单的条件过滤
            if (PRESET_API_NAME.includes(apiName)) {
                fieldMapping = {
                    "coupon_amount":"coupon_amount",
                    "rebate_rule_id":"rebate_rule_id",
                    "lock_status":"lock_status",
                    "new_opportunity_id":"new_opportunity_id",
                    "life_status":"life_status",
                    "rebate_amount":"rebate_amount",
                    "ship_to_tel":"ship_to_tel",
                    "misc_content":"misc_content",
                    "product_rebate_rule_id":"product_rebate_rule_id",
                    "delivery_comment":"delivery_comment",
                    "order_status":"order_status",
                    "account_id":"account_id",
                    "dataId":"_id",
                    "life_status_before_invalid":"life_status_before_invalid",
                    "order_amount":"order_amount",
                    "price_book_id":"price_book_id",
                    "out_owner":"out_owner",
                    "owner_department":"owner_department",
                    "out_resources":"out_resources",
                    "commision_info":"commision_info"
                };
                dFieldMapping = {
                    "price_book_product_id__r":"price_book_product_id__r",
                    "gift_amortize_subtotal":"gift_amortize_subtotal",
                    "coupon_amortize_amount":"coupon_amortize_amount",
                    "price_book_product_id":"price_book_product_id",
                    "rebate_amortize_amount":"rebate_amortize_amount",
                    "misc_content":"misc_content",
                    "product_price":"product_price",
                    "price_book_discount":"price_book_discount",
                    "is_multiple_unit":"is_multiple_unit",
                    "sale_contract_line_id":"sale_contract_line_id",
                    "product_id":"product_id",
                    "owner_department":"owner_department",
                    "out_owner":"out_owner",
                    "price_book_id__r":"price_book_id__r",
                    "owner":"owner",
                    "stat_unit_count":"stat_unit_count",
                    "quantity":"quantity",
                    "lock_status":"lock_status",
                    "last_modified_time":"last_modified_time",
                    "create_time":"create_time",
                    "actual_unit":"actual_unit",
                    "life_status":"life_status",
                    "price_book_price":"price_book_price",
                    "gift_amortize_price":"gift_amortize_price",
                    "last_modified_by":"last_modified_by",
                    "created_by":"created_by",
                    "record_type":"record_type",
                    "other_unit":"other_unit",
                    "price_book_subtotal":"price_book_subtotal",
                    "data_own_department":"data_own_department",
                    "rebate_coupon_id":"rebate_coupon_id",
                    "subtotal":"subtotal",
                    "price_book_id":"price_book_id",
                    "sales_price":"sales_price",
                    "coupon_dynamic_amount":"coupon_dynamic_amount",
                    "conversion_ratio":"conversion_ratio",
                    "policy_subtotal":"policy_subtotal",
                    "amortize_subtotal":"amortize_subtotal",
                    "order_id":"order_id",
                    "rebate_dynamic_amount":"rebate_dynamic_amount"
                }
            }

            const {api_name__r, detail_api_name, detail_api_name__r} = config;
            return {
                label: {
                    master: api_name__r,
                    detail: detail_api_name__r,
                },
                apiName: {
                    master: apiName,
                    detail: detail_api_name
                },
                fieldMapping: {
                    master: fieldMapping,
                    detail: dFieldMapping
                }
            };
        },

        // 获取指定来源对象的返利维度字段
        fetchSourceObjectDimensionFields(apiName) {
            if (!apiName) return Promise.resolve([]);
            return new Promise((resolve, reject) => {
                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/aggregate_rule/service/getAggregateDimension',
                    data: {
                        apiName
                    },
                    success(res) {
                        if (res.Result.StatusCode == 0) {
                            resolve(res.Value && res.Value.dimension);
                            return
                        }
                        resolve([]);
                        CRM.util.alert(res.Result.FailureMessage);
                    }
                })
            })
        },

        submit(data) {
            data.details = {}
            if (data.ruleObj && data.ruleObj.length) {
                data.details['RebatePolicyRuleObj'] = data.ruleObj.map((item, index) => {
                    if (item._id?.includes('rule_')) delete item._id;
                    return {
                        ...item,
                        // 提交数据时，根据数据顺序赋值价格规则优先级（仅在此处赋值）
                        priority: index + 1
                    }
                });
            }
            delete data.ruleObj;

            console.log(data)
            Model.prototype.submit.apply(this, arguments);
        }
    });
});
