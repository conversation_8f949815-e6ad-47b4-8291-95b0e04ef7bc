//处理数据
define(function(require, exports, module) {
	var util = require('crm-modules/common/util'),
		CpUtil = require('./cputil');

	var Model = Backbone.Model.extend({
		defaults: {
			optionsData: [],
			tableData: [],
			opSelected: [],
			originSpecs: {},
			defColumns: [],
			od: {},
			opType: 1,
			tableDataObj: {},
			columnsObj: {},
			filterIds: [],
			filterProductIds: [],
			cacheData: [],
			supplementData: [],
			remberData: [],
			objectData: {},
			allFlag: true,
			singleSpec: false,
			queryInfo: {},
			maxSelects: 0,
			spuName: '', // 用于生成产品名称
			needBackFillRemberData: false, // 是否需要回填已选数据
		},

		initialize: function() {
			//初始化数据
			var specOptins = this.get('selectOpt'),
				opType = this.get('opType'),
				filterIds = this.get('filterIds'),
				filterProductIds = this.get('filterProductIds'),
				remberData = this.get('remberData') || [],
				queryInfo = this.get('queryInfo') || {},
				objectData = this.get('objectData') || {};
			if (opType == 1) {
				var optionsData = [{
					name: $t('wq.weex.path.selecttip'),
					value: ''
				}];
				this.set('maxSelects', specOptins.length);
				specOptins = optionsData.concat(specOptins);
			}
			if (filterIds) {
				this.set('filterIds', filterIds);
			}
			if (filterProductIds) {
				this.set('filterProductIds', filterProductIds);
			}

			if (opType !== 2) {
				this.set('opSelected', []);
			}

			this.set('columnsObj', this.get('columns'));
			this.set('tableDataObj', this.get('orTbData'));
			this.set('od', this.get("od"));
			this.set('opType', opType);
			this.set('spuId', this.get("spuId"));
			this.set('optionsData', specOptins);
			this.set('remberData', remberData);
			this.set('objectData', objectData);
			this.set('queryInfo', queryInfo);
			this.set('singleSpec', this.get('singleSpec') || true);
			this.set('supplementData', []);
			this.set('cacheData', []);

		},

		getInitData: function() {
			var me = this,
				dtd = $.Deferred(),
				spuId = this.get('spuId'),
				opType = this.get('opType'),
				columnsObj = this.get('columnsObj'),
				tableDataObj = this.get('tableDataObj');

			//获取表头
			if (columnsObj.data && columnsObj.data.length >= 1) {
				var defColumns = columnsObj.data;
				me.set('defColumns', defColumns);
			}

			if (opType >= 2) {
				this.getChoosedSpec(spuId, function(data) {
					//获取table数据
					if (tableDataObj.data && tableDataObj.data.length >= 1) {
						var tbData = tableDataObj.data;
						me.supplementSku(tbData.skuList, tbData.specBrief);
						dtd.resolve(data);
					} else {
						me.getSkuDataQueryParams().then(() => {
							me.getSkuData(spuId).done(function(table) {
								if (opType == 2) {
									me.supplementSku(table.skuList, table.specBrief);
									me.trigger('init:complete');
								} else {
									// 选择模式判断是否回填已选数据
									if (opType == 3 && me.get('needBackFillRemberData')) {
										me.backfillRemberData(table.skuList);
									}

									me.set('tableData', table.skuList);
									me.set('cacheData', table.skuList);
								}
								dtd.resolve(data);
							});
						})
					}
					//dtd.resolve(data);
				})
			} else {
				dtd.resolve();
			}
			return dtd;
		},
		//规格值选择or反选
		setSpecValue: function(specId, idx, value, index, cb) {
			var data = this.get('optionsData');
			var targetSpec = {},
				me = this;
			_.find(data, function(item) {
				if (item.value == specId) {
					targetSpec = item;
				}
			})
			this.tableHandle(targetSpec, idx, value, index, function() {
				var $item = targetSpec.options[idx];
				targetSpec.options[idx].status = value;
				me.set('optionsData', data);
				cb && cb();
			});
		},
		//同一规格下规格值全选
		batchChoose: function(specId, value, cb) {
			var data = this.get('optionsData'),
				opSelected = this.get('opSelected'),
				cacheData = this.get('cacheData'),
				curData = [],
				target = {},
				flag = false;

			_.find(data, function(item) {
				if (item.value == specId) {
					_.each(item.options, function(i) {
						i.status = value;
					});
					target = _.extend({}, item);
				}
			});
			if (opSelected.length >= 1) {
				var exist = false;
				_.find(opSelected, function(item, k) {
					if (item.value == specId) {
						exist = true;
						if (value !== '0') {
							item.options = target.options
						} else {
							opSelected.splice(k, 1);
							flag = opSelected.length == 0 ? true : false;
						}
					}
				})
				if (!exist && value) {
					opSelected.push(target);
				}
			} else {
				if (value) {
					opSelected.push(target);
				}
			}

			curData = CpUtil.filterCpList(cacheData, opSelected);
			this.set('optionsData', data);
			this.set('optionsSelect', opSelected);
			this.set('tableData', curData);
			this.set('allFlag', flag);
		},


		//规格选项删除——删除table的一条column
		delSpec: function(index) {
			let opSelected = this.get('opSelected'),
				key = opSelected[index].name,
				newData = [];
			opSelected.splice(index, 1);
			if (opSelected.length >= 1) {
				newData =CRM.util.createCpList(opSelected);

			}
			//编辑有规格商品，删除规格值时需要特殊处理
			if (this.get("opType") == 2) {
				let tableData = this.get('tableData'),
					supplementData = this.get('supplementData'),
					supplementDataCache=[],
					tableOldData = _.filter(tableData, t => t._id);
				newData.forEach((d, nIdx) => {
					const keyArr = Object.keys(d);
					//判断是否为历史产品
					let oIdx=this._findItemInArray(tableOldData,keyArr,d);
					if(oIdx||oIdx==0){
						d = _.extend(d, tableOldData[oIdx]);
						tableOldData.splice(oIdx, 1)
						delete d[key];
					}else{
						//判断是否为补充产品
						let sIdx=this._findItemInArray(supplementData,keyArr,d);
						if(sIdx||sIdx==0){
							let sItem = _.extend({},d, supplementData[sIdx])
							delete sItem[key];
							supplementDataCache.push(sItem);
							newData.splice(nIdx, 1);
							supplementData.splice(sIdx,1);
						}
					}
				})
				this.set('supplementData', supplementDataCache);
			}

			//删除optionsSelect的值——触发rendertable
			this.set('opSelected', opSelected);
			this.set('tableData', newData);

		},

		_findItemInArray:function(arr,keys,data){
			if (arr.length<=0) return null;
			let idx=null;
			_.find(arr, (a, i) => {
				let flag = keys.every(k => {
					return data[k] == a[k]
				})
				if (flag) {
					idx=i;
					return
				}
			})
			return idx;

		},
		//规格选项改变值
		changeSpec: function(specArr, cb) {
			var me = this,
				tableData = this.get('tableData'),
				opSelected = this.get('opSelected'),
				discardSpec = {};
			_.each(opSelected, function(op, i) {
				var result = $.inArray(op.value, specArr);
				if (result < 0) {
					me.delSpec(i);
					cb && cb();
				}
			})
		},

		//增减规格值联动笛卡尔集表格
		tableHandle: function(spec, idx, value, index, callback) {
			var me = this,
				opType = this.get('opType');

			var item = {
				spec_name: spec.name,
				spec_id: spec.value,
				spec_value_name: spec.options[idx].name,
				spec_value_id: spec.options[idx].value
			};
			//新增规格值
			if (value == "2" && opType <= 2) {
				this.addOptionsValue(item, function() {
					me.opHandle(item, value, index);
					callback && callback();
				});
			} else {
				if (opType >= 3) { //筛选先处理options，再根据处理后的opSelected生成table
					me.opHandle(item, value, index);
					me.chooseSku(item, value);
				} else { //新建编辑先处理table，再处理options
					me.delOptionsValue(item);
					me.opHandle(item, value, index);
				}
				callback && callback();
			}
		},

		//新建编辑——增加规格值
		addOptionsValue: function(option, cb) {
			var tableData = this.get('tableData'),
				supplementData = this.get('supplementData'),
				opSelected = this.get('opSelected'),
				opType = this.get('opType'),
				key = option.spec_id,
				isNewOption = CpUtil.isNew(opSelected, key);

			if (isNewOption) { //新增规格—列数据
				tableData = tableData.length == 0 ? [{}] : tableData;
				_.each(tableData, function(item) {
					CpUtil.addProp(item, option.spec_name, option.spec_value_id);
				})
				//对隐藏的前端补全产品也要新增规格
				_.each(supplementData, function(item) {
					CpUtil.addProp(item, option.spec_name, option.spec_value_id);
				})
				cb && cb();
			} else { //增加规格值-行数据
				var opFilter = opSelected.filter(function(i) {
						return i.value !== key;
					}),
					item = {
						name: option.spec_name,
						value: option.spec_id,
						options: [{
							"name": option.spec_value_name,
							"value": option.spec_value_id
						}]
					};
				opFilter.push(item);

				var temp = CRM.util.createCpList(opFilter),

					validTabler = _.filter(tableData, function(t) {
						return !t.is_removed;
					}),
					tableLen = validTabler.length,
					tempLen = temp.length;

				if (tableLen + tempLen >= 401) {
					util.alert($t('产品明细不能超过400条'));
					return
				} else {
					tableData = tableData.concat(temp);
					cb && cb();
				}
			}
			this.set('tableData', tableData);
		},

		//新建编辑——删除规格值
		delOptionsValue: function(option) {
			var tableData = this.get('tableData'),
				supplementData = this.get('supplementData'),
				opSelected = this.get('opSelected'),
				prop = option.spec_name,
				value = option.spec_value_id,
				isLastOption = CpUtil.isLast(opSelected, option.spec_id);

			if (!isLastOption || opSelected.length === 1) {
				tableData = _.filter(tableData, function(data) {
					return !(data.hasOwnProperty(prop) && data[prop] == value) && !(data.hasOwnProperty(`_${prop}`) && data[`_${prop}`] == value)
				});
			}

			if (isLastOption) { //删除规格值后，该规格没有值——删除列数据
				_.each(tableData, function(data) {
					delete data[prop];
				});
			}

			this.set('tableData', tableData);
		},

		//选产品
		chooseSku: function(option, value) {
			var flag = this.get('allFlag'),
				tableData = this.get('tableData'),
				cacheData = this.get('cacheData'),
				opSelected = this.get('opSelected'),
				name = option.spec_name,
				valueId = option.spec_value_id,
				curData = [];
			if (flag) {
				curData = _.filter(cacheData, function(cd) {
					return cd[name] == valueId;
				});
			} else {
				curData = CpUtil.filterCpList(cacheData, opSelected);
			}
			flag = false;
			if (opSelected.length == 0) {
				flag = true;
			}
			this.set('tableData', curData);
			this.set('allFlag', flag);
		},

		//所选规格&规格值
		opHandle: function(item, value, index) {
			var key = item.spec_id,
				opSelected = this.get('opSelected'),
				isNewOption = CpUtil.isNew(opSelected, key),
				isLastOption = CpUtil.isLast(opSelected, key);

			if (value == '0') {
				if (isLastOption) {
					_.find(opSelected, function(op, index) {
						if (op.value == key) {
							opSelected.splice(index, 1);
						}
					})
				} else {
					_.find(opSelected, function(op) {
						if (op.value == key) {
							op.options = _.filter(op.options, function(v) {
								return v.value !== item.spec_value_id
							});
						}
					})
				}
			} else {
				var specItem = {
					name: item.spec_value_name,
					value: item.spec_value_id
				};
				if (isNewOption) { //新增规格&规格值
					var opItem = {
						name: item.spec_name,
						value: item.spec_id,
						options: [specItem]
					};
					opSelected.splice(index, 0, opItem);
				} else { //只添加规格值
					_.find(opSelected, function(op) {
						if (op.value == key) {
							op.options.push(specItem);
							return
						}
					})
				}
			}
			this.set('opSelected', opSelected);
		},

		//获取某一规格的规格值
		getSpecValue: function(id) {
			let me = this,
				optionsData = this.get('optionsData');
			return new Promise((resolve, reject) => {
				let sqi = {
					"limit": 2000,
					"offset": 0,
					"filters": [{
						"field_name": "status",
						"field_values": ["1"],
						"operator": "EQ"
					}]
				};
				
				util.FHHApi({
					url: '/EM1HNCRM/API/v1/object/SpecificationValueObj/controller/RelatedList',
					data: {
						associate_object_data_id: id,
						associate_object_describe_api_name: "SpecificationObj",
						associated_object_describe_api_name: "SpecificationValueObj",
						associated_object_field_related_list_name: "target_related_list_specificationvalue",
						include_associated: true,
						search_query_info: JSON.stringify(sqi)
					},
					success: function(res) {
						if (res.Result.StatusCode == 0) {
							var data = res.Value.dataList;
							var specValues = _.map(data, function(item) {
								return {
									name: item.name,
									value: item._id,
									status: false,
									spec_id: item.specification_id
								}
							});
							_.find(optionsData, function(item) {
								if (item.value == id) {
									item.options = specValues;
								}
							});
							resolve(specValues);
							me.set('optionsData', optionsData);
							return
						}
						util.alert(res.Result.FailureMessage);
					}
				}, {
					errorAlertModel: 1
				});
			})
		},

		getSkuColumns: function() {
			var defSkuColumns = this.get('defColumns');
			//过滤商品必填&产品不可单独编辑信息
			var selfColumns = _.filter(defSkuColumns, function(a) {
				var apiName = a.api_name;
				if (apiName == 'standard_price') {
					a.api_name = 'price'
				}
				return apiName !== 'spu_id' && apiName !== 'category' && apiName !== 'product_category_id' && apiName !== 'unit' && apiName !== 'product_line' && apiName !== 'product_spec' && apiName !== 'batch_sn';
			})
			selfColumns = _.map(selfColumns, function(b) {
				return _.extend(b, {
					data: b.api_name,
					title: b.label,
					range: [b.length, b.decimal_places],
					returnType: b.return_type,
					isRequired: b.is_required,
					isEdit: b.api_name === 'name' || !b.is_readonly,
					dataType: b.render_type,
					noSupportBatchEdit:true, //不支持列批量编辑
					options: _.map(b.options, function(c) {
						return _.extend({
							IsSysItem: c.not_usable,
							ItemCode: c.value,
							Children: c.child_options,
							isCustom: c.value == 'other',
							customRequired: c.is_required
						}, c);
					})
				})
			});
			return selfColumns;
		},

		supplementSku: function(tableData, specBrief) {
			var me = this,
				spuId = this.get('spuId'),
				opSelected = this.get('opSelected'),
				supplementData = this.get('supplementData'),
				totalSku = CRM.util.createCpList(opSelected);

			//补充数据
			if (specBrief.length !== totalSku.length) {
				var specBrief_s = JSON.stringify(specBrief);

				totalSku.forEach(function(ts, i) {
					var ts_s = JSON.stringify(ts),
						index_s = specBrief_s.indexOf(ts_s);

					if (index_s < 0) {
						ts.is_removed = true;
						ts.is_supplement = true;
						supplementData.push(ts);
						return
					}
				});
			}

			var visibleData = [],
				invisibleData = [];
			_.each(tableData, function(td) {
				if (td.data_right_flag == 'invisible' || td.is_deleted) {
					invisibleData.push(td)
				} else {
					visibleData.push(td)
				}
			})
			me.set('tableData', visibleData);
			me.set('cacheData', invisibleData); //缓存不可见但需提交的数据
			me.set('supplementData', supplementData); //前端补全的sku数据

		},

		//编辑——获取商品已选sku的规格&规格值
		getChoosedSpec: function(id, cb) {
			var me = this,
				opType = me.get('opType'),
				filterIds = me.get('filterProductIds'),
				dtd = $.Deferred();
			var objectData = me.get('objectData'),
				ispb = objectData ? objectData.pricebook_open : false;
			util.FHHApi({
				url: '/EM1HNCRM/API/v1/object/spu_sku_object/service/search_spec_and_values_by_spu',
				data: {
					"spu_id": id, //  SPU的主键
					"is_include_all": opType >= 3 ? false : true, // 是否包含所有规格值的标记。如果是true，返回规格下所有规格值，用status区分是否是使用的。如果传入false,则值给返回所有状态是0的规格值。
					"price_book_id": ispb ? objectData.pricebook_id : "", //价目表的ID
					"sku_ids_to_filter": filterIds, //用于基于用户已经选择过的sku列表过滤掉规格&规格值
					"is_add_pro_to_price": opType == 4 ? true : false  //价目表添加产品
				},
				success: function(res) {
					if (res.Result.StatusCode == 0) {
						var data = res.Value.dataList,
							opSelected = [],
							opSelectedObj = {};

						var optionsData = _.map(data, function(d) {

							var options = [],
								selectOp = [];

							_.each(d.specValueList, function(s) {
								s.status = opType >= 3 ? '1' : s.status;
								var item = _.extend({
									"name": s.specValueName,
									"value": s.specValueId,
								}, s)
								if (!(s.status == '0' && !s.active)) {
									options.push(item);
								}
								if (s.status == '1') {
									selectOp.push(item);
								}
							});

							if (selectOp.length >= 1) {
								opSelected.push({
									"name": d.specName,
									"value": d.specId,
									"options": selectOp
								})
								opSelectedObj[d.specId] = selectOp
							}
							return {
								"name": d.specName,
								"value": d.specId,
								"options": options
							}
						});

						var prasedData = {
							opSelected: opSelected,
							optionsData: optionsData
						};
						if (opType >= 3) {
							me.set('optionsData', opSelected);
						} else {
							me.set('optionsData', optionsData);
							me.set('opSelected', opSelected);
							me.set('originSpecs', opSelectedObj);
						}

						dtd.resolve(prasedData);
						cb && cb(prasedData);
					} else {
						util.alert(res.Result.FailureMessage);
					}
				}
			}, {
				errorAlertModel: 1
			});
			return dtd;
		},


		//编辑——获取已有sku数据
		getSkuData: function(id) {
			var me = this,
				filterIds = me.get('filterIds'),
				opType = me.get('opType'),
				pdQueryParam = JSON.parse(me.get('pdQueryParam')?.search_query_info || '""'),
				dtd = $.Deferred(),
				getSkuDataAfter=this.get('getSkuDataAfter');
				
			var queryInfo = me.get('queryInfo');

			var sq = {
				"limit": 10000,
				"offset": 0,
				"filters": [],
				"wheres": queryInfo.wheres || [],
				"orders": pdQueryParam.orders || []
			};
			if (filterIds.length >= 1) {
				sq.filters.push({
					field_name: '_id',
					field_values: filterIds,
					operator: 'NIN'
				})
			}
			var objectData = me.get('objectData'),
				ispb = objectData ? objectData.pricebook_open : false;

			objectData.is_spec = true;

			//编辑获取全数据权限
			if (opType == 2) {
				objectData.under_spu_skus_edit = true;
			}
			var get_apiname = ispb ? "PriceBookProductObj" : "ProductObj";

			util.FHHApi({
				url: '/EM1HNCRM/API/v1/object/' + get_apiname + '/controller/RelatedList',
				data: {
					associate_object_data_id: id,
					associate_object_describe_api_name: "SPUObj",
					associated_object_describe_api_name: get_apiname,
					associated_object_field_related_list_name: "spu_sku_list",
					include_associated: true,
					search_query_info: JSON.stringify(sq),
					object_data: objectData,
					master_data: me.get('masterData'),
					details: me.get('details'),
				},
				success: async function(res) {
					if (res.Result.StatusCode == 0) {

						//格式化table数据
						var skuList = res.Value.dataList;
						var specBrief = [];
						if(typeof getSkuDataAfter === 'function'){
							skuList = await getSkuDataAfter(skuList);
						}
						_.map(skuList, function (d, index) {
							var item = {},
								specNames = [],
								compositionName,
								spec_and_value = d.spec_and_value;
							if (ispb) {
								spec_and_value = d.product_id__ro.spec_and_value;
							}
							_.each(spec_and_value, function(spec) {
								item[spec.spec_name] = spec.spec_value_id;
								specNames.push(spec.spec_value_name);
							});
							compositionName = `${d.spu_id__r}[${specNames.join('-')}]`;

							specBrief.push(item);
							return _.extend(d, item, {nameIsFrozen: opType <= 2 && compositionName !== d.name});
						})
						var dataList = {
							specBrief: specBrief,
							skuList: skuList
						};
						dtd.resolve(dataList);
					} else {
						util.alert(res.Result.FailureMessage);
					}
				}
			}, {
				errorAlertModel: 1
			});
			return dtd;
		},

		// 获取当前产品对象的查询数据的参数
		// !!临时处理，该组件目前逻辑是先获取的数据然后才初始化的产品对象表格，获取数据时还拿不到表格处理的查询参数(场景排序字段)，这里通过实例化一个表格拿到了处理后查询参数
		getSkuDataQueryParams() {
			const me = this;
			return new Promise((resolve) => {
				require.async('crm-modules/components/objecttable/objecttable', (oTable) => {

					const objectData = me.get('objectData'),
						apiname = !!objectData.pricebook_open  ? "PriceBookProductObj" : "ProductObj";

					const nOTable = oTable.extend({
						initComplete: function () {
							const params = this.getParams();
							me.set('pdQueryParam', params);
							resolve();
							table?.destroy();
							table = null;
						},
						getParams: function() {
							const params = this.table.getParam();
							return this.parseParam(params);
						}
					})
					let table = new nOTable({
						apiname,
						doStatic: true,
					})

					table.render();
				})
				
			})
		},

		/*
		 * 批量编辑上下架
		 */
		batchEdit: function(key, isUp, cb) {
			var me = this,
				tableData = me.get('tableData');
			_.each(key, function(i) {
				tableData[i].product_status = (isUp ? 1 : 2);
			})
			cb && cb();
		},
		/*
		 * 移除数据——价格设置为0，状态置为下架
		 */
		removeHandle: function(data, index, cb) {
			var me = this,
				tableData = me.get('tableData'),
				supplementData = me.get('supplementData'),
				tableLen = tableData.length,
				sIdx = index - tableLen,
				targetData = {};

			if (index >= tableLen) { //数据为前端补全部分
				targetData = supplementData[sIdx];
			} else {
				targetData = tableData[index];
			}
			targetData.is_removed = targetData.is_removed ? false : true;
			if (targetData.is_supplement) {
				delete targetData["is_supplement"];
				tableData.push(targetData);
				supplementData.splice(sIdx, 1);
			}
			if (data._id) {
				targetData.status_flag = targetData.is_removed ? 3 : 1; //3作废，1编辑
			} else {
				targetData.price = targetData.is_removed ? (targetData.price || 0) : (targetData.price || '');
			}
			me.set('tableData', tableData);
			cb && cb();
		},
		//新建——获取默认全部规格&规格值
		getAllSpec: function() {
			let me = this,
				optionsData = this.get('optionsData');
			return new Promise((resolve, reject) => {
				let sqi = {
					"limit": 2000,
					"offset": 0,
					"filters": [{
						"field_name": "status",
						"field_values": ["1"],
						"operator": "EQ"
					}]
				};
				util.FHHApi({
					url: '/EM1HNCRM/API/v1/object/SpecificationObj/controller/List',
					data: {
						object_describe_api_name: 'SpecificationObj',
						search_query_info: JSON.stringify(sqi),
					},
					success: function(res) {
						if (res.Result.StatusCode == 0) {
							var data = res.Value.dataList;
							var specOptins = _.map(data, function(item) {
								return {
									'value': item._id,
									'name': item.name
								}
							});
							let arr = [{
								name: $t('请选择'),
								value: ''
							}];
							_.each(specOptins, (r) => {
								let flag = _.findWhere(optionsData, {
									value: r.value
								});
								if (!flag) {
									arr.push(r)
								}
							})
							me.set('maxSelects', arr.length - 1);
							optionsData = [].concat(optionsData, arr);
							me.set('optionsData', optionsData);
							resolve(arr);
							return
						}
						util.alert(res.Result.FailureMessage);
					}
				}, {
					errorAlertModel: 1
				});
			})
		},
		//转义字符串
		_parseValue: function(data) {
			_.each(data, (d) => {
				d.name = CRM.util.enCodeValue(d.name) + ' ';
			})
			return data;
		},
		// 回填已填数据
		backfillRemberData(list) {
			const remberData = this.get('remberData');

			if (remberData.length !== 0) {
				const len = list.length;
				// 将数据转换成map
				let map = {};
				remberData.forEach(item => {
					map[item._id] = item;
				});
				
				// 回填之前已经选择的数据
				for (let i = len - 1; i >= 0; i--) {
					const id = list[i]._id;
					if (map[id]) {
						list.splice(i, 1, map[id]);
					}
				}
			}
		}
	})
	module.exports = Model;
})
