/**
 * @desc: 选配bom
 * @author: wang<PERSON><PERSON>
 * @date: 2020-03-08
 */

define(function (require, exports, module) {
	var util = CRM.util;
	var subset = require('../subset/index');
	var DraggableContainer = require('crm-modules/common/draggablecontainer/draggablecontainer');

	module.exports = Backbone.View.extend({

		options: {
			title: $t('配置'),
			apiname: 'BOMObj',
			recordtype: 'default__c',
			zIndex: 200,
			idKey: 'rowId',
			// 必传参数
			rootId: '', 				// 产品bom根结点的产品id；开启bomcore之后可以不传
			bom_core_id: null,         	// bomcore对象_id
			bom_type: null,            	// bom类型
			bom_version: null,         	// bom版本
			bom_core_name: null,        // bom name
			masterData: {}, 			// 主对象数据
			object_data: {}, 			// 从对象基本信息
			details: {},				// 当前从对象数据

			// 非必要
			source_api_name:'',			// 来自哪个从对象
			priceBookId: '', 			// 价目表id
			originalTotalMoney: 0, 		// 默认总金额；取自价目表售价，或产品价格；
			extendData: null, 			// 需要替换的数据；
			isFrom: null, 				// 来自哪里; 'md',
			apinameFrom: '', 			// 从哪个对象进的 产品/价目表明细,
			accountId: '', 				// 客户id
			partnerId: '', 				// 合作伙伴id
			mcCurrency: '',				// 币种
			isHideTotalMoney: false,	// 是否隐藏总金额
			decimal_places: 2,			// 调整后金额小数位；
			mapFields: null,			// 映射字段 Object
			btnName: {					// 按钮名称替换
				save: $t("确定"),
				cancel: $t("取消")
			},
			constraintPriceBookPriority: false,	// 强制走强制优先级
			extendRootData: {},  				// 根节点替换数据
			parseColumn_after: null,  			// fn 处理列勾子
			dialogEnter_after: null,  			// fn 点确定回填勾子
			hidePriceBookSelect: false,  	 	// 隐藏主对象切换价目表
			attribute: {},						// 产品包属性
			parseParam_addTemp: null,  			// fn 添加临时子件，处理请求入参勾子
			disabledFields: [],        			// 设置某些字段只读
			renderLeftHook: null,        		// fn 左侧区域渲染扩展
			doSearchAfterHook: null,     		// fn 搜索后勾子
			doCloseBomAfterHook: null,     		// fn 展开收起后勾子
			onlyShowSelectAfterHook: null,     	// fn 查看已选后勾子
			cellChangeAfterHook: null,        	// 表格编辑后事件
			cellChangeBeforeHook: null,      	// 表格编辑前事件
			initDataEndHook: null,       		// 初始化数据结束勾子
			changeNsAttrHook: null,       		// 编辑非标属性勾子
			styleType: 1,						// 交互类型，默认1，还有2,3

			selfElement:'',						// 自定义容器
			ruleElement:'',						// 约束规则容器
			totalMoneyElement:'',				// 总金额自定义容器
			rootAttrElement:'',					// 根节点属性容器
			synchronizedChildrenAttr: false,	// 根节点属性、非标属性修改，同步子件属性修改
			hideRootAttribute: false,			// 不显示根节点属性
			changeHeightHook: null,				// 更改表格高度
			totalMoneyChangeHook: null,			// 总金额变化勾子
			hideChildren: false,				// 默认不展示子件-模式 3
			renderAfterHook: null,				// 框架初始化完勾子
			noChangeBomVersion: false,			// 不允许切换版本
			hideChildrenAttr: false,			// 隐藏子件属性

			hideParentsCheckBox: false,			// 隐藏父级复选框

			root_pricing_period: 1,				// 母件期数
			isSupportPeriod: false,				// 是否支持周期性

			checkboxClickEndHook: null,			// fn 复选框点击勾子
			trClickEndHook: null,				// fn 行点击勾子
			trHoverHook: null,					// fn 行hover勾子

			search_rich_text_extra: false,		// 搜索富文本勾子
			notShowSelectAllBtn: false,			// 不显示全选按钮
			hideIconColumn: false,				// 不显示icon列

			changeAttrAfterHook: null,			// fn 切换属性勾子
			changeAttrConstraintHook: null,		// fn 切换属性级联勾子

		},

		currentRule: [],

		events: {
			// 'hover .j-addTemporaryChildren': 'tdHoverHandle',
			// 'mouseleave .j-addTemporaryChildren': 'hideBtns',
		},

		initialize() {
			this.apiname = this.options.apiname;
			this.recordtype = this.options.recordtype;
			this.totalMoney = 0; 					// 最新修改后的总金额；
			this.defSelectMondy = 0; 				// 默认选中的总金额；
			this._defSelectSingleSetMoney = 0; 		// 默认选中的总单套金额；
			this._originalTotalSingleSetPrice = 0; 	// 根节点单套金额；
			this.treeData = this.columns = null;
			this.defSelectRow = []; 				// 默认选中行数据；
			this._cacheSelectedByQuote = []; 		// 报价器命中选中行数据；
			this.pricision = 2; 					// 调整后总金额精度；
			this.extendBomDataPIds = []; 			// 替换的数据rowId；
			this.newPriceBookProductData = null; 	// 取价服务返回的价目表明细数据
			this._searchInfo = {};					// 搜索信息
			this._cacheCalculateAmount = [];		// 缓存约束数量规则
			this._priceBookPriority = CRM._cache.priceBookPriority || this.options.constraintPriceBookPriority; // 是否走强制优先级逻辑
			this._priceFlowPricebookChildNotSelected = CRM.util.isGrayScale('CRM_BOM_FLOW_PRICEBOOK_EXTEND'); // 下单选配时所选子件不在母件价目表时不显示
			if (CRM.util.isGrayScale('CRM_BOM_PRICE_BOOK_DISABLED')) this.options.disabledFields?.push('price_book_id');
			this._isOpenQuoter = this.isOpenQuoter();
			this.render();
			this.sendLog();
		},


		render() {
			if (!this.options.rootId) return;
			let isCloseBom = CRM.getLocal('isCloseBom'); // 是否收起子节点按钮
			if (isCloseBom === null) CRM.setLocal('isCloseBom', true);
			this.renderDialog();
			this.initData();
			this.addEvents();
		},

		resetTable(opt){
			this.options = Object.assign(this.options, opt);
			this.initData();
		},


		// 子件单独定价
		individualPricingForChildren(){
			return this.rootData && this.rootData.sale_strategy === 'sub';
		},

		// 不允许切换版本；二次配置不允许切换
		_noChangeVersion(){
			return this.options.isFrom === 'twiceConfig' || this.options.noChangeBomVersion;
		},

		// 模式3
		_isStyle3(){
			return this.options.styleType == 3 || ['1', '2'].includes(util.getConfigStatusByKey('cpq_ui_mode'));
		},

		// 不显示子件属性
		_hideChildrenAttr(){
			// return false;
			return this._isStyle3() || this.options.hideChildrenAttr;
		},

		addContainer(el){
			if(!el?.length) return;
			if(this.options.rootAttrElement){
				this.options.rootAttrElement.append(`<div class="dialog_attribute"></div>`);
			}
			this.$el = el;
			el.append(`<div class="dialog_selectbom">
								<div class="dialog_selectbom_left" ></div>
								<div class="dialog_selectbom_right" ></div>
							</div>`);
			if(this._isStyle3()){
				let mw = Math.ceil(el.width() * 0.8);
				// let mw = el.width() - 885;
				this.initDraggableContainer(el.find('.dialog_selectbom_left'), mw, el.width());
				// this.$el.find('.draggable-container__main').prepend('<div class="rootAttrBox"></div>');
				this.$el.find('.draggable-container__main').prepend('<div class="attrRangeBox"></div>');
				this.$el.find('.draggable-container__main').prepend('<div class="priceQuoterBox"></div>');
				this.$el.find('.draggable-container__main').prepend('<div class="selectbom_extendLeftBox"></div>');

			}
			this.addChangeVersion();
			this.options.renderAfterHook && this.options.renderAfterHook({$el: this.$el, $el_left: this.$el.find('.selectbom_extendLeftBox'), api: this._getApi()});

		},

		addChangeVersion(){
			if(!this.dialog) return;
			let disabledClass = this._noChangeVersion() ? 'versionDisabled' : '';
			this.dialog.element.find('.dialog-tit').append(`<div class="dialog_selectbom_versionBox ${disabledClass}"></div>`);
		},

		// 更新版本
		updateVersion() {
			if(!this.dialog) return;
			let version = this.dialog.element.find('.dialog_selectbom_versionBox');
			version.html($t('切换') + $t('版本') + '：' + this.options.bom_version + ` <span class="fx-icon-switch"></span>`);
		},

		// 拖拽
		initDraggableContainer(el, maxWidth, fullWidth){
			this.dragContainer = new DraggableContainer({
				el,
				showCloseBtn: true,
				id: 'bom_left',
				maxWidth,
				width: 400,
				isFull: !!this.options.hideChildren,
				fullWidth
			})
			this.dragContainer.on('toggle', _.debounce(e => {
				console.log('drag')
				this.treeTable.resize();
				this.displayAttribute();
				// this.treeTable.refresh();

			}, 200));
			this.dragContainer.render();
		},

		// 增加入口来源埋点
		sendLog(){
			CRM.util.sendLog('crm', 'bom', {
				operationId: 'crmBomConfig',
				eventData:{
					from: this.options.isFrom || 'other',
					source_api_name: this.options.source_api_name || '',
				}
			});
		},

		async initData() {
			this._singleClose = util.getConfigStatusByKey('bom_single_leaf_node_closed')  === '1';
			if(this._isStyle3()){
				this.options.synchronizedChildrenAttr = true;
			}

			//进配置页，拉取默认子件lingj
			let res = await this._fetchBomChildren(this.options.rootId);
			if(res) this.beforeRenderTable(res);
		},

		async _fetchBomChildren(productId = '', param = {}, isChildBom) {
			// let extendDataBomCoreId = this.getExtendDataBomCoreId();
			let filter = this.getFilter();

			let p = Object.assign({}, {
				bom_core_id: this.options.bom_core_id, // 或_params.bom_core_id
				price_book_id: this.options.priceBookId,
				child_search_query_info: '{"limit":2000,"offset":0,"filters":[{"field_name":"enabled_status","field_values":true,"operator":"EQ"}]}',
				account_id: this.options.accountId,
				partner_id: this.options.partnerId || '',
				bom_list: this.getOldBomData(),
				include_constraint: true,
				mc_currency: this.options.mcCurrency || '',
				details: this.options.details,
				object_data: this.options.masterData,
				filter_empty_groups: true,
				bom_id: this.options?.rowData?.bom_id,
				extraData: this.options?.extraData,     //报价器参数
				search_rich_text_extra: !!this.options?.search_rich_text_extra,
				// extendBomCoreId: extendDataBomCoreId,
				...filter
			}, param);
			let res = await util.fetchBomAndRelatedBomData(
				[productId], p, isChildBom
			);
			if(!res){
				this.alertMsg($t('sfa.crm.bom.fetchError')) // 未查到产品组合数据
			}
			return res;
		},

		// 收集二次配置数据中的 bomcoreid，补充到请求里，支持 bomcore 不指定版本
		// getExtendDataBomCoreId(){
		// 	let r = [];
		// 	let {related_core_id, bom_id, new_bom_path} = this.getFieldMap();
		// 	if(this.options.extendData){
		// 		util.forEachTreeData(this.options.extendData, item => {
		// 			if(item[related_core_id]){
		// 				r.push({
		// 					bom_core_id: item[related_core_id],
		// 					new_bom_id: item[bom_id],
		// 					new_bom_path: item[new_bom_path],
		// 				})
		// 			}
		// 		})
		// 	}
		// 	return r;
		// },

		getFilter() {
			return {};
		},
		// 请求bom数据
		// fetchBomData(_params = {}) {
		// 	let _this = this;
		// 	let filter = this.getFilter();
		//     return new Promise((resolve, reject) => {
		//         util.fetchBomChildren(
		//             [this.options.rootId],
		//             false,
		//             function (res) {
		//                 resolve(res);
		//             },
		//             Object.assign({
		//                 bom_core_id: _this.options.bom_core_id, // 或_params.bom_core_id
		//                 price_book_id: _this.options.priceBookId,
		//                 child_search_query_info: '{"limit":2000,"offset":0,"filters":[{"field_name":"enabled_status","field_values":true,"operator":"EQ"}]}',
		//                 account_id: _this.options.accountId,
		//                 partner_id: _this.options.partnerId || '',
		//                 bom_list: _this.getOldBomData(),
		//                 include_constraint: true,
		//                 mc_currency: _this.options.mcCurrency || '',
		//                 details: _this.options.details,
		//                 object_data: _this.options.masterData,
		//                 filter_empty_groups: true,
		//             }, filter, _params)
		//         );
		//     })
		// },

		// 从对象传过来的产品包明细数据，过滤出有价目表id的子节点，传给server，server需要用重新查一次价目表明细信息；
		getOldBomData: function () {
			if (this.options.extendData && this.options.extendData.length) {
				const oData = util.parseTreeToNormal(this.options.extendData, true, true);
				let {node_type, price_mode, bom_id, attribute_json, price_book_id, related_core_id, new_bom_path} = this.getFieldMap();
				let mapFields = {
					'node_type': node_type,
					'price_mode': price_mode + '__v',
					'bom_id': bom_id,
					'attribute_json': attribute_json,
					'price_book_id': price_book_id,
					'related_core_id': related_core_id,
					'new_bom_path': new_bom_path,
				};
				oData.forEach(item => {
					for(let key in mapFields){
						let f = mapFields[key] || key;
						item[key] = item[f];
					}
				});
				const allowKeys = ['_id', 'node_type', 'price_mode', 'bom_id', 'attribute_json', 'price_book_id', 'related_core_id', 'new_bom_path'];

				return oData.filter((item) => item.price_book_id || this.isTempNode(item) || item.related_core_id)
					.map((item) => (this.isTempNode(item) ? item : _.pick(item, allowKeys)));
			}
			return [];
		},

		addEvents() {
			$('body').delegate('.trInsertHandle', 'click', (e) => {
				let rowId = $(e.target).data('rowid');
				let rowData = this.treeTable.getRowDataById(rowId);
				this.trInsertHandle(rowData);
			});
			$('body').delegate('.addChildrenHandle', 'click', (e) => {
				let rowId = $(e.target).data('rowid');
				let rowData = this.treeTable.getRowDataById(rowId);
				this.addChildrenHandle(rowData);
			});
			$('body').delegate('.aplCalculateHandle', 'click', (e) => {
				let rowId = $(e.target).data('rowid');
				let rowData = this.treeTable.getRowDataById(rowId);
				this.aplCalculateHandle(rowData);
			});

			// 收起母件属性
			$('body').delegate('.rootAttrBoxForQuoter_bottomLine', 'click', (e) => {
				this.$el.find('.rootAttrBoxForQuoter').toggleClass('rootAttrClosed')
			});

			$('body').on('click', () => {
				this.hideBtns()
			});

			// 切换版本
			$('body').delegate('.dialog_selectbom_versionBox', 'click', (e) => {
				if($(e.currentTarget).hasClass('versionDisabled')) return;
				this.changeVersion();
			});
		},

		isShowPriceBook: function () {
			return CRM._cache.openPriceList && this.options.accountId && !this.options.hidePriceBookSelect;
		},

		beforeRenderTable: function (res) {
			let _this = this;
			// 获取是否开启属性产品
			CRM.util.getProductWithAttr().then(async attrStatus => {
				// 开了价目表；从产品添加，需要先走取价服务，查到对应报价单明细；默认总金额取该明细的价格；
				// 如果开了强制优先级，也要走取价服务，取优先级最高的价目表，不用明细传过来的价目表；
				// 如果开了属性，要走取价服务
				// 开固定搭配，走取价服务
				await this.getRealPriceForQuote(res);  // 开了报价器，需要取初始价格
				if (
					(this.isShowPriceBook() && this.options.apinameFrom === "ProductObj") ||
					_this._priceBookPriority ||
					attrStatus ||
					CRM._cache.fixedCollocationOpenStatus // 固定搭配开关
				) {
					this.getRootProductFromService().then(data => {
						_this.newPriceBookProductData = data[0];
						_this.renderTreeTable(res);
					})
				} else {
					_this.renderTreeTable(res);
				}
			})
		},

		_hasApl(){
			return this._treeRes.dataMapList.find(item =>  item.describeApiName === "AssignAplFunctionList");
		},

		parseColumn() {
			let _this = this;
			this.hideSomeFields();
			this.addColumns();
			let hasApl = this._hasApl();
			let isOTC = this.isOpenTemporaryChildren();
			let w1 = hasApl ? 104 : 80;
			let w2 = hasApl ? 94 : 70;
			let width = isOTC ? w1 : w2;
			if(CRM._cache.openAttribute && this._hideChildrenAttr()){
				width += 34;
			}
			let rootBomtype = this.options.bom_type;
			this.columns.push({
				data: null,
				width: width,
				title: $t('操作'),
				lastFixed: true,
				render: function (data, type, row) {
					if (!row.isGroup) {
						let attrBtn = '<a data-id="' + row.rowId + '" data-action="showAttrHandle"  href="javascript:;" class="j-showAttrHandle" style="margin-right:8px">'+ $t('属性') + '</a>';
						let r = '';
						 r += '<a data-id="' + row._id + '"  tb-action-type="showdetail" href="javascript:;" class="j-detail " >' + $t("查看") + '</a>';
						// 属性按钮
						if(_this._hideChildrenAttr() && _this._hasAttr(row)){
							r += '<a data-id="' + row.rowId + '" data-action="showAttrHandle"  href="javascript:;" class="j-showAttrHandle" style="margin-right:8px">'+ $t('属性') + '</a>';
						}

						// 父为标准bom不可以操作子bom
						let isShowMore = !(rootBomtype === 'standard' || row.a_node_bom_core_type === 'standard');
						// apl 计算 按钮
						if(hasApl && isShowMore){
							r += '<a data-id="' + row.rowId + '" data-action="aplCalculateHandle"  href="javascript:;" class="j-aplCalculateHandle" >'+'APL' + '</a>';
						}


						if (isShowMore && isOTC && !_this.isTempNode(row) && !row.__isNotFlowRootPricebook) {
							r += '<a data-id="' + row.rowId + '" data-action="showMoreBtns"  href="javascript:;"  class="j-addTemporaryChildren " >' + $t('更多') + '</a>'
						}
						if (_this.isTempNode(row)) {
							r = '';
							// 属性按钮
							if(_this._hideChildrenAttr() && _this._hasAttr(row)){
								r += attrBtn;
							}
							r += '<a data-id="' + row.rowId + '" data-action="delTemporaryChildren"  href="javascript:;" class="j-delTemporaryChildren" >' + $t("删除") + '</a>';
						}
						if(_this.options.addBtnHook){
							let r2 = _this.options.addBtnHook(row);
							r += r2;
						}
						return r;
					}
				}
			});
			this.options.parseColumn_after && this.options.parseColumn_after(this.columns);
		},

		// 开了周期性产品
		isOpenIssue(){
			// return util.getConfigStatusByKey('periodic_product') === '1';
			return this.options.isSupportPeriod;
		},

		/**
		 * @desc 添加修改调整价格列
		 */
		addColumns: function () {
			var _this = this;
			var index = _this.columns.length;
			while (index--) {
				var item = _this.columns[index];
				if (this.isOpenIssue() && item.data === 'modified_adjust_price') {
					var obj = $.extend(true, {}, item);
					obj.title = $t('sfa.crm.price_per_set'); // 单套价格
					obj.api_name = 'price_per_set';
					obj.data = 'price_per_set';
					obj._id = '';
					obj.__isEdit = false;
					_this.columns.splice(index + 1, 0, obj);
				}

				// 期数
				if (item.data === 'pricing_period') {
					if(this.isOpenIssue()){
						item.isEdit = true;
					}else{
						item.isHidden = true;
					}
				}

				// 切换 bom 版本
				if (item.data === 'node_bom_core_version') {
					item.isEdit = true;
					item.disabledDel = true;  // 不允许删除
				}
			}
			if(!this.options.hideIconColumn){
				this.columns.unshift({
					api_name: 'icon',
					data: null,
					width: 36,
					fixed: true,
					fixedIndex: 0,
					title: ' ',
					render: function (data, type, full) {
						let html = '';
						//添加临时子件标记
						if (_this.isOpenTemporaryChildren() && _this.isTempNode(full)) {
							html += `<span class="icon-tempNode" title="${$t('临')}">${$t('临')}</span>`;
						}
						// 添加bom图标
						if (full.is_package) {
							html += '<a data-id="' + full._id + '"   href="javascript:;" class="j-bao" ><span class="package-icon"></span></a>';
						}
						return html;
					}
				});
			}

			// 添加固定列 属性
			if(_this._hideChildrenAttr()){
				this.columns.push({
					api_name: 'attributeHtml',
					data: 'attributeHtml',
					width: 200,
					// lastFixed: true,
					// fixedIndex: 0,
					title: $t('属性'),
					render: function (data, type, full) {
						let t = _this.getSelectedAttr(full);
						return `<span title="${t}">${t}</span>`;
					}
				});
			}


			_this._setFieldDisabled(this.columns);
		},

		getSelectedAttr(row){
			let a = util.getSelectAttrText(row, false).attrT;
			let b = util.getNsAttrText(row).attrT;
			return a + b;
		},

		// 自定义，设置某些字段只读
		_setFieldDisabled(columns) {
			if (this.options.disabledFields?.length) {
				columns.forEach(item => {
					if (item.data && this.options.disabledFields.includes(item.data)) {
						item.isEdit = false;
					}
				})
			}
		},

		/**
		 * @desc 产品让隐藏掉一些字段；
		 */
		hideSomeFields: function () {
			var hideFields = ['is_required', 'selected_by_default', 'price_editable', 'amount_editable'];
			_.each(this.columns, function (item) {
				if (_.contains(hideFields, item.data)) {
					item.isHidden = true;
				}
			})
		},

		beforeParseDataHook() {

		},

		// 获取根节点单套价格
		getRootSingleSetPrice(){
			return util.multiplicational(this.options.originalTotalMoney, this.options.root_pricing_period || 1);
		},

		// 处理bom数据；添加编辑字段，组装数据
		parseData: function (data, res) {
			var _this = this;

			var productData = _.find(data, function (item) {
				return item.describeApiName === 'ProductObj'
			});

			var PriceBookProductObj = _.find(data, function (item) {
				return item.describeApiName === 'PriceBookProductObj'
			});

			let proInfo = _.find(productData.dataList, item => {
				return item._id === _this.options.rootId;
			});

			// 老约束
			var cstInfo = _.find(data, function (item) {
				return item.describeApiName === 'ProductConstraintLinesObj'
			});

			var cst = cstInfo ? cstInfo.dataList : [];

			// 缓存属性约束条件
			let attrConstraintInfo = _.find(data, function (item) {
				return item.describeApiName === 'BomAttributeConstraintLinesObj'
			});
			let attrAplFunctionList = _.find(data, function (item) {
				return item.describeApiName === 'AplFunctionList'
			});
			let AssignAplFunctionList = _.find(data, function (item) {
				return item.describeApiName === 'AssignAplFunctionList'
			});
			let TriggerFormulaMap = _.find(data, function (item) {         // 高级公式计算相关
				return item.describeApiName === 'TriggerFormulaMap'
			});

			this._attrConstraintRule = attrConstraintInfo ? attrConstraintInfo.dataList : [];
			this._attrAplFunctionList = attrAplFunctionList ? attrAplFunctionList.dataList : [];
			this._attrAssignAplFunctionList = AssignAplFunctionList ? AssignAplFunctionList.dataList : [];
			this._attrFormulaMap = TriggerFormulaMap ? TriggerFormulaMap.dataList : [];
            console.log('约束规则================================');
			console.log(this._attrConstraintRule, this._attrAplFunctionList, this._attrAssignAplFunctionList);

			this.options.originalTotalMoney = proInfo.price;

			if (!this.options.bom_core_id) this.options.bom_core_id = proInfo.core_id;

			// 如果有价目表明细信息，取价目表售价；
			if (PriceBookProductObj) {
				proInfo = _.find(PriceBookProductObj.dataList, item => {
					return item.product_id === _this.options.rootId;
				});
				this._rootPriceBookData = proInfo;
				this.options.originalTotalMoney = proInfo.pricebook_sellingprice;
			}

			if (this.newPriceBookProductData) this.options.originalTotalMoney = this.newPriceBookProductData.selling_price;

			this.rootData = productData.dataList[0];
			this.rootData.product_id = this.rootData._id;
			this.initRootData();
			this.hideAttrRoot();
			this.extendRootData(this.rootData, this.options.extendRootData);
			console.log('rootData', this.rootData);
			this._cacheAllAttribute(res);
			this.beforeParseDataHook(data, res);
			if (this.options.extendData) this.extendTempData(res, this.options.extendData, this.rootData);
			this.initBomData(res);
			this.removeInvalidData(res);
			this.addConstraint(res, cst);
			this.addAttrConstraintMark([this.rootData].concat(res), this._attrConstraintRule);
			if (CRM._cache.openAttribute) util.setDefaultAttr(res);

			res = util.parseDataToBOM(res, this.rootDataBomId);
			res = util.sortTreeData(res, 'order_field');
			this.setCheckedForQuoter(res);
			util.setBomChecked(res);
			this.changeTempRowId(res);

			// 保存默认选中项和默认选中总金额；
			this.calDefSelectMoney(res);
			_this.saveDefSelectRow(res);

			// 二次编辑，替换数据
			if (_this.options.extendData) _this.extendBomData(res, _this.options.extendData);

			util.setChildrenAmount(res, {
				baseAmount: '__amount',
				amount: 'amount',
				amount_any:'amount_any',
				related_core_id: 'related_core_id',
			}, function (val, row, parentBom) {
				row.amount = val;
			});

			// 执行老约束
			if (cst.length) {
				let cloneData = util.cloneObjArr(res);

				// 执行约束条件；如果失败，则返回clone数据；
				let isSuc = util.todoBomConstraint(res, res);

				if (!isSuc.status) {
					util.alert(isSuc.msg);
					return cloneData;
				}
			}
			this.options.initDataEndHook && this.options.initDataEndHook({
				data: res,
				api: this._getApi(),
			});
			return res;
		},

		// 获取字段描述
		_getDescribe(field){
			let des = this.treeTable.getDescribe() || {};
			return des[field];
		},

		calDefSelectMoney(data) {
			if (this.isIncludeDefSelect()) {
				this.defSelectMondy = util.calculateSelectMoney(data, '__adjust_price', true);
				if(this.isOpenIssue()) this._defSelectSingleSetMoney = util.calculateSelectMoney(data, '__price_per_set', true, true);
			}
		},

		// 更换临时子件的rowId;
		changeTempRowId(data = []) {
			if (!this.isOpenTemporaryChildren()) return;
			util.forEachTreeData(data, item => {
				if (!item.isGroup && this.isTempNode(item)) item.rowId = item._oldRowId;
			})
		},

		initRootData() {
			this.rootData._isRoot = true;
			this.rootData.isChecked = true;
			this.rootData.product_id__r = this.rootData.name;
			this.rootDataBomId = this.rootData.bom_id;
			util.addRowId(this.rootData);
			if (this.newPriceBookProductData){
				this.rootData.pricebook_id = this.newPriceBookProductData.pricebook_id;
			}
		},

		// 没有报价器，隐藏根节点属性
		hideAttrRoot(){
			if(this._isStyle3()) {
				this._getRootAttrElement().hide();
			}
		},

		// 替换根节点当前选中属性;
		extendRootData(rootData, extendRootData) {
			if (!extendRootData) return;
			let fieldMapping = this.getFieldMap();

			const attrProps = ["attribute_json", "nonstandard_attribute_json", "selectedAttr", "nsAttr", 'pricing_period', ];
			attrProps.forEach(key => {
				let v = fieldMapping[key] ? extendRootData[fieldMapping[key]] : extendRootData[key];
				if (v) {
					rootData[key] = _.isObject(v) ? this.cloneObj(v) : v;
				}
			})
		},

		// 一次性产品
		_isOne(item){
			if(item.product_id__ro){
				return item.product_id__ro?.pricing_mode && item.product_id__ro?.pricing_mode === 'one'
			}
			return item.pricing_mode && item.pricing_mode === 'one'
		},

		// 周期性产品 cycle
		_isCycle(item){
			if(item.product_id__ro){
				return item.product_id__ro?.pricing_mode && item.product_id__ro?.pricing_mode === 'cycle'
			}
			return item.pricing_mode && item.pricing_mode === 'cycle';
		},

		// 整期售卖
		_isWholeSale(item){
			if(item.product_id__ro){
				return item.product_id__ro?.whole_period_sale && item.product_id__ro?.whole_period_sale === 'yes'
			}
			return item.whole_period_sale && item.whole_period_sale === 'yes'
		},

		// 设置行字段编辑
		initEditFields(data, allData){
			var _this = this;
			var editFields = [];
			var columns = this.columns;
			// 缓存复用bom数据
			// 设置价格、数量可编辑
			_.each(columns, function (item) {
				if (item.isEdit) editFields.push(item.data)
			});
			data = Array.isArray(data) ? data : [data];
			allData = allData || this.treeData;

			function _findChildren(d){
				let status = false;
				util.forEachTreeData(allData, item => {
					if(item.parent_bom_id && item.parent_bom_id === d.bom_id){
						status = true
					}
				});
				return status
			}

			_.each(data, function (item) {
				if (!item.isGroup) {
					// 处理每行的 价格和数量是否可编辑
					var copyEditFields = $.extend(true, [], editFields);
					if (!item.amount_editable) util.deleteArrChildren(copyEditFields, 'amount');
					if (!item.price_editable) util.deleteArrChildren(copyEditFields, 'modified_adjust_price');

					// 一次性产品，禁止修改期数
					if (_this._isOne(item)) util.deleteArrChildren(copyEditFields, "pricing_period");

					// 周期性产品，且有子件的，禁止修改调整金额
					if (_this._isCycle(item)) {
						let hasChildren = _findChildren(item);
						if(hasChildren) util.deleteArrChildren(copyEditFields, "modified_adjust_price");
					}

					// 复用 bom 且没指定版本的才可以切换 版本 字段
					if(!item.is_package || item.related_core_id) util.deleteArrChildren(copyEditFields, 'node_bom_core_version');

					// 1. 定价模式是配置价格
					// 2. 开启强制优先级
					// 3. 灰度下单选配时所选子件不在母件价目表时不显示
					// 4. 父节点bom类型是标准bom
					// 价目表不可编辑；
					if (
						item.price_mode == '1' ||
						_this._priceBookPriority ||
						_this._priceFlowPricebookChildNotSelected ||
						item.a_node_bom_core_type === 'standard'
					) util.deleteArrChildren(copyEditFields, 'price_book_id');
					// 如果是配置价格，且还有价目表的，清空价目表；
					if (item.price_mode == '1' && item.price_book_id) {
						item.price_book_id = item.price_book_id__r = item.price_book_product_id = item.price_book_product_id__r = null;
					}
					item.isEditField = copyEditFields;
				} else {
					item.isEditField = [];
				}
			});
		},

		// 初始化bom数据；是否必选，字段是否可编辑
		initBomData: function (res) {
			var _this = this;
			const pbId = _this.newPriceBookProductData?.pricebook_id || _this.options.priceBookId || '';
			// 缓存复用bom数据
			var _cacheRelatedBomData = util.keyBy(res.filter(item => item.related_core_id), 'related_core_id');

			_.each(res, function (item) {
				if (_this.isTempNode(item)) {
					// item.bom_id = item.bom_id__r = null;
				} else {
					item.bom_id = item._id;
					item.bom_id__r = item.name;
				}
				if (!item.isGroup) {
					if(!item.hasOwnProperty('__amount')) item.__amount = item.amount;
					// 子件价目表与母件不一致
					item.__isNotFlowRootPricebook = (
						_this._priceFlowPricebookChildNotSelected &&
						item.price_mode === '2' &&
						item.price_book_id !== pbId
					);
					// 必选 | 子件价目表与母件不一致并且不是默认选中的
					item.disabled = item.is_required || (item.__isNotFlowRootPricebook && !item.selected_by_default);
					item.modified_adjust_price = item.adjust_price;
					// 报价器命中的数据，需要更改默认价格；
					if (item.hasOwnProperty('_oldAdjustPrice')) item.adjust_price = item._oldAdjustPrice;
					item.__adjust_price = item.adjust_price;
					item.pricing_period  = item.pricing_period || 1;
					item.__pricing_period = item.pricing_period;
					item.pricing_mode = item.pricing_mode || item.product_id__ro?.pricing_mode;

					item.__price_per_set = util.multiplicational(item.__adjust_price, item.__pricing_period);
					item.price_per_set = util.formatDecimalPlace(item.__price_per_set, this.pricision);
					// 给子件添加父节点的bom类型
					if (item.core_id) {
						let p_data = item.core_id === _this.options.bom_core_id && _this.options.bom_type ? {node_bom_core_type: _this.options.bom_type} : _cacheRelatedBomData[item.core_id];
						item.a_node_bom_core_type = p_data?.node_bom_core_type;
					}
				}
			});
			this.initEditFields(res, res);
		},

		// 有约束条件的子节点，添加约束条件信息；
		// 找出每个子节点所有必须选择和不能选择的
		addConstraint: function (bomData, cstList) {
			if (!cstList.length) return;
			let uniqData = _.uniq(cstList, item => item.up_bom_id); // 约束节点去重后的条件；
			let len = uniqData.length;
			while (len--) {
				let item = uniqData[len];
				let allDw = util.findAllConstraint(item.up_bom_id, cstList, true);
				if (!allDw) {
					util.alert($t('配置的约束关系成环了'));
					return;
				}
				let d = _.find(bomData, bd => bd._id === item.up_bom_id);
				if (d) {
					d.mustSelect = allDw.mustSelect;
					d.cannotSelect = allDw.cannotSelect;
				}
			}
		},

		// 添加属性约束规则标记，没标记的就不用执行属性约束了；
		addAttrConstraintMark(bomData = [], rule = []) {
			if (!rule.length) return;
			rule.forEach(item => {
				item.condition_range.forEach(c => {
					let rowData = util.getDataByKey(c.bom_id, bomData, 'bom_id', true);
					if (rowData) {
						rowData.forEach(c => {
							c._attrConMark = true;
						})
					}
				})
			})
		},


		getTotalPriceElement(){
			return this.options.totalMoneyElement || this.dialog?.element;
		},

		// 展开收起，联动搜索
		dropDownClick(rowData, allData, status){
			let {key, data} = this._searchInfo || {};
			if (status && key) {
				let data = [rowData];
				this.filterDataBySearchKey(data);
				this.refreshRowIsShow(data);
			}
		},

		renderTreeTable(res) {
			let _this = this;
			this._treeRes = res;
			let el = _this.$el.find('.dialog_selectbom_right');
			this.treeTable && this.treeTable.destroy();
			require.async('../object_treetable/object_treetable', function (TreeTable) {
				let proto = TreeTable.prototype;
				let table = new TreeTable({
					el: el,
					apiname: 'BOMObj',
					// columns: _this.columns,
					doStatic: true,
					listType: 'selected',
					pickAttribute: CRM._cache.openAttribute, //表格是否展开选择属性
					notShowAttribute: _this._hideChildrenAttr()
				});
				table.getOptions = _.bind(function () {
					var opts = proto.getOptions.call(this);
					opts.searchTerm = opts.showFilerBtn = opts.showMoreBtn = null;
					opts.recordtype = _this.options.recodetype;
					opts.isPreObjBatchEdit = true;
					//opts.showRequiredTip = !__layoutRule || __layoutRule.isEmpty();// 没有布局规则的时候显示星号必填提示
					opts.beforeEditFn = function (opts, next) {
						return _this.beforeEditTableCellHandle(opts, table, next);
					};
					opts.getIsOpenOnlySelected = _this._isOpenOnlyShowSelected.bind(_this);
					opts.trHeight = CRM._cache.openAttribute ? 40 : 42;
                    opts.showGroupToolTipIcon = true;
					return _this.parseTableOptions(opts, el, this);
				}, table);

				table.beforeSetAttributes = _this.beforeSetAttributes.bind(_this);
				table.recordtype = _this.options.recodetype;
				table.apiname = _this.options.apiname;
				table.render();
				_this.treeTable = table;
				table.checkboxclickHandle = _this.checkboxClick.bind(_this);
				table.cellChangeHandle = _this.cellChange.bind(_this);
				table.showGroupDetail = _this.showGroupDetail.bind(_this);
				table.searchChangeHandle = _this.doSearch.bind(_this);
				table.checkboxDisabled = _this.checkboxDisabled.bind(_this);
				table.checkboxClickBefore = _this.checkboxClickBefore.bind(_this);
				table.trclickHandle = _this.trclickHandle.bind(_this);
				table.scrollingAfter = _this.scrollingAfter.bind(_this);
				table.dropDownClick = _this.dropDownClick.bind(_this);
				table.trHoverHook = _this.trHover.bind(_this);
				table.notAllowCheck = function () {
					util.alert($t('该分组最多只能选一个'))
				};
				table.on('attrValueChange', _this.attrValueChange.bind(_this));
				table.on('attrValueChange.before', _this.attrValueChangeBefore.bind(_this));
				table.on('nsAttribute.blur.before', _this.nsAttributeBlurBefore.bind(_this));
				table.on('nsAttribute.blur', _this.nsAttributeBlur.bind(_this));
				table.on('showdetail', _this.showdetail.bind(_this));
				table.on('column.lockChange', _this.columnLockChange.bind(_this));
				table.on('delTemporaryChildren', _this.delTemporaryChildren.bind(_this));
				table.on('checkbox.click.intercept', _this.checkBoxClickIntercept.bind(_this));
				table.on('setChildrenShowType.after', _this.setChildrenShowTypeAfter.bind(_this));
                table.onToolTipMouseOverHandle = function(e, field, rowData, toolTipInstance) {
                    if (field === 'product_id') {
                        let {group_options_control, min_prod_count, max_prod_count} = rowData;
                        let msg = min_prod_count > 0 ? `，${$t('crm.select_bom_group_tip_single_one', null, '必须选择1种')}` : '';
                        let minMsg = min_prod_count ? `，${$t('crm.select_bom_group_tip_multiple_min', {count: min_prod_count}, '最少选择{{count}}种')}` : '';
                        let maxMsg = max_prod_count ? `，${$t('crm.select_bom_group_tip_multiple_max', {count: max_prod_count}, '最多选择{{count}}种')}` : '';
                        let groupToolTip = group_options_control 
                        ? $t('crm.select_bom_group_tip_single', { msg }, '该分组下产品为单选{{msg}}')
                        : $t('crm.select_bom_group_tip_multiple', { minMsg, maxMsg }, '该分组下产品为多选{{minMsg}}{{maxMsg}}');
                        toolTipInstance.show(e, groupToolTip)
                    }
                }
				table.tableComplete = _this.tableComplete.bind(_this, res, table);
			})
		},

		trHover($trs, rowId){
			this.options.trHoverHook && this.options.trHoverHook({$trs, rowId, api: this._getApi()});
		},

		checkBoxClickIntercept: $.noop,
		tableCompleteHook: $.noop,

		renderData(table) {
			table.doStaticData(this.treeData);
		},

		tableComplete: async function (res, table) {
			let _this = this;
			await this.tableCompleteHook(res);
			let dataList = util.flatBomData(res.dataMapList);
			_this.treeData = _this.parseData(res.dataMapList, dataList);
			_this.initTotalMoney();
			_this.setDataIsShow(_this.treeData);
			// if(_this.hasHistoryData()) _this.onlyShowSelectedRow(_this.treeData, false);
			this.renderData(table);
			_this.renderLeft();
			// 有替换数据处理
			if (_this.options.extendData) _this.updateExtendDataPrice(_this.extendBomDataPIds);
			_this.initDefSelectRowPrice();
			_this.calculateTotalMoney();
			_this.addPriceBookBox();
			_this.addCloseBomBtn();
			// _this.addFilterChildrenBtn();
			_this.addSelectAllBtn();
			_this.addFilterSelected();
			_this.afterDoStaticDataHook(res);
			//属性展开
			if (CRM._cache.openAttribute || CRM._cache.openNsAttribute) {
				util.initDataSelectedAttrByJson(_this.rootData);
				_this.renderParentAttribute();
				_this.initConstraint();

			}
			if (CRM._cache.openAttribute) {
				setTimeout(async function () {
					_this.displayAttribute2(true);
					_this._fromRender = true;
					await _this.executeBomAttrConstraint();
					await _this.calculateAllFormula();
				}, 100)
			}
			_this.addConstraintBtn();
			_this.addHeaderAplBtn();
			_this.updateVersion(_this.rootData)

		},

		// 渲染左侧区域
		renderLeft(){
			if(this.options.renderLeftHook && this.dialog){
				this.options.renderLeftHook({
					$el: this.dialog.element.find('.dialog_selectbom_left'),
					data: this.treeData,
					rootProductId: this.options.rootId,
					rootCoreId: this.options.bom_core_id,
					api: this._getApi()
				})
			}
		},

		afterDoStaticDataHook: $.noop,

		addHeaderAplBtn(){
			if(this._hasApl()){
				let me = this;
				let el = this.options.ruleElement || this.dialog.element.find('.dialog-tit');
				this.bomAplBtn && this.bomAplBtn.destroy && this.bomAplBtn.destroy();
				el.find('.bom_aplBtn').remove();
				el.append(`<div class="bom_aplBtn"></div>`);

				const AplBtn = FxUI.create({
					wrapper: el.find('.bom_aplBtn')[0],
					template: `<fx-button :size="size"  @click="handlerClick">${$t('执行') + 'APL'}</fx-button>`,
					data() {
						return {
							size: 'micro',
						}
					},
					methods: {
						handlerClick() {
							me.aplCalculateHandle(me.rootData);
						}
					}
				});
				this.bomAplBtn = AplBtn.$children[0];
			}
		},

		// 添加查看约束规则按钮
		addConstraintBtn() {
            if (!this._attrConstraintRule.length && !this._attrAplFunctionList.length && !this._attrAssignAplFunctionList.length) {
                return;
                
            }
            let el = this.options.ruleElement || this.dialog.element.find('.dialog-tit');
            el.append(`<span class="ruleBtn showConstraintRule">${$t('查看约束关系')}</span>`);
            $('body').delegate('.showConstraintRule', 'click', (e) => {
                this.showConstraintRule()
            })
		},
		// 展示约束规则
		showConstraintRule() {
			let ruleList = _.uniq(this._attrConstraintRule, item => item._id); // 去除重复规则
			require.async('../constraintrule/constraintrule', (ConRule) => {
				this.CR = new ConRule({
					ruleList: ruleList,
                    aplRuleList: this._attrAplFunctionList,
                    assignAplRuleList: this._attrAssignAplFunctionList
				});
			})
		},

		columnLockChange() {
			this.displayAttribute();
		},

		beforeSetAttributes: function (attr) {
			this.columns = attr.columns;
			this.parseColumn();
			// 设置价格、数量可编辑
			_.each(this.columns, function (item) {
				var arr = ['modified_adjust_price', 'amount', 'price_book_id'];
				if (arr.includes(item.data)) {
					item.isEdit = true;
				}
			});
			this.pricision = this.getTotalMoneyPrecision();
			return attr
		},

		// 属性点击之前
		attrValueChangeBefore() {
			this.updateCloneData();
		},

		getSearchConfig() {
			return {
				// highFieldName: f,
				// placeHolder: $t("产品名称"),
				showFilterField: true,
				type: "Keyword",
				// fieldName: f,
			};
		},

		//格式化表格配置
		parseTableOptions: function (opts, el, table) {
			var _this = this;
			return _.extend({}, opts, {
				// height: el.height() - 146,
				single: false,
				showMultiple: true,
				arrowResetSingle: true,
				showRequiredTip: true,
				isOrderBy_allColumn: false,
				isAddPlusSign: true,
				sizeType: 'md',
				showSize: false,
				showPage: false,
				isShowAllChecked: false,
				showGroupCheckbox: false,
				showFilerBtn: false,
				checkedSpecial: true,
				cacheSearchField: true,
				checked: {
					idKey: _this.options.idKey,
					data: []
				},
				search: this.getSearchConfig(),
				beforeEditFill: function (all, fn) {
					_this.beforeEditFill(all, fn)
				},
				filterColumns: _this.getSearchField() || null,

				hideParentsCheckBox: this.options.hideParentsCheckBox,
			})
		},

		getSearchField() {
			let columns = this.columns.filter(item => !item.isHidden);
			this.addIsFilter(columns);
			return this.filterUnSupportedFilterColumn(columns);
		},

		// 过滤掉BOM不支持筛选的列；
		filterUnSupportedFilterColumn: function (columns) {
			let filterColumns = ['spu_id', 'owner', 'created_by', 'owner_department', 'data_own_department', 'out_owner', 'last_modified_by', 'relevant_team'];
			let res = [];
			_.each(columns, function (item) {
				if (!filterColumns.includes(item.data)) res.push(item);
			});
			return res;
		},

		addIsFilter: function (columns) {
			_.each(columns, function (item) {
				item.isFilter = true
			})
		},

		// 复选框禁止点击；
		checkboxDisabled: function (table, data) {
			if (data.__isNotFlowRootPricebook) {
				// 优先级高于必填
				util.alert($t('产品不在母件选择的价目表中，如需添加该产品，需将其维护至选择的价目表'))
			} else if (data && data.is_required) {
				util.alert($t('当前产品为必选，选择此产品父级后默认选中当前产品'))
			} else if (data.__isConstraintSetting) {
				this.alertForConstraint(data);
				// util.alert($t('该产品是被约束产品，因此不能操作'))
			} else {
				util.alert($t('该产品的价格不为0，且父级产品价格不可编辑，因此不能勾选'))
			}
		},

		// 约束关系提示，必须选择的产品；
		alertForConstraint(data){
			let msg = '';
			this.currentRule.forEach(rule => {		
				let f = rule.result_range.find(item => item.bom_id === data.bom_id);
				if(f){
					if(rule.constraint_type === '1'){
						msg += $t('sfa.crm.bom.checkConstraint1', {
							A: rule.condition_range.map(item => item.product_name).join(),
							B: f.product_name,
						}, '已设置约束关系：选【{{A}}】必须选择【{{B}}】，产品【{{A}}】已选择，产品【{{B}}】必须选择');
						msg += '\n';
					}else if(rule.constraint_type === '2'){
						msg += $t('sfa.crm.bom.checkConstraint2', {
							A: rule.condition_range.map(item => item.product_name).join(),
							B: f.product_name,
						}, '已设置约束关系：选【{{A}}】不允许选择【{{B}}】，产品【{{A}}】已选择，所以产品【{{B}}】置灰不可选择');
						msg += '\n';
					}
				}
			})
			if(msg) util.alert(msg);
		},

		// 校验数量小数位是否越界；
		__validAmountDP(newNum){
			let dp = this._getDescribe('amount')?.decimal_places;
			let decNum = String(newNum).split('.')[1]?.length || 0;
			return decNum <= dp;
		},

		beforeEditFill: function (all, fn, tabId) {
			let res = true;
			let field = all.$tr.context.getAttribute('data-name');
			let rowData = all.cellData;
			// var rowId = all.$tr[0].getAttribute('data-rowid');
			// 编辑数量时，需要校验
			if (field === 'amount') {
				let newNum = all.data;
				// 如果改的复用 bom 的子件数量，需要先除以复用 bom 的数量，计算出单个复用 bom 中子件的数量，再校验 小数位是否越界和数量规则
				if(!rowData.amount_any && rowData.__parentBomRowId){
					let pd = this.treeTable.getRowDataById(rowData.__parentBomRowId);
					if(pd && pd.amount != 1){
						newNum = util.division(newNum, pd.amount);
						if(newNum){
							if(!this.__validAmountDP(newNum)){
								let msg = rowData.product_id__r +  $t('crm.bom.amount1') + newNum + '，' + $t('产品的数量小数位越界，请重新修改');  // 单包产品数量为
								this.treeTable.addCellErrorAndErrorMsg(rowData.rowId, field, msg);
								return;
							}
						}
					}
				}

				res = util.checkRowData(rowData, newNum, this.treeTable, 'amount', this.isOpenIssue());
				if(res) rowData.__amount = newNum;
			}
			//
			if (field === 'pricing_period') {
				let newNum = all.data;
				rowData.__temp_pricing_period = newNum;
				// 整期售卖的产品，期数必须是整数
				if(this._isWholeSale(rowData) && this._isDecimal(newNum)){
					let msg = $t('sfa.crm.bom.checkPricingPeriod2');  // 整期售卖的产品，期数必须是整数
					this.treeTable.addCellErrorAndErrorMsg(rowData.rowId, field, msg);
					return;
				}
				res = util.checkRowData(rowData, rowData.amount, this.treeTable, 'pricing_period', this.isOpenIssue());
				delete rowData.__temp_pricing_period;
			}

			// 临时子件改价格，需要校验父级价格是否可编辑；
			// if (this.isTempNode(rowData) && rowData.isChecked && rowData.pid && field === 'modified_adjust_price') {
				// let ppe = this.checkParentPriceEditable(rowData);
				// if (!ppe && Number(all.data) !== 0) {
				// 	this.alertMsg($t('因父级产品价格不可编辑，所以该产品价格必须为0'));
				// 	// this.treeTable.setCellsValueByRowId({modified_adjust_price: rowData.modified_adjust_price}, rowData.rowId, rowData);
				// 	res = false;
				// }
			// }
			if (!res) return;

			this.options.cellChangeBeforeHook && this.options.cellChangeBeforeHook({
				rowData,
				changeField: field,
				data: this.treeTable.getAllData(),
				updateTableValue: this._updateTableValue.bind(this),
			});
			fn();
		},

		// 是小数
		_isDecimal(num){
			return String(Number(num)).split('.').length > 1;
		},

		// 改复用 bom 数量，需要子件数量 成倍；
		changeChildrenAmount(rowData){
			if(rowData.related_core_id && rowData.children?.length){
				let obj = {};
				let msg = '';
				let cloneData = util.cloneBomData(rowData, ['rowId', 'pid', 'amount', '__amount', 'related_core_id', 'amount_any', 'product_id__r', 'is_package']);
				let dp = this._getDescribe('amount')?.decimal_places;
				util.setChildrenAmount(cloneData, {
					baseAmount: '__amount',
					amount: 'amount',
					amount_any: 'amount_any',
					related_core_id: 'related_core_id',
				},  (val, row) => {
					let r1 = this.__validAmountDP(val);
					if(!r1) msg = '【' + row.product_id__r + '】' + $t('crm.bom.amount3') ;  // 小数位已四舍五入
					obj[row.rowId] = {
						data:{
							amount: util.formatDecimalPlace(val, dp)
						}
					};
				});
				if(msg) this.alertMsg(msg, 'info');
				this.treeTable.setCellsVal(obj);
			}
		},

		//  父级价格是否可编辑
		// checkParentPriceEditable(row) {
		// 	let pid = row.pid;
		// 	let pData = this.treeTable.getRowDataById(pid);
		// 	if (!pData || pData.isGroup) return true;
		// 	return pData.price_editable;
		// },

		checkboxClickHook_before: $.noop,

		// 复选框点击事件；
		async checkboxClick(isChecked, el, isCheckedNum, single, rowId) {
			let _this = this;
			let data = this.treeTable.getRowDataById(rowId);
			await this.checkboxClickHook_before(isChecked, data);

			// 标记默认选中项被手动反选掉了
			if (!isChecked && data.selected_by_default) {
				data.noSelectDef = true;
				if (data.__isNotFlowRootPricebook) _this.treeTable.setCheckboxDisabled(data, true);
			}

			if (isChecked) {
				this.checkAttrError(data);
				let r = await this.executeBomAttrConstraint();
				if (!r) return;
			} else {
				let hasMark = this.checkHasConstraintRule(data);
				if (hasMark) {
					let r = await this.executeBomAttrConstraint();
					if (!r) return;
				}
			}
			// 模式 3时，走一遍属性过滤逻辑
			if(this._isStyle3()) this.onlyShowSelectedRow();

			if (data.children) {
				// 若取消勾选，且有children，先重置调整价格；
				if (!isChecked) {
					util.forEachTreeData([data], function (item) {
						if (!item.children) return;
						_this.treeTable.setCellsValueByRowId({
							"modified_adjust_price": item.__adjust_price,
							"adjust_price": item.__adjust_price,
							"price_per_set": util.multiplicational(item.__adjust_price, item.__pricing_period),

						}, item.rowId, item)
					})
				} else {
					this.treeTable.openRowChildren(rowId, true, data); // 勾选时，展开子节点；
					this.calLastLevelParents([data])
				}
			}
			if (CRM._cache.openAttribute) {
				let firstPData = data.pid ? this.getFirstParents(data.pid, this.treeData) : data;
				this.displayAttribute([firstPData])
			}
			this.upDateEveryParentPrice(data.pid);
			this.calculateTotalMoney();
			this.removeGroupError(data.pid);
			if (!isChecked) this.hideRowAfterCheck(data);
			this._closeParent(data, isChecked);
			this.options.checkboxClickEndHook && this.options.checkboxClickEndHook({isChecked, data, api: this._getApi(),});
			this.afterCheckboxClickHook(isChecked, data);

		},

		// 单选分组，勾选子件后自动收起分组；
		_closeParent(data, isChecked){
			// bom_single_leaf_node_closed
			if(this._singleClose && isChecked && data.pid && !data.children){
				let p = this.treeTable.getRowDataById(data.pid);
				if(p.isGroup && p.group_options_control){
					this.treeTable.setChildrenShowType(p, false);
				}
			}
		},

		// 计算有子节点的节点
		calLastLevelParents(data) {
			if(this.individualPricingForChildren()) return;
			util.forEachTreeData(data, item => {
				if (item.children && !item.isGroup && item.isChecked) {
					this.upDateEveryParentPrice(item.rowId);
				}
			})
		},

		// 查找最后一层子级数据
		findLastLevelChildren(children) {
			children = Array.isArray(children) ? children : [children];
			let r;
			const queue = util.cloneObjArr(children);
			while (queue.length > 0) {
				const node = queue.shift();
				r = node;
				if (node.children && node.children.length > 0) {
					for (const child of node.children) {
						queue.push(child);
					}
				}
			}
			return r;
		},

		afterCheckboxClickHook: $.noop,

		//展示 or 隐藏产品的属性
		displayAttribute(data) {
			if (!this.treeTable) return;
			let tableData = data || this.treeTable.getAllData() || [];
			CRM.util.forEachTreeData(tableData, (data, pData) => {
				if (!data.isGroup && this._hasAttr(data)) {
					if (data.isChecked && data.isShow) {
						this.treeTable.showAttribute(data)
					} else {
						this.treeTable.hideAttribute(data)
					}
				}
			})
		},

		displayAttribute2(showAttr) {
			if (!this.treeTable) return;
			let tableData = this.treeTable.getAllData() || [];
			CRM.util.forEachTreeData(tableData, data => {
				if (!data.isGroup && this._hasAttr(data)) {
					if (showAttr && data.isChecked && data.isShow) {
						this.treeTable.showAttribute(data)
					} else {
						this.treeTable.hideAttribute(data)
					}
				}
			})
		},

		// 移除分组行的error提示；
		removeGroupError: function (rowId) {
			if (rowId) {
				let data = this.treeTable.getRowDataById(rowId);
				if (data) {
					if (data.isGroup) this.treeTable.removeTrError(data.rowId);
					if (data.pid) this.removeGroupError(data.pid);
				}
			}
		},

		showGroupDetail: function (data) {
			require.async('crm-modules/components/showgroupdetail/showgroupdetail', function (showGroupDetail) {
				if (data.isGroup) {
					showGroupDetail(data);
				} else if (data.product_group_id) {
					util.getGroupInfo(data.product_group_id, function (value) {
						showGroupDetail(value)
					})
				}
			});
		},

		// 计算自己单套价格
		calSelfSingleSetPrice(data){
			let dp = this._getDescribe('price_per_set')?.decimal_places;
			let val = this.multiplicationalLoop(data, ['modified_adjust_price', 'pricing_period']);
			let obj = {
				[data.rowId]: {
					data:{
						price_per_set: util.formatDecimalPlace(val, dp)
					}
				}
			};
			this.treeTable.setCellsVal(obj);
		},

		// 单元格编辑
		async cellChange(data, column, type, obj) {
			let _this = this;
			let rowData = obj.cellData;
			let fieldName = column.api_name;
			if (rowData.children && column.api_name === "modified_adjust_price") {
				util.onceAlert('bom_change_map', $t('bom手动改调整价格'))
			}
			await this.beforeCellChangeHook(rowData);

			this.changeChildrenAmount(rowData);

			// 改调整金额，计算自己的单套价格
			if(fieldName === "modified_adjust_price" || fieldName === "pricing_period"){
				this.calSelfSingleSetPrice(rowData)
			}

			// 计算每层父级价格
			if (rowData.isChecked) {
				if(this.individualPricingForChildren()) {
					this.calculateTotalMoney();
				}else{
					let id = fieldName === "pricing_period" ? rowData.rowId : rowData.pid;
					this.upDateEveryParentPrice(id);
					this.calculateTotalMoney();
				}	
			}
			// 清空子件版本
			if(fieldName === "node_bom_core_version" && !data){
				rowData.related_core_id = null;
			}
			if(['amount', 'modified_adjust_price'].includes(fieldName)){
				await this.calculateFormulaRow(rowData, fieldName);
			}
			this.options.cellChangeAfterHook && this.options.cellChangeAfterHook({
				rowData,
				changeField: column.api_name,
				data: this.treeTable.getAllData(),
				updateTableValue: this._updateTableValue.bind(this),

			})
		},

		beforeCellChangeHook: $.noop,

		showdetail($target) {
			let rowId = $target.parents('.tr').data('rowid');
			let rowData = this.treeTable.getRowDataById(rowId);
			this._showDetail(rowData._id, 'BOMObj')
		},

		getDialogTitle() {
			return this.options.title;
		},

		/**
		 * @desc 复选框
		 */
		renderDialog() {
			let className = this._isStyle3() ? 'dialog_selectbom_style3' :'';
			if(this.options.selfElement) {
				this.options.selfElement.addClass(className);
				this.addContainer(this.options.selfElement);
				return;
			};
			var _this = this;
			var height = $(window).height() - 57 - 55;
			const tpl = `<div class="dialog_selectbom_content" style="height:${height}px;">
							<div class="dialog_attribute">
							</div>
						</div>`;

			require.async('crm-widget/dialog/dialog', function (Dialog) {
				_this.dialog = new Dialog({
					content: tpl,
					classPrefix: 'crm-c-dialog crm-dialog-selectbom crm-comp-pickview ' + className,
					title: _this.getDialogTitle(),
					showBtns: true,
					showScroll: false,
					zIndex: _this.options.zIndex,
					size: 'hg',
					btnName: _this.options.btnName,
					// showFullScreenBtn:true,
					maxSize: true
				});

				_this.dialog.on('dialogEnter', _this.dialogEnterEve.bind(_this));

				_this.dialog.on('dialogCancel', function (evt) {
					_this.trigger('dialogCancel');
					_this.destroy();
				});

				_this.dialog.on('hide', function (evt) {
					_this.trigger('dialogClose');
					_this.destroy();
				});

				_this.dialog.show();
				_this.$el = _this.dialog.element;
				_this.addContainer(_this.dialog.element.find('.dialog_selectbom_content'))

			})
		},

		// 校验超
		checkSelectedNum(data) {
			// let n = 0;
			// util.forEachTreeData(data, item => {
			// 	if (!item.isGroup) n++;
			// });
			// if (n > 1000) {
			// 	util.alert($t('最多可勾选1000条子产品'));
			// 	return false;
			// }
			return true;
		},

		async getResult(){
			let _this = this;
			if(!this.treeData) return;
			let obj = _this.getData(true);
			let r = this.checkSelectedNum(obj.data);
			if (!r) return;
			if (_this.treeTable.editValid() && _this.validGroupAndAmount(_this.treeTable, obj.data, obj.checkedData)) {
				let param = _this.parseDataToCheckBom(obj);
				const flag = _this.beforeCheckBom(obj);
				if (!flag) return;
				CRM.util.showLoading_tip();
				let res = await util.checkBom(param);
				let checkRes = _this.parseCheckRes(res);
				CRM.util.hideLoading_tip();
				if (!checkRes) return;
				if (CRM._cache.openNsAttribute) {
					checkRes = this.checkNsAttrValueEmpty(obj.data, obj.newRootData);
					if (!checkRes) return;
				}
				// 有约束规则，走一次 server 校验
				if (this._attrConstraintRule.length || this._attrAplFunctionList.length) {
					let allData = this._getAllDataIncludeRoot();
					let r2 = await util.checkBomAttrRule_server(allData[0], this._attrConstraintRule, this._attrAplFunctionList);
					if (!r2) return;
					let r3 = await this.checkFormula();
					if (!r3) return;
				}
				// 自定义校验数据勾子
				if (_this.options.dialogEnter_after) {
					let r = await _this.options.dialogEnter_after({
						data: obj.data,        			// 当前勾选的子产品
						newRootData: obj.newRootData || _this._rootPriceBookData, 	// 根节点数据
					});
					if (r && !r.status) return;
				}
				return obj;
			}
		},

		dialogEnterEve: _.debounce(async function () {
			let obj = await this.getResult();
			if(!obj) return;
			this.trigger('dialogEnter', obj);
			this.destroy();
		}, 300),

		// 校验勾选数据非标属性是否都有值
		checkNsAttrValueEmpty(checkData = [], rootData) {
			let msg = '';
			util.forEachTreeData(checkData, item => {
				if (item.isGroup || !item.isChecked) return;
				if (item.nonstandardAttribute && item.nonstandardAttribute.length) {
					let s = util.checkNsAttrAllVal(item.nonstandard_attribute_json, item.nonstandardAttribute);
					if (!s.status) msg += util.getBomPath(item) +'【' + s.msg.join(',') + '】' + ': ' + $t('非标属性值不能为空') + '<br/>';
				}
				msg += this.checkAttrValue(item);
			});
			if(rootData){
				if (rootData.nonstandardAttribute && rootData.nonstandardAttribute.length) {
					let s = util.checkNsAttrAllVal(rootData.nonstandard_attribute_json, rootData.nonstandardAttribute);
					if (!s.status) msg += rootData.product_id__r +'【' + s.msg.join(',') + '】' + ': ' + $t('非标属性值不能为空') + '<br/>';
				}
				msg += this.checkAttrValue(rootData);
			}
			if (msg) this.alertMsg(msg);
			return !msg;
		},

		// 校验属性是否都有值，灰度了无属性默认值
		checkAttrValue(item) {
			let msg = '';
			let isGrayNoDefault = CRM.util.isGrayScale('CRM_ATTR_NO_DEFAULT');
			if (!isGrayNoDefault) return msg;
			if (item?.attribute) {
				let r1 = util.validAttribute(item);
				if (!r1) msg += util.getBomPath(item) + ': ' + $t('属性值不能为空') + '<br/>';
			}
			return msg;
		},

		/**
		 * @desc 校验分组规则，校验数量规则，校验约束条件
		 * @param table
		 * @param data 当前勾选项，不含未选中项
		 * @param allCheckedData 当前勾选项，包括未选中的所有子级；
		 * @returns {*}
		 */
		validGroupAndAmount: function (table, data, allCheckedData) {
			var res1 = util.validGroupData(table, null, true);
			if (!res1) return false;
			var res2 = util.validAmountBeforeSubmit(table, data, null, null, true,);
			var res3 = util.validGroupData(table, allCheckedData);
			var nData = util.parseTreeToNormal(allCheckedData, true);
			nData = _.filter(nData, item => item.isChecked);
			var res4 = util.validConstraintRuleForBom(nData, table.getAllData());
			return res2 && res3 && res4;
		},

		/**
		 * @desc 显示详情
		 */
		_showDetail: function (id, apiname) {
			var me = this;
			if (!id) return;
			require.async('crm-components/showdetail/showdetail', function (Detail) {
				me._detail && (me._detail.destroy(), me._detail = null);
				me._detail = new Detail({
					apiName: apiname || '',
					top: 0,
					zIndex: me.options.zIndex
					// showMask: true,
				});
				me._detail.on('refresh', function () {
					// me._showConfig(me.productId)
				});
				me._detail.show(id);
			});
		},

		// 最新当前选中项总金额
		calculateCurSelectedMoney(data) {
			return util.calculateSelectMoney(data, 'modified_adjust_price'); // 最新当前选中项总金额
		},

		/**
		 *  @desc 计算总金额
		 */
		calculateTotalMoney: function () {
			if(this.individualPricingForChildren()) return this.calculateTotalMoneyForChildren();
			let originalTotalMoney = this.options.originalTotalMoney; // 原始总金额
			let defSelectMondy = this.defSelectMondy; // 默认选中总金额
			let data = this.getTableCheckedData();
			let currentSelectValue = this.calculateCurSelectedMoney(data); // 最新当前选中项总金额
			this.totalMoney = util.accAdd(util.accAdd(Number(originalTotalMoney), -defSelectMondy), currentSelectValue);
			this.totalMoney = util.formatDecimalPlace(this.totalMoney, this.pricision);
			let tm = this.totalMoney;
			// 周期性产品单套总价格
			if(this.isOpenIssue()){
				let om = this.getRootSingleSetPrice(); 	// 原始总金额
				let dm = this._defSelectSingleSetMoney;         // 默认选中总金额
				let sv = util.calculateSelectMoney(data, 'price_per_set', false, true); // 最新当前选中项总金额
				this.totalSingleSetPrice = util.accAdd(util.accAdd(om, -dm), sv);
				this.totalSingleSetPrice = util.formatDecimalPlace(this.totalSingleSetPrice, this.pricision);
				tm = this.totalSingleSetPrice;
			}
			this.updateTotalMoney(tm);
			this.updateSelectNum(data);
		},

		// 子件单独定价，计算总金额; 总金额 = 母件原始总金额 + 子件最新当前选中项总金额
		calculateTotalMoneyForChildren(){
			let originalTotalMoney = this.options.originalTotalMoney; // 原始总金额
			let data = this.getTableCheckedData();
			let currentSelectValue = util.calculateSelectMoneyForChildren(data, 'modified_adjust_price'); // 最新当前选中项总金额
			this.totalMoney = util.accAdd(originalTotalMoney, currentSelectValue);
			this.totalMoney = util.formatDecimalPlace(this.totalMoney, this.pricision);
			let tm = this.totalMoney;
			// 周期性产品单套总价格
			if(this.isOpenIssue()){
				let om = this._originalTotalSingleSetPrice; 	// 原始总金额
				let sv = util.calculateSelectMoneyForChildren(data, 'price_per_set', ); // 最新当前选中项总金额
				this.totalSingleSetPrice = util.accAdd(om, sv);
				this.totalSingleSetPrice = util.formatDecimalPlace(this.totalSingleSetPrice, this.pricision);
				tm = this.totalSingleSetPrice;
			}
			this.updateTotalMoney(tm);
			this.updateSelectNum(data);
		},

		initTotalMoney() {
			let el = this.options.totalMoneyElement || this.dialog.element.find('.dialog-con-wrap');
			let isHide = this.options.isHideTotalMoney ? 'displayNone' : '';
			if(isHide) return;
			let defMoney = this.isOpenIssue() ? this.getRootSingleSetPrice() : this.options.originalTotalMoney;

			el.append(`<div class="selectbom-totalMoney ${isHide}">
				<div class="totalMoneyIcon"><span class="selectbom-selectnum" >0</span></div>
				<div class="totalMoneyBox">
					<p class="originalTotalMoney">${$t('总金额')}: <span class="defTotalMoney">${CRM.util.toMoney(defMoney)}</span></p>
					<p class="afterChangeMoney">${$t('调整后总金额')}: <span class="changeMoney">--</span></p>
				</div>
			</div>`)
		},

		getTableCheckedData() {
			return this.treeTable.getOnlyCheckedData();
		},

		getTableParentCheckedData() {
			return this.treeTable.getCheckedData(true);
		},

		// 用__amount 替换 amount
		_changeAmount(data, checkedData){
			util.forEachTreeData(data, item => {
				if(item.isGroup) return;
				if(item.hasOwnProperty('__amount')) item.amount = item.__amount;
			});
			util.forEachTreeData(checkedData, item => {
				if(item.isGroup) return;
				if(item.hasOwnProperty('__amount')) item.amount = item.__amount;
			});
		},

		//flag-true: 最终提交数据，才格式化属性相关值
		getData: function (flag) {
			this.updateNewPriceBookDataAttr();
			let checkedData = this.getTableParentCheckedData();
			checkedData = util.cloneObjArr(checkedData);
			let data = this.getTableCheckedData();
			this._changeAmount(data, checkedData);
			if (flag) {
				if (CRM._cache.openAttribute) {
					util.parseDataAttr(checkedData);
					util.parseDataAttr(data);
				}
			}
			let newRootData = null;
			if(this.newPriceBookProductData){
				let rd = util.cloneObjArr([this.newPriceBookProductData]);
				rd = util.generateAttrsInfo(rd);
				newRootData = rd[0];
			}
			let rd2 = util.cloneObjArr([this.rootData]);
			rd2 = util.generateAttrsInfo(rd2);

			data = this.parseDataHook(data);

			let {bom_core_id, bom_type, bom_version , bom_core_name} = this.options;
			return {
				data,        			// 当前勾选的子产品
				checkedData, 			// 第一层勾选的数据，包含各自所有子产品，用来校验分组规则
				newRootData, 			// 根节点价目表明细数据
				rootDataBomId: this.rootDataBomId,  // 根节点bom_id
				rootDataCoreId: this.options.bom_core_id,  // 根节点bom_core_id
				totalMoney: this.totalMoney,        // 调整后总金额
				originalTotalMoney: this.options.originalTotalMoney, // 产品包原始金额
				hasAttributeConstraint: !!this._attrConstraintRule.length,
				bomCoreData:{
					node_bom_core_type: bom_type,
					node_bom_core_version: bom_version,
					_id: bom_core_id,
					name: bom_core_name,
				},
				newCoreData: this._newCoreData,
				rootData: rd2[0],     // 根节点产品数据
				totalSingleSetPrice: this.totalSingleSetPrice,

				sale_strategy: this.rootData.sale_strategy, // 子件定价

			}
		},

		// 最终获取数据前，删掉临时子件的bom_id;
		parseDataHook(data) {
			if (this.isOpenTemporaryChildren()) {
				data = util.cloneObjArr(data);
				util.forEachTreeData(data, item => {
					if (this.isTempNode(item)) {
						delete item.bom_id;
					}
				})
			}
			return data;
		},

		// 更新调整后总金额；
		updateTotalMoney: function (m) {
			if(this.options.isHideTotalMoney){
				this.options.totalMoneyChangeHook && this.options.totalMoneyChangeHook(m);
				return
			}
			let el = this.getTotalPriceElement();
			el.find('.changeMoney').html(CRM.util.toMoney(m));
		},

		// 更新默认总金额
		updateDefTotalMoney: function () {
			let el = this.getTotalPriceElement();
			let m = util.formatDecimalPlace(this.options.originalTotalMoney, this.pricision);
			el.find('.defTotalMoney').html(CRM.util.toMoney(m));
		},

		// 移除已经作废、下架、未启用的子节点
		removeInvalidData: function (data, noAlert) {
			let len = data.length;
			let delRow = [];
			let alertProduct = [];

			while (len--) {
				let item = data[len];
				if (!item.isGroup && !util.isNormal(item)) {
					delRow.push(item);
					data.splice(len, 1);
				}
			}

			if (this.options.extendData && delRow.length && !noAlert) {
				util.forEachTreeData(this.options.extendData, function (item) {
					if (util.findDataByBomId(delRow, item._id)) {
						alertProduct.push(item.product_id__r)
					}
				})
			}

			if (alertProduct.length) {
				util.alert($t('产品') + ': ' + alertProduct.toString() + ' , ' + $t('已被下架或作废或删除'));
			}
		},

		_saveDefSelectRowHook(data) {
			return data;
		},

		_getDefObj(item) {
			if ((this.isDefSelect(item)) && !item.isGroup) {
				return {
					adjust_price: item.adjust_price,
					__adjust_price: item.__adjust_price,
					__pricing_period: item.__pricing_period,
					product_id: item.product_id,
					product_id__r: item.product_id__r,
					parent_bom_id: item.parent_bom_id,
					amount: item.amount,
					__amount: item.__amount,
					_id: item._id,
					bom_id: item._id,
					rowId: item.rowId,
					pid: item.pid,
					current_root_new_path: item.current_root_new_path,
				};
			}
		},

		/**
		 * @desc 保存默认选中行数据，用于计算;
		 */
		saveDefSelectRow: function (data) {
			util.forEachTreeData(data, item => {
				// 缓存因为报价器而选中的数据
				if (!item.isGroup && item.selected_by_quoter) {
					let f = this._cacheSelectedByQuote.find(c => c.rowId === item.rowId);
					if(!f){
						this._cacheSelectedByQuote.push(item);
					}
				}
				let o = this._getDefObj(item);
				if (o) {
					let f = this.defSelectRow.find(c => c.rowId === o.rowId);
					if(f){
						Object.assign(f, o);
					}else{
						this.defSelectRow.push(o);
					}
				}
			});
		},

		upDateEveryParentPrice(id) {
			if(this.individualPricingForChildren()){
				return
			}
			this._upDateEveryParentPrice(id);
		},

		/**
		 * @desc 更新每一层节点的modified_adjust_price
		 * @param id
		 * @param reset 重置调整价格
		 * @param onlyAdjustPrice 只计算选配价格
		 */
		_upDateEveryParentPrice: function (id) {
			let _this = this;
			if (!id) return;

			function _fn(pid) {
				let data = _this.treeTable.getRowDataById(pid);
				if (!data) return;
				if (data.isGroup) {
					if (!data.pid) return;
					data = _this.treeTable.getRowDataById(data.pid);
				}
				if (data.children) {
					let update = _this._calculatePrice(data);
					_this.treeTable.setCellsValueByRowId(update, data.rowId, data)
				}
				if (data.pid) _fn(data.pid);
			}

			_fn(id)
		},

		// 查找最上层父级节点
		getFirstParents: function (id, allData) {
			if (!id) return;
			let data;

			function _fn(pid) {
				let f = util.getDataByKey(pid, allData);
				if (!f) return;
				data = f;
				if (data.pid) _fn(data.pid);
			}

			_fn(id)

			return data;
		},

		_doUpDateEveryParentPrice: function (id, allData) {
			let _this = this;
			if (!id) return;

			function _fn(pid) {
				let data = util.getDataByKey(pid, allData);
				if (!data) return;
				if (data.isGroup) {
					if (!data.pid) return;
					data = util.getDataByKey(data.pid, allData);
				}
				if (data.children) {
					let update = _this._calculatePrice(data);
					data = Object.assign(data, update);
					_this._doUpDateEveryParentPriceHook(data);
				}
				if (data.pid) _fn(data.pid);
			}

			_fn(id)
		},

		// 多个字段连乘
		multiplicationalLoop(data, fields){
			let res = 1;
			fields.forEach(f => {
				res = util.multiplicational(res, Number(data[f]))
			});
			return res;
		},

		// 计算某行的单套价格
		getRowPriceSingle(data, type = 1){
			let price = type === 1 ? 'adjust_price' : 'modified_adjust_price';
			let pricingPer = type === 1 ? '__pricing_period' : 'pricing_period';
			let am = data.hasOwnProperty('__amount') ? '__amount' : 'amount';
			let fields = [price, pricingPer, am];
			return this.multiplicationalLoop(data, fields);
		},

		// 计算节点价格；
		_calculatePrice(data) {
			let _this = this;
			let defSelectRow = this.getDefSelectRow(data._id, data.current_root_new_path);
			let currentSelectRow = this.getCurrentSelectRow(data.children);
			let defSelectValue = 0;
			let currentSelectValue = 0;
			let currentSelectAdjustValue = 0;

			let defSelectPriceSingleValue = 0;		// 默认选中行的总单套价格
			let currentSelectPriceSingleValue = 0;	// 当前单套价格

			let isOpenPP = this.isOpenIssue();

			let res = data.modified_adjust_price;
			let adjustPriceRes = data.adjust_price;
			let curPriceSingle = this.multiplicationalLoop(data, ['__adjust_price', 'pricing_period']);

			// 默认选中项总金额
			if (this.isIncludeDefSelect()) {
				_.each(defSelectRow, function (item) {
					defSelectValue = util.accAdd(defSelectValue, util.multiplicational(Number(item.__adjust_price), Number(_this._getAmount(item))));
					// 统计子件的默认总单套价格 = 子件的初始单套价格 * 数量 之和
					if(isOpenPP) defSelectPriceSingleValue = util.accAdd(defSelectPriceSingleValue, _this.getRowPriceSingle(item));
				});
			}

			// 当前选中项总金额
			_.each(currentSelectRow, function (item) {
				currentSelectValue = util.accAdd(currentSelectValue, util.multiplicational(Number(item.modified_adjust_price), Number(_this._getAmount(item))));
				// 统计子件的当前总单套价格 = 子件的最新单套价格 * 数量 之和
				if(isOpenPP) currentSelectPriceSingleValue = util.accAdd(currentSelectPriceSingleValue, _this.getRowPriceSingle(item, 2));
			});

			// 当前选中项标准选配价格之和
			_.each(currentSelectRow, function (item) {
				currentSelectAdjustValue = util.accAdd(currentSelectAdjustValue, util.multiplicational(Number(item.adjust_price), Number(_this._getAmount(item))));
			});

			// 计算调整价格 = 自己的价格 - 默认选中子产品的价格 + 当前选中子产品的调整价格；
			res = util.accAdd(util.accAdd(Number(data.__adjust_price), -defSelectValue), currentSelectValue);
			// 计算标准选配价格 = 自己的价格 - 默认选中子产品的价格 + 当前选中子产品的标准选配价格；
			adjustPriceRes = util.accAdd(util.accAdd(Number(data.__adjust_price), -defSelectValue), currentSelectAdjustValue);
			// 计算单套价格 = 自己的原始价格*自己的期数 - 默认选中子产品单套价格*数量 + 当前选中子产品的最新单套价格*数量；
			if(isOpenPP) curPriceSingle = util.accAdd(util.accAdd(Number(curPriceSingle), -defSelectPriceSingleValue), currentSelectPriceSingleValue);

			let m = util.formatDecimalPlace(res, this.pricision);
			let adPrice = util.formatDecimalPlace(adjustPriceRes, this.pricision);
			if(isOpenPP) curPriceSingle = util.formatDecimalPlace(curPriceSingle, this.pricision);


			// this.calPriceSingleset(data, );
			return {
				"modified_adjust_price": m,
				"adjust_price": adPrice,
				"price_per_set": curPriceSingle,
			};
		},

		_getAmount(data){
			return data.hasOwnProperty('__amount') ? data.__amount : data.amount;
		},

		_doUpDateEveryParentPriceHook: $.noop,

		/**
		 * @desc 获取默认选中项数据
		 * @param bomId
		 * @returns {*}
		 */
		getDefSelectRow: function (bomId, rootNewPath) {
			return _.filter(this.defSelectRow, function (item) {
				return item.parent_bom_id === bomId && item.current_root_new_path.includes(rootNewPath);
			})
		},

		/**
		 * @desc 获取某节点的当前选中的子节点；若子节点是分组，则取分组中的第一层数据;
		 * @param data
		 * @returns {Array}
		 */
		getCurrentSelectRow: function (data) {
			let res = [];
			_.each(data, function (item) {
				if (item.isChecked) {
					if (item.isGroup) {
						if (item.children) {
							_.each(item.children, function (c) {
								if (c.isChecked) res.push(c);
							})
						}
					} else {
						res.push(item)
					}
				}
			});
			return res;
		},

		// 校验子件不在母件价目表
		beforeCheckBom(obj) {
			const tData = util.parseTreeToNormal(obj.data || [], true);
			const tips = tData.filter((item) => item.__isNotFlowRootPricebook).map((item) => `【${item.product_id__r}】`).join('');
			if (tips) {
				util.alert(tips + $t('产品不在母件选择的价目表中，如需添加该产品，需将其维护至选择的价目表'))
				return false;
			}
			return true;
		},

		/**
		 * @desc 组装数据，给server接口校验bom
		 * @param obj
		 * @returns {{productBomList: {rootProductId: (string|*), newProductPrice: (number|*), subBomList: (*|Array)}[]}}
		 */
		parseDataToCheckBom: function (obj) {
			let _this = this;
			let rootId = util.uniqueCode();

			_.each(obj.data, function (item) {
				item.pid = item.parent_prod_pkg_key = rootId;
			});

			let data = util.parseTreeToNormal(obj.data, true);

			let groupList = _.filter(data, function (item) {
				return item.isGroup;
			});

			function _findGroup(id) {
				return _.find(groupList, function (item) {
					return item.rowId == id;
				})
			}

			// server用的字段；
			let fields = ['new_bom_path', 'core_id', 'related_core_id', 'node_bom_core_type', 'node_bom_core_version', 'product_group_id__r', 'node_type',
				'product_id', '_id', 'product_status', 'product_id__r', 'adjust_price', 'object_describe_api_name', 'amount', 'product_group_id',
				'bom_id', 'modified_adjust_price', '__adjust_price', 'prod_pkg_key', 'parent_prod_pkg_key', 'price_book_id'];
			let subBomList = [];
			data.forEach(item => {
				if (item.isGroup) return;
				item.prod_pkg_key = item.rowId;
				delete item.product_id__ro;
				if (item.pid && !item.isGroup) {
					let group = _findGroup(item.pid);
					item.parent_prod_pkg_key = group ? group.pid : item.pid;
				}
				let o = _.pick(item, fields);
				subBomList.push(o);
			});

			let pbId = _this.newPriceBookProductData ? _this.newPriceBookProductData.pricebook_id : _this.options.priceBookId || '';

			return {
				"productBomList": [{
					core_id: _this.options.bom_core_id,
					node_bom_core_type: _this.options.bom_type,
					node_bom_core_version: _this.options.bom_version,
					rootProductId: _this.options.rootId, // 根节点产品id
					newProductPrice: obj.totalMoney, 	 // 调整后总金额
					subBomList,					     // 子产品数据，不含分组
					rootProdKey: rootId,				 // 根节点虚拟key
					rootBomId: obj.rootDataBomId, 		 // 根节点bom_id
					priceBookId: pbId					 // 根节点的价目表id
				}],
				priceBookId: pbId						 // 根节点的价目表id
			}
		},

		/**
		 * @desc 处理校验结果
		 * @param obj
		 */
		parseCheckRes: function (obj) {
			if (obj.Result.StatusCode !== 0) {
				return false;
			}
			let msg = '';
			let result = obj.Value.result;
			_.each(result, function (item, key) {
				if (item.checkSingleResult) msg += item.checkSingleResult + '<br/>';
			});

			if (msg.length) {
				util.alert(msg);
				return false
			}
			return true;
		},

		// 临时子件，是由明细带过来的，需要找到对应的位置，塞到对应父级下
		extendTempData(data = [], extendData = [], rootData = {}) {
			if (!this.isOpenTemporaryChildren()) return;
			let {node_no, bom_id} = this.getFieldMap();
			let basicData = this.getBomObjBasicData();
			let msg = '';
			util.forEachTreeData(extendData, row => {
				if (this.isTempNode(row)) {
					let sData = util.getDataByKey(row.rowId, data);
					if (!sData) return msg = $t('临时子件不可用，已删除');
					delete sData.pid;
					sData.bom_id = sData.rowId;
					row._oldRowId = row.rowId;
					sData._oldRowId = sData.rowId;
					sData.order_field = row[node_no] || row.order_field;
					Object.assign(sData, basicData);
					let pData = util.getDataByKey(row.parent_rowId || row.pid, extendData);
					if (!pData) {
						sData.parent_bom_id = rootData.bom_id;
						delete sData.parent_rowId;
						return
					}
					if (pData.isGroup) {
						sData.product_group_id = pData[bom_id];
						let realPData = util.getDataByKey(pData.parent_rowId || pData.pid, extendData);
						if (realPData) {
							sData.parent_bom_id = realPData.bom_id;
						} else {
							sData.parent_bom_id = rootData.bom_id;
						}
					} else {
						sData.parent_bom_id = pData[bom_id];
					}
				}
			});
			if (msg) this.alertMsg(msg, 'info');
		},

		getFieldMap() {
			return this.options.mapFields || util.getSpecialFieldByApiName(this.options.apinameFrom);
		},

		/**
		 * @desc 用从对象上的数据替换最新数据；
		 * @param data
		 * @param newData
		 */
		extendBomData: function (data, newData) {
			let _this = this;
			util.setBomChecked(data, null, true, true, true);
			let obj = this.getFieldMap();
			util.forEachTreeData(newData, item => {
				let oData;
				if (_this.isTempNode(item)) {
					oData = util.getDataByKey(item.rowId, data);
				} else {
					oData = item[obj.new_bom_path] ? util.findDataByBomId(data, item[obj.new_bom_path], 'new_bom_path') : util.findDataByBomId(data, item[obj.bom_id]);
				}
				if (oData) {
					if (oData.pid && !_this.extendBomDataPIds.includes(oData.pid)) _this.extendBomDataPIds.push(oData.pid);
					// 数据变更导致不匹配，不选中回填
					if (!oData.__isNotFlowRootPricebook) {
						oData.isChecked = true;
					}
					_this._extendData(item, oData);
					if (!item.isChecked && !item.is_required) item.disabled = false;
				}
			});
			util.forEachTreeData(data, (item) => {
				// 不匹配价目表 && 非选中 && 配置默认选中，设置为不可选
				if (item.__isNotFlowRootPricebook && !item.isChecked && item.selected_by_default) {
					item.disabled = true;
				}
			});
			util.checkGroupIsChecked(data);
		},

		// 替换数据，用新数据更新老数据
		_extendData(nData, oData) {
			let _this = this;
			let {related_core_id, bom_version, nonstandard_attribute_json, attribute_json, price_per_set, pricing_period} = this.getFieldMap();
			if (!nData.isGroup) {
				let price = nData.modified_adjust_price;
				let amount = _this.getAmount(nData);
				oData.modified_adjust_price = price; // 价格变动记录点，销售订单和报价单的价格字段不一样
				oData.amount = oData.__amount = amount;
				if(oData.is_package && !oData.related_core_id){
					oData.related_core_id = nData[related_core_id];
					oData.node_bom_core_version = nData[bom_version];
				}
				if (nData.hasOwnProperty('adjust_price')) oData.adjust_price = nData.adjust_price;
				if (nData._hasTempNode) oData._hasTempNode = nData._hasTempNode;
				// 价目表id。如果开了强制优先级，就不用明细上带过来的价目表了；如果是配置价格，也不回填价目表信息
				if (nData.price_book_id && !_this._priceBookPriority && oData.price_mode == '2') {
					oData.price_book_id = nData.price_book_id;
					oData.price_book_id__r = nData.price_book_id__r;
				}
				// 价目表产品id。如果开了强制优先级，就不用明细上带过来的价目表了；如果是配置价格，也不回填价目表信息
				if (nData.price_book_product_id && !_this._priceBookPriority && oData.price_mode == '2') {
					oData.price_book_product_id = nData.price_book_product_id;
					oData.price_book_product_id__r = nData.price_book_product_id__r;
				}
				//如果有选属性
				if (nData[attribute_json]) {
					oData.attribute_json = nData[attribute_json];
					oData.selectedAttr = nData.selectedAttr;
				}
				if (nData.selectedAttr) {
					oData.selectedAttr = nData.selectedAttr;
				}
				if (nData.nsAttr) {
					oData.nsAttr = nData.nsAttr;
				}
				// 替换非标属性
				if (nData[nonstandard_attribute_json]) {
					let nonAttrList = oData.nonstandardAttribute;
					if(nonAttrList){
						oData.nsAttr = {};
						_.each(nData[nonstandard_attribute_json], (val, key) => {
							let fa = nonAttrList.find(a => a.id === key);
							if (fa) {
								oData.nsAttr[key] = {
									value: val,
									name: fa.name
								}
							}
						})
					}
				}
				if (nData[pricing_period]) {
					oData.pricing_period = nData[pricing_period];
					oData.price_per_set = nData[price_per_set];
				}
				util.initDataSelectedAttrByJson(oData);
			}
		},

		getAmount: function (data) {
			let {quantity} = this.getFieldMap();
			if (data.hasOwnProperty('defQuantity')) return data.defQuantity;
			if (data.hasOwnProperty(quantity)) return data[quantity];
			return data.amount;
		},

		/**
		 * @desc 更新替换行数据的父级price
		 */
		updateExtendDataPrice: function (data) {
			let _this = this;
			_.each(data, function (id) {
				_this.upDateEveryParentPrice(id)
			})
		},

		/**
		 * @desc 更新已选数量，只统计非分组的节点;
		 * @param data
		 */
		updateSelectNum: function (data) {
			let _this = this;
			let num = 0;
			util.forEachTreeData(data, function (item) {
				if (!item.isGroup) num++;
			});
			let el = this.getTotalPriceElement();
			let t = setTimeout(function () {
				if (el?.length) {
					let dom = el.find('.selectbom-selectnum');
					dom.html(num);
					dom.attr('title', num);
				}
				clearTimeout(t)
			})
		},

		/**
		 * @desc 获取总金额的精度 （取"标准选配价格"的精度）
		 */
		getTotalMoneyPrecision: function () {
			// var r = _.find(this.columns, function (item) {
			// 	return item.data === 'adjust_price'
			// });
			// if (r) {
			// 	return r.decimal_places;
			// } else {
			// 	// util.alert($t('缺少标准选配价格字段，请联系管理员'));
			// 	return 0
			// }
			return this.options.decimal_places
		},

		/**
		 * @desc 搜索结果匹配，展开匹配行；
		 * @param key
		 * @param data
		 */
		doSearch: function (key, data) {
			this._searchInfo = {key, data};
			if (!key.length) {
				let allData = this.treeTable.getAllData();
				util.forEachTreeData(allData, item => item.isShow = true);
				// this.treeTable.refresh();
				this.onlyShowSelectedRow();
				this.options.doSearchAfterHook && this.options.doSearchAfterHook({data: this.treeData});
				return;
			}
			this.treeTable.table.options.search.highFieldName = data.value;
			this.treeTable.doStaticData(this.treeTable.getAllData()); // 重新赋值，触发render，筛选关键词高亮
			this.onlyShowSelectedRow(null, true);
			this.options.doSearchAfterHook && this.options.doSearchAfterHook({data: this.treeData});

		},

		addPriceBookBox: function () {
			let _this = this;
			if (!this.isShowPriceBook()) return;

			this.treeTable.$el.find('.dt-sc-box').after('<div class="selectbom-pricebook-box"><span class="selectbom-pricebook-name">' + $t('产品包价目表') + '</span><div class="selectbom-pricebook-content"></div></div>');
			CRM.util.showLoading_tip('', null, 'bom-pricebook-loading');
			const {accountId, partnerId, rootId, masterData = {}, details = {}} = _this.options;
			util.getPriceBookProductInfo({
				accountId,
				partnerId,
				productIdList: [rootId],
				productInfos: [
					{
						productId: rootId,
						amount: '1',
					}
				],
				object_data: masterData,
				details: CRM.util.parsePriceBookDataRangeDetails(
					_this.options.details,
					true,
					null,
					{field: 'product_id', value: rootId}
				),

			}).then(res => {
				let val = res.priceBookProductDataList;
				_this.parseDataToSelect(val);
				_this.setNewPriceBookProData(val);
				CRM.util.hideLoading_tip('', 'bom-pricebook-loading');
				let def = _this.newPriceBookProductData ? _this.newPriceBookProductData.pricebook_id : '';
				_this.initFXSelect(val, def);
			});
		},

		parseDataToSelect: function (data) {
			data.forEach(item => {
				item.label = item.pricebook_id__r;
				item.value = item.pricebook_id;
			})
		},

		setNewPriceBookProData: function (list) {
			let _this = this;
			if (!_this.newPriceBookProductData && _this.options.priceBookId) {
				_this.newPriceBookProductData = _.find(list, item => {
					return item.pricebook_id === _this.options.priceBookId;
				})
			}
		},

		// 加载产品包价目表组件
		initFXSelect(selectOpt) {
			var _this = this;
			if (!_this.treeTable) return;
			this.fxSelect = FxUI.create({
				wrapper: _this.treeTable.$el.find('.selectbom-pricebook-content')[0],
				template: ` <fx-select
								ref="el1"
								v-model="value"
								:el-style="selectStyle"
								:options="options"
								:before-change="beforeChange"
								@change="change"
								@focus="focus"
								:disabled="disabled"
							  ></fx-select>`,
				data() {
					return {
						value: _this.newPriceBookProductData ? _this.newPriceBookProductData.pricebook_id : '',
						selectStyle: {
							width: '200px',
							height: '30px'
						},
						options: selectOpt,
						disabled: (
							(_this.options.disabledFields && _this.options.disabledFields.includes('price_book_id')) ||
							_this._priceFlowPricebookChildNotSelected ||
							_this._priceBookPriority ||
							_this.options.apinameFrom === "PriceBookProductObj"
						)
					}
				},
				methods: {
					beforeChange(val, oldVal) {
						console.log('beforeChange...', val, oldVal)
					},
					focus(event) {
						console.log('focus:', event);
					},
					change(val) {
						let data = this.options.find(item => item.value == val);
						_this.afterPriceBookChange(data, this)
					},
				}
			});
			this.treeTable.resize();
		},

		// 主子价目表是否需要同步；
		isNeedSyncChildrenPriceBook: function () {
			return CRM._cache.bom_adaptation_price_list_rules == '0';
		},

		// 更换产品包价目表;
		afterPriceBookChange: function (data, fxs) {
			if(!this._hasAccountId()) return;
			let _this = this;
			var confirm = util.confirm($t("更换价目表将重新计算总金额"), $t("确认"), function () {
				confirm.destroy();
				_this.rootDataExecutePriceService(data);
			}, {
				stopPropagation: true,
				hideFn: function () {
					fxs.value = _this.newPriceBookProductData.pricebook_id;
				}
			});
		},

		rootDataExecutePriceService(data) {
			this.getRootProductFromService(data).then((allRes) => {
				let result = _.find(allRes, item => item.product_id === this.options.rootId);
				this.newPriceBookProductData = result;
				this.options.originalTotalMoney = result.hasOwnProperty('selling_price') ? result.selling_price : result.pricebook_sellingprice;
				this.originalTotalSingleSetMoney = util.multiplicational(this.options.originalTotalMoney, this.options.root_pricing_period);
				this._updateChildrenData(allRes);
				this.rootDataExecutePriceServiceHook();
				this.saveDefSelectRow(this.treeData);
				this.calDefSelectMoney(this.treeData);
				this.updateExtendDataPrice(this.needUpdateParentRow);
				this.updateDefTotalMoney();
				this.calculateTotalMoney();
			})
		},

		rootDataExecutePriceServiceHook: $.noop,

		//获取取价接口返回的产品包数据
		getRootProductFromService: function (data) {
			let _this = this;
			let options = this.options;
			return new Promise((resolve, reject) => {
				if (data && (!CRM._cache.openAttribute && !_this.isNeedSyncChildrenPriceBook())) {
					resolve([data]);
					return;
				}
				if(!this._hasAccountId()) return resolve([data]);
				let rootPriceBookId = data && data.pricebook_id || options.priceBookId;
				let fullProductList = {
					"productId": options.rootId,
					"priceBookId": rootPriceBookId,
				};
				if (CRM._cache.openAttribute || _this.isNeedSyncChildrenPriceBook()) {
					fullProductList = Object.assign(fullProductList, {
						"priceBookId": rootPriceBookId,
						"attrMap": options.attribute
					});
				}
				fullProductList = [fullProductList];
				fullProductList = _this._addChildrenData(fullProductList, rootPriceBookId);
				CRM.util.showLoading_tip();
				CRM.util.replacePriceForPriceBook({
					accountId: options.accountId,
					partnerId: options.partnerId || '',
					mcCurrency: options.mcCurrency || '',
					fullProductList: fullProductList,
					object_data: _this.options.masterData || {},
					details: _this.options.details,
					object_api_name: this.options.masterData?.object_describe_api_name,
				}).then(obj => {
					resolve(obj.newRst);
					CRM.util.hideLoading_tip();
				})
			})
		},

		// 补上子产品数据；切换根结点价目表时，子产品也需要重新取价
		_addChildrenData: function (data, rootPriceBookId) {
			if (this.isNeedSyncChildrenPriceBook() && this.treeData) {
				data = data.concat(this.parseProductAttrList(this.treeData, rootPriceBookId))
			}
			return data;
		},

		//表格单元格编辑之前的回调
		//不执行next则不会触发默认的编辑
		beforeEditTableCellHandle: function (opts, table, next) {
			//opts.column.type === "object_reference" &&
			if (opts.column.data === 'price_book_id') {
				//lookup型的组件不用表格底层的选对象逻辑
				this._pickSelfObjForPriceBook(opts, table);
				return;
			}
			// 切换 bom 版本
			if (opts.column.data === 'node_bom_core_version') {
				this._pickSelfObjForBomVersion(opts, table);
				return;
			}

			next();
		},

		/**
		 * @desc 切换子产品价目表
		 */
		_pickSelfObjForPriceBook: function (opts, table) {
			var me = this;
			require.async('crm-modules/components/pickselfobject_pricebook/pickselfobject_pricebook', function (PickSelf) {
				me._pickobject = new PickSelf();
				me._pickobject.render({
					apiname: 'PriceBookProductObj',
					master_data: {
						account_id: me.options.accountId,
						partner_id: me.options.partnerId || '',
					},
					object_data: {
						product_id: opts.data.product_id
					},
					dataId: opts.data.price_book_product_id || '',
					masterData: me.options.masterData || {},
					source_api_name: me.options.apinameFrom,
					details: me.options.details,
					priceBookDesc: true,
					// object_data: me.opts.object_data,
					// relatedname: me.opts.target_related_list_name,
					// wheres: me.opts.wheres,
					// disabledcfg: me.opts.disabledcfg || null
				});
				me._pickobject.on('destroy', function () {

				});
				me._pickobject.on('select', function (obj) {
					me.afterSelectPriceBook(obj, opts.data, table);
				});
			})
		},

		// 更换子节点的价目表，需要重新计算；
		afterSelectPriceBook: function (data = {}, rowData = {}, table) {
			let obj = {
				price_book_id: data.pricebook_id,
				price_book_id__r: data.pricebook_id__r,
				modified_adjust_price: data.pricebook_price,
				price_book_product_id: data._id,
				price_book_product_id__r: data.name,
				adjust_price: data.pricebook_price,
				__adjust_price: data.pricebook_price,
			};
			obj.price_per_set = util.multiplicational(obj.modified_adjust_price, data.pricing_period);

			table.setCellsValueByRowId(obj, rowData.rowId, rowData);

			//如果有属性，要重新计算属性价格
			if (rowData.attribute) {
				this.attrValueChange(rowData)
			} else {
				//原逻辑
				if (this.isDefSelect(rowData)) {
					this.saveDefSelectRowBefore(rowData);
					this.saveDefSelectRow(this.treeData);
				}
				if (rowData.isChecked) {
					this.upDateEveryParentPrice(rowData.rowId);
					this.calculateTotalMoney()
				}
			}
		},

		saveDefSelectRowBefore: $.noop,

		/**
		 * @desc 切换复用 bom 版本
		 */
		_pickSelfObjForBomVersion: function (opts, table) {
			var me = this;
			let rowData = opts.data;
			require.async('crm-modules/components/pickselfobject/pickselfobject', function (PickSelf) {
				me._pickobject = new PickSelf();
				me._pickobject.render({
					apiname: 'BomCoreObj',
					master_data: {
						account_id: me.options.accountId,
						partner_id: me.options.partnerId || '',
					},
					object_data: {
						product_id: opts.data.product_id
					},
					dataId: rowData.related_core_id || '',
					masterData: me.options.masterData || {},
					source_api_name: me.options.apinameFrom,
					details: me.options.details,

					//过滤到已添加的任务依赖数据
					beforeRequest: function (rq) {
						let info = JSON.parse(rq.search_query_info);
						let filters = info.filters || [];
						filters.push({
							field_name: "product_id",
							field_values: [rowData.product_id],
							operator: "EQ",
						});

						// 切换复用 bom 版本时，售卖方式需要和母件一致
						if(me.rootData.sale_strategy){
							filters.push({
								field_name: "sale_strategy",
								field_values: [me.rootData.sale_strategy],
								operator: "EQ",
							});
						}

						rq.search_query_info = JSON.stringify(info);
						return rq;
					},
					// priceBookDesc: true,
					// object_data: me.opts.object_data,
					// relatedname: me.opts.target_related_list_name,
					// wheres: me.opts.wheres,
					// disabledcfg: me.opts.disabledcfg || null
				});
				me._pickobject.on('destroy', function () {

				});
				me._pickobject.on('select', function (obj) {
					me._changeBomVersion(obj, opts.data, table);
				});
			})
		},

		// 切换 bom 版本
		async _changeBomVersion(obj, rowData, table) {

			let related_core_id = obj._id;
			let o = {
				node_bom_core_version: obj.name,
				related_core_id,
			};
			// 更新 bom 版本字段
			table.setCellsValueByRowId(o, rowData.rowId, rowData);
			// 清掉默认选中项中的子件
			this._deleteCacheDefault(rowData.children);
			// 清掉当前子件数据
			rowData.children = [];
			// 清掉对应属性约束
			this.deleteAttributeRule(rowData.new_bom_path);
			// 请求最新子件数据
			let res = await this._fetchBomChildren(
				rowData.product_id,
				{
					bom_core_id: related_core_id, // 或_params.bom_core_id
					price_book_id: rowData.price_book_id || '',
					bom_list: [],
					new_bom_id: rowData.bom_id,
					new_bom_path: rowData.new_bom_path,
				}, true);
			let newChildren = util.flatBomData(res.dataMapList);
			newChildren = this._parseChildrenBomData(res.dataMapList, newChildren, rowData);

			table.addChildrenData(newChildren, rowData.rowId, false, false, true);

			this.calLastLevelParents(newChildren);
			this.upDateEveryParentPrice(rowData.rowId);
			this.calculateTotalMoney();

			setTimeout(() => {
				if (CRM._cache.openAttribute || CRM._cache.openNsAttribute) {
					this.displayAttribute(newChildren);
					// this._fromRender = true;
					this.executeBomAttrConstraint();
				}
			},200);
		},

		_parseAttrForRange(data){
			if(data.attribute){
				data.attribute.forEach(c => {
					c.isStandardAttribute = true;
					c.children = c.attribute_values;
				});
			}
			return [...data.attribute || [], ...data.nonstandardAttribute || []];
		},

		// 缓存属性展示
		_cacheAllAttribute(){
			let rootAttrList = this._parseAttrForRange(this.rootData);

			if(this.rootData.attr_range){
				this.rootData.attr_range = this.rootData.attr_range.filter(item => {
					item.isStandardAttribute = item.apiName === 'AttributeObj';
					return true;
				});
				this._cacheAttrRange = this.rootData.attr_range;
			}else{
				this._cacheAttrRange = rootAttrList;
			}
		},

		//
		_parseChildrenBomData: function (data, res, rootData) {
			var _this = this;

			var productData = _.find(data, function (item) {
				return item.describeApiName === 'ProductObj'
			});

			var PriceBookProductObj = _.find(data, function (item) {
				return item.describeApiName === 'PriceBookProductObj'
			});

			let proInfo = _.find(productData.dataList, item => {
				return item._id === _this.options.rootId;
			});

			// 缓存属性约束条件
			let attrConstraintInfo = _.find(data, function (item) {
				return item.describeApiName === 'BomAttributeConstraintLinesObj'
			});

			let attrRule = attrConstraintInfo ? attrConstraintInfo.dataList : [];
			if (attrRule.length) {
				this._attrConstraintRule = this._attrConstraintRule.concat(attrRule);
			}
			console.log('约束规则================================');
			console.log(this._attrConstraintRule);
			this.initBomData(res);
			this.removeInvalidData(res);

			this.addAttrConstraintMark(res, attrRule);
			if (CRM._cache.openAttribute) util.setDefaultAttr(res);

			res = util.parseDataToBOM(res, rootData.bom_id);
			res = util.sortTreeData(res, 'order_field');
			this.setCheckedForQuoter(res);
			if (rootData.isChecked) util.setBomChecked(res);

			// 保存默认选中项和默认选中总金额；
			// this.calDefSelectMoney(res);
			this.saveDefSelectRow(res);

			return res;
		},

		// 删除缓存的默认选中数据
		_deleteCacheDefault(children) {
			let del = [];
			util.forEachTreeData(children, item => {
				if (!item.isGroup) {
					del.push(item.rowId);
				}
			});
			this.defSelectRow = this.defSelectRow.filter(c => !del.includes(c.rowId));
			this._cacheSelectedByQuote = this._cacheSelectedByQuote.filter(c => !del.includes(c.rowId));
		},

		// 删除属性约束
		deleteAttributeRule(newBomPath) {
			this._attrConstraintRule = this._attrConstraintRule.filter(c => c.current_root_new_path !== newBomPath);
		},

		updateAttrPriceBook(curData, realData){
			curData.attribute_price_book_id = realData.attribute_price_book_id || "";
			curData.attribute_price_book_id__r = realData.attribute_price_book_id__r || "";
			curData.attribute_price_book_lines_ids = realData.attribute_price_book_lines_ids || "";
		},

		// 更新取价服务结果
		updateDataForRealPrice(data = [], newRst = []) {
			newRst.forEach(realData => {
				// let fd = data.find(d => d.product_id === realData.product_id && d.price_book_id === realData.pricebook_id);
				let fd = data.find(d => d.rowId == realData.rowId);
				if (!fd) return;
				if(fd._isRoot) return;
				let price = realData.selling_price,
					discount = realData.discount,
					realPrice = util.formatDecimalPlace(price * (discount / 100), this.options.decimal_places);
				//补充属性价目表数据
				this.updateAttrPriceBook(fd, realData);
				let changedPrice = {
					"adjust_price": realPrice,
					"modified_adjust_price": realPrice,
					"__adjust_price": realPrice,
					price_book_id: realData.pricebook_id,
					price_book_id__r: realData.pricebook_id__r,
					price_book_product_id: realData._id,
					price_book_product_id__r: realData.name,

					"price_per_set": util.multiplicational(realPrice, fd.pricing_period)
				};
				this.treeTable.setCellsValueByRowId(changedPrice, fd.rowId, fd);
				if (this.isDefSelect(fd)) {
					this.saveDefSelectRowBefore(fd);
					this.saveDefSelectRow(this.treeData);
				}
				if (fd.isChecked) {
					this.upDateEveryParentPrice(fd.rowId);
				}
			});
		},

		_getRealPrice(fullData = []) {
			return new Promise(resolve => {
				if (!fullData.length) return resolve([]);
				// let el = this.$el.find('.dialog_selectbom_right');
				util.showLoading_tip();
				CRM.util.replacePriceForPriceBook({
					accountId: this.options.accountId,
					partnerId: this.options.partnerId,
					fullProductList: fullData,
					object_data: this.options.masterData || {},
					details: this.options.details,
					object_api_name: this.options.masterData?.object_describe_api_name,
				}).then(res => {
					util.hideLoading_tip();
					if (res.applicablePriceSystem && res.newRst && res.newRst.length >= 1) {
						resolve(res.newRst)
					}
				}, err => {
					util.hideLoading_tip();
				});
			})
		},

		// 批量走取价服务
		async getRealPriceForBom(data = []) {
			if (!data || !data.length) return;
			if(!this._hasAccountId()) return;
			let fullData = this.parseNormalDataForPriceService(data);
			if (!fullData.length) return;
			let newRst = await this._getRealPrice(fullData);
			this.updateDataForRealPrice(data, newRst);
			return newRst;
		},

		attrValueChangeHook: $.noop,

		/*
		 * 属性产品相。切换属性
		 * 单选属性值,取价服务计算自产品属性价格
		 */
		async attrValueChange(data = {}) {
			const me = this;
			if (!data.selectedAttr) return;
			if(!this._hasAccountId()) return;
			let isTempNode = this.isTempNode(data);
			if (!isTempNode) {
				let r = await this.executeBomAttrConstraint();
				if (!r) return;
			}
			await this.attrValueChangeHook(data);
			if (data.price_mode == "1") {
				me.calculateFormulaRow(data, 'attribute');
				return
			};
			util.showLoading_tip();
			CRM.util.replacePriceForPriceBook({
				accountId: this.options.accountId,
				partnerId: this.options.partnerId,
				fullProductList: me.parseProductAttrList(data),
				object_data: this.options.masterData || {},
				details: this.options.details,
				object_api_name: this.options.masterData?.object_describe_api_name,
			}).then(res => {
				util.hideLoading_tip();
				if (res.applicablePriceSystem && res.newRst && res.newRst.length >= 1) {
					let realData = res.newRst[0],
						price = realData.selling_price,
						discount = realData.discount,
						realPrice = util.formatDecimalPlace(price * (discount / 100), me.options.decimal_places);

					//补充属性价目表数据
					me.updateAttrPriceBook(data, realData);
					me.changePriceByAttr(data, realPrice);
					me.calculateFormulaRow(data, 'attribute');
				}
			}, err => {
				util.hideLoading_tip();
			});
		},

		// 获取比自己低一层级的数据，分组不算层级
		_getLowerData(data = {}) {
			if (data.children && data.children.length) {
				if (data.children[0].isGroup) {
					if (data.children[0].children && data.children[0].children.length) {
						return data.children[0].children[0]
					} else {
						return data;
					}
				} else {
					return data.children[0];
				}
			} else {
				return data;
			}
		},

		//回填属性产品取价服务的价格，并修改相应父级价格&总计
		changePriceByAttr(data = {}, price = '') {
			let changedPrice = {
				"adjust_price": price,
				"modified_adjust_price": price,
				"__adjust_price": price,
				"price_per_set": util.multiplicational(price, data.pricing_period)
			};

			this.treeTable.setCellsValueByRowId(changedPrice, data.rowId, data);
			if (this.isDefSelect(data)) {
				this.saveDefSelectRowBefore(data);
				this.saveDefSelectRow(this.treeData);
			}
			if (data.isChecked) {
				let lowerData = this._getLowerData(data);
				this.upDateEveryParentPrice(lowerData.pid);
				this.calculateTotalMoney()
			}
		},

		// 是否是默认选中行
		isDefSelect(data = {}) {
			return data.is_required || (data.selected_by_default);
		},

		//格式化取价服务需要的产品-属性属性值列表参数
		parseProductAttrList: function (data, rootPriceBookId = '') {
			let newData = _.isArray(data) ? data : [data];
			let res = [];
			util.forEachTreeData(newData, item => {
				let r = this._parseDataForPriceService(item, rootPriceBookId);
				if (r) res.push(r);
			});
			return res;
		},

		// 给取价服务测试
		_parseDataForPriceService(item, rootPriceBookId) {
			if (!item.isGroup && (item.price_book_id || item.pricebook_id || this.isTempNode(item))) {
				let pbId = item.price_book_id || item.pricebook_id;
				let attrs = {};
				if (item.old_attribute) {
					item.old_attribute.forEach(c => {
						let v = c.attribute_values.find(a => a.is_default === '1');
						attrs[c.id] = v.id;
					});
					delete item.old_attribute;
				} else if (item.selectedAttr) {
					for (let key in item.selectedAttr) {
						attrs[key] = item.selectedAttr[key].value_ids[0].id
					}
				}
				return {
					"productId": item.product_id,
					"priceBookId": rootPriceBookId || pbId || '',
					"attrMap": attrs,
					"rowId": item.rowId
				}
			}
		},

		parseNormalDataForPriceService(data) {
			let newData = _.isArray(data) ? data : [data];
			let res = [];
			let rootPriceBookId = this.newPriceBookProductData ? this.newPriceBookProductData.pricebook_id : this.options.priceBookId || '';
			let pb = this.isNeedSyncChildrenPriceBook() ? rootPriceBookId : '';
			newData.forEach(item => {
				let r = this._parseDataForPriceService(item, pb);
				if (r) res.push(r);
			});
			return res;
		},

		_getTableData() {
			return this.treeTable.getCurData()
		},

		_getUpdateFields(item, child) {
			let obj = {
				price_book_id: item.pricebook_id,
				price_book_id__r: item.pricebook_id__r,
				modified_adjust_price: item.pricebook_price,
				price_book_product_id: item._id,
				price_book_product_id__r: item.name,
				adjust_price: item.pricebook_price,
				__adjust_price: item.pricebook_price,
			};
			if (child.selectedAttr) {
				let realPrice = util.multiplicational(item.selling_price, (item.discount / 100));
				realPrice = util.formatDecimalPlace(realPrice, this.options.decimal_places);
				obj.adjust_price = realPrice;
				obj.__adjust_price = realPrice;
				obj.modified_adjust_price = realPrice;
				this.updateAttrPriceBook(obj, item);

			}
			obj.price_per_set = util.multiplicational(obj.modified_adjust_price, child.pricing_period);
			return obj;
		},

		// 更新子产品的价目表相关数据
		_updateChildrenData: function (allRes) {
			let _this = this;
			this.needUpdateParentRow = [];
			if (allRes && allRes.length > 1) {
				let tableData = this._getTableData();
				util.forEachTreeData(tableData, function (child) {
					if (child.isGroup) return;
					let item = allRes.find(c => c.product_id === child.product_id); 			// 可能有重复的产品
					if(!item) return;
					let obj = _this._getUpdateFields(item, child);
					_this.treeTable.setCellsValueByRowId(obj, child.rowId, child);
					_this.needUpdateParentRow.push(child.rowId);
				});
				_this._updateChildrenDataHook_after(allRes);
			}
		},

		_updateChildrenDataHook_after: $.noop,

		// 添加收起子节点按钮
		addCloseBomBtn: function () {
			this.treeTable.$el.find('.dt-caption').append('<div class="selectbom_btn_box"><span class="title">' + $t('默认收起子级') + '</span><span class="selectbom_closeSwitch"></span></div>');
			this.renderSwitch();
		},

		// 渲染开关组件；
		renderSwitch: function () {
			let _this = this;
			let isCloseBom = CRM.getLocal('isCloseBom');
			this.openTreeSwitch && this.openTreeSwitch.destroy && this.openTreeSwitch.destroy();
			this.openTreeSwitch = FxUI.create({
				wrapper: '.selectbom_closeSwitch',
				template: `<fx-switch
								  v-model="value"
								  size="mini"
								  @change="change"
							  >
						</fx-switch>`,
				data() {
					return {
						value: isCloseBom
					}
				},
				methods: {
					change(data, node, tree) {
						CRM.setLocal('isCloseBom', data);
						_this.afterSwitchChange(data);
					},
				}
			})
		},

		// 切换展开收起
		afterSwitchChange: function (status) {
			if(status){ // 收起
				this.onlyShowSelectedRow(null, false);
				let allData = this.treeTable.getAllData();
				_.forEach(allData, item => {
					if (item.children) {
						util.forEachTreeData(item.children, c => c.isShow = false);
					}
				});
				this.treeTable.refresh();
			}else{ // 展开
				this.treeTable.setAllChildrenIsShow(true);
				this.onlyShowSelectedRow();
			}
			this.displayAttribute2(true);
			this.options.doCloseBomAfterHook && this.options.doCloseBomAfterHook({data: this.treeTable.getAllData(), api: this._getApi(), status: status});
		},

		// 根据开关，设置子节点是否展示；
		setDataIsShow: function (data, refresh) {
			let isCloseBom = CRM.getLocal('isCloseBom');
			util.forEachTreeData(data, function (item) {
				if (item.pid) {
					item.isShow = !isCloseBom;
				}
			});
			if(refresh) this.refreshRowIsShow(data);
		},

		// 必选或默认选中项是否计入默认产品包总金额；包含默认选中，返回true
		isIncludeDefSelect: function () {
			return CRM._cache.bom_price_calculation_configuration == '0';
		},

		// 初始化默认选中价格
		initDefSelectRowPrice() {
			let _this = this;
			if (!this.isIncludeDefSelect()) {
				let nd = _.uniq(_this.defSelectRow, item => item.pid);
				_.each(nd, rowData => {
					if (rowData.pid) _this.upDateEveryParentPrice(rowData.pid);
				})
			}
			// 因为报价器选中的子件需要重新计算父级价格
			if (this._cacheSelectedByQuote) {
				let nd = _.uniq(_this._cacheSelectedByQuote, item => item.pid);
				_.each(nd, rowData => {
					if (rowData.pid) _this.upDateEveryParentPrice(rowData.pid);
				})
			}
		},

		// 复选框点击之前，拷贝一份数据，约束关系用
		checkboxClickBefore() {
			this.updateCloneData();
		},

		// todo: 优化下，只拷贝部分字段；
		updateCloneData() {
			if (!this._attrConstraintRule.length) return;
			let allData = this.treeTable.getAllData();
			this.cloneData = util.cloneObjArr(allData); // 拷贝一份数据，如果执行约束关系不成功，则重置数据，勾选操作不生效；
		},

		// 有历史值替换
		hasHistoryData() {
			return this.options.extendData && this.options.extendData.length;
		},

		hideSearch() {
			this.$el.find('.j-dt-sc-box').hide();
			this.$el.find('.crm-w-panel').hide();
		},

		showSearch() {
			this.$el.find('.j-dt-sc-box').show();
		},

		// 滚动时隐藏按钮
		scrollingAfter() {
			setTimeout(() => {
				this.hideBtns();
			}, 10);
		},

		_updateTableValue(){
			this.treeTable.setCellsValueByRowId(...arguments)
		},

		// 过滤数据 rowIds: 要显示的行 rowId
		_filterData(rowIds = []) {
			if(!rowIds) return;
			rowIds = Array.isArray(rowIds) ? rowIds: [rowIds];
			// 根据筛选结果，过滤要展示的行；
			dataObj = this.treeTable.getDataToObj();
			// 设置父级显示
			function _setPDShow(pid) {
				let pd = dataObj[pid];
				if(pd){
					pd.isShow = true;
					if(pd.pid) _setPDShow(pd.pid);
				}
			}
			util.forEachTreeData(this.treeData, item => {
				item.isShow = rowIds.includes(item.rowId);
				item.__filterByIndividual = !item.isShow;
				if(item.isShow && item.pid) _setPDShow(item.pid);
			});
			this.treeTable.refresh(null, 'refresh', true);
		},

		_refreshTable(data){
			this.treeTable.refresh(data, 'refresh', true);
		},

		// 复用 bom 子件的数量 = 单个复用 bom 下的数量 * 复用 bom 的数量；
		_calRealAmount(amount, parentAmount){
			let dp = this._getDescribe('amount')?.decimal_places;
			return util.toFixed(util.multiplicational(amount, parentAmount), dp);
		},

		// 是否有客户 id
		_hasAccountId(){
		  	return this.options.accountId;
		},

		_getApi(){
		 	return {
				filterData: this._filterData.bind(this),
				refreshTable: this._refreshTable.bind(this),
				setNsAttributeValue: this.setNsAttributeValue.bind(this),
				setRootNsAttributeValue: this.setRootNsAttributeValue.bind(this),
				filterDataBySearchKey: this.filterDataBySearchKey.bind(this),
				onlyShowSelectedRow: this.onlyShowSelectedRow.bind(this),
				calculateFormulaRow: this.calculateFormulaRow.bind(this),

				getAllCheckedData: this.getTableCheckedData.bind(this),
				setRowChecked: this._setRowChecked.bind(this),
				getRootInfo: this._getRootInfo.bind(this),
				getAllData: this._getAllData(),

				getMasterData: this._getMasterData.bind(this),

			}
		},

		_getMasterData(){
			return this.options?.masterData;
		},

		// api 获取所有子件数据
		_getAllData(){
			return this.treeTable?.getAllData() || [];
		},

		// api 设置行选中或取消选中
		_setRowChecked(rowId, status){
			return this.treeTable.setRowCheckedByRowId(rowId, status,);
		},

		// api 获取根节点信息
		_getRootInfo(){
			return {
				core_id: this.options.bom_core_id,
				rootData: this.rootData, // 可能是价目表明细，可能是产品数据
			}
		},

		/**
		 * @desc 设置根节点非标属性
		 * @param rowData
		 * @param attrInfo : {nsAttrId: val}
		 */
		setRootNsAttributeValue(rowData, attrInfo){
			_.each(attrInfo, (val, key) => {
				this.attributeComp?.setNsAttrVal(rowData, {
					attrId: key,  	//非标属性Id,
					value: val 		//属性值-数字
				}, true)
			})
		},

		/**
		 * @desc 设置某行非标属性值
		 * @param rowId
		 * @param attrInfo {
					"65954eb112e1da0006468c12": {
						"value": "2"
					}
				}
		 */
		setNsAttributeValue(rowId, attrInfo){
			let rowData = this.treeTable.getRowDataById(rowId);
			if(!rowData) return util.alert($t('crm.dialog_selectbom.not_found_child', null, '未找到子件'));
			rowData.nsAttr = rowData.nsAttr || {};
			if (!Object.keys(attrInfo).length) {
				return;
			}
			let res = [];
			_.each(attrInfo, (val, attrId) => {
				if (!rowData.nonstandardAttribute) return;
				let f = rowData.nonstandardAttribute.find(a => a.id === attrId);
				if (!f) return;
				rowData.nsAttr[attrId].value = val.value;
				res.push(attrId);
				let tr = this.treeTable.$el.find('.show-attr[data-sourcerowid='+ rowId +']');
				if(!tr.length) return;
				let li = tr.find('.ns-item[data-id='+ attrId +']');
				if(!li.length) return;
				let $item = li.find('.j-nsAttr-input');
				if(!$item.length) return;
				$item.val(val.value);
			});
			return res;
		},

		// 切换版本
		async changeVersion(){
			let newCoreData = await this.renderBomCoreObj();
			this.options.bom_core_id = newCoreData._id;
			this.options.bom_version = newCoreData.core_version;
			this.options.bom_type = newCoreData.category;
			this.options.bom_core_name = newCoreData.name;
			this._newCoreData = newCoreData;
			// this._replaceRootSelectedAttr();
			this.initData();
		},

		// 切版本，保留已选属性和非标属性
		_replaceRootSelectedAttr(){
			if(this.options.extendRootData){
				if(this.rootData.nsAttr) this.options.extendRootData.nsAttr = this.rootData.nsAttr;
				if(this.rootData.selectedAttr) this.options.extendRootData.selectedAttr = this.rootData.selectedAttr;
			}
		},

		// 选版本
		renderBomCoreObj() {
			let me = this;
			let pId = this.rootData.product_id || this.rootData._id;
			return new Promise(resolve => {
				CRM.api.pick_data({
					hideAdd: true,
					apiName: 'BomCoreObj',
					single: true,
					isCheckinSimpleOrder: true,
					data: me.rootData.core_id,
					filters: [{
						field_name: 'product_id',
						field_values: [pId],
						operator: 'EQ'
					}],
					methods: {
						select(res) {
							console.log(res);
							resolve(res.selected);
						}
					},
				}, )
			});
		},

		...subset,

		//==================================================================================

		destroy() {
			util.closeError_fx();
			$('body').undelegate('.showConstraintRule', 'click');
			// $('body').undelegate('.j-addTemporaryChildren', 'hover');
			$('body').undelegate('.trInsertHandle', 'click');
			$('body').undelegate('.addChildrenHandle', 'click');
			$('body').undelegate('.aplCalculateHandle', 'click');
			$('body').undelegate('.rootAttrBoxForQuoter_bottomLine', 'click');
			$('body').undelegate('.dialog_selectbom_versionBox', 'click');
			// $('body').undelegate('.j-addTemporaryChildren', 'mouseleave');
			this.dialog && this.dialog.destroy();
			this.treeTable && this.treeTable.destroy();
			this.fxSelect && this.fxSelect.destroy();
			this.openTreeSwitch && this.openTreeSwitch.destroy();
			this.attributeComp && this.attributeComp.destroy();
			this.bomAplBtn && this.bomAplBtn.$destroy();
			this.PickSelf && this.PickSelf.destroy();
			this.dragContainer && this.dragContainer.destroy();
			this.SelectAllBtn && this.SelectAllBtn.destroy();
			this.AttrRange && this.AttrRange.$destroy();
			this.PriceQuoter && this.PriceQuoter.$destroy();
			this.ChildrenAttrRange && this.ChildrenAttrRange.$destroy();
			this.treeTable = this.treeData = this.columns = this.fxSelect = this.openTreeSwitch = this.cloneData = this.currentRule = this.attributeComp = this.PickSelf = this._dd = null;
			this.off();
			$('.showConstraintRule').remove();
			$('.selectbom-totalMoney').remove();
			this.options.rootAttrElement && this.options.rootAttrElement.empty();
			this.$el.children().remove();
		}

	})


})
